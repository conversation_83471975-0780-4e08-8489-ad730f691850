<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.cosfo</groupId>
  <artifactId>cosfo-manage</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>cosfo-manage</name>
  <description>cosfo-manage</description>

  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
    <qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
    <swagger.version>2.7.0</swagger.version>
    <gauva.version>28.2-jre</gauva.version>
    <commons-lang3>3.12.0</commons-lang3>
    <mysql.version>8.0.22</mysql.version>
    <jwt>0.9.1</jwt>
    <hutool.version>5.7.22</hutool.version>
    <commons-io.version>1.3.1</commons-io.version>
    <smart-doc.version>2.5.3-xm</smart-doc.version>
    <erp-client.version>1.1.9-RELEASE</erp-client.version>
    <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
    <rocket-mq.version>1.2.1</rocket-mq.version>
    <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
    <xianmu-dubbo.version>1.0.10-RELEASE</xianmu-dubbo.version>
    <authentication-sdk.version>1.1.15</authentication-sdk.version>
    <ordercenter.client.version>1.4.4-RELEASE</ordercenter.client.version>
    <item.client.version>1.0.53-RELEASE</item.client.version>
    <goodscenter-client.version>1.0.9.1-RELEASE</goodscenter-client.version>
    <xianmu-robot-util.version>1.0.2</xianmu-robot-util.version>
    <nacos-config.version>0.2.12</nacos-config.version>
    <usercenter-client.version>1.2.7-RELEASE</usercenter-client.version>
    <authentication-client.version>1.2.7-RELEASE</authentication-client.version>
    <cosfo-store-inventory.version>1.0.2-RELEASE</cosfo-store-inventory.version>
    <jindie-sdk.version>1.1-SNAPSHOT</jindie-sdk.version>
    <open-platform-client.version>1.2-RELEASE</open-platform-client.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>net.xianmu.starter</groupId>
      <artifactId>xianmu-mybatis-interceptor-support</artifactId>
      <version>1.0.2-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-launcher</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- auth 服务依赖-->
    <dependency>
      <groupId>net.xianmu</groupId>
      <artifactId>authentication-sdk</artifactId>
      <version>${authentication-sdk.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.elasticsearch</groupId>
          <artifactId>elasticsearch</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.rocketmq</groupId>
          <artifactId>rocketmq-spring-boot</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.elasticsearch.client</groupId>
          <artifactId>elasticsearch-rest-client</artifactId>
        </exclusion>

        <exclusion>
          <groupId>org.elasticsearch.client</groupId>
          <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </exclusion>
      <!-- org/mybatis/mybatis/3.5.0 -->
        <exclusion>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>net.xianmu</groupId>
      <artifactId>authentication-client</artifactId>
      <version>${authentication-client.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.baomidou</groupId>
          <artifactId>mybatis-plus-boot-starter</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--    用户中心-->
    <dependency>
      <groupId>net.xianmu</groupId>
      <artifactId>usercenter-client</artifactId>
      <version>${usercenter-client.version}</version>
    </dependency>

      <!--    营销中心-->
    <dependency>
        <groupId>net.xianmu</groupId>
        <artifactId>marketing-center-client</artifactId>
        <version>1.0.9-RELEASE</version>
    </dependency>

    <!-- shiro -->
    <dependency>
      <groupId>org.apache.shiro</groupId>
      <artifactId>shiro-spring</artifactId>
      <version>1.4.0</version>
    </dependency>
    <!-- auth 服务依赖-->

    <dependency>
      <groupId>org.icepear.echarts</groupId>
      <artifactId>echarts-java</artifactId>
      <version>1.0.6</version>
      <exclusions>
        <exclusion>
          <artifactId>slf4j-simple</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- redisson -->
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
      <version>3.16.4</version>
    </dependency>
    <dependency>
      <groupId>net.xianmu.starter</groupId>
      <artifactId>xianmu-task-support</artifactId>
      <version>1.0.5</version>
    </dependency>
    <dependency>
      <groupId>com.cosfo</groupId>
      <artifactId>erp-client</artifactId>
      <version>${erp-client.version}</version>
    </dependency>
    <dependency>
      <groupId>net.summerfarm</groupId>
      <artifactId>ofc-client</artifactId>
      <version>1.6.6-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>net.xianmu.starter</groupId>
      <artifactId>xianmu-rocketmq-support</artifactId>
      <version>${rocket-mq.version}</version>
    </dependency>
    <dependency>
      <groupId>com.cosfo</groupId>
      <artifactId>cosfo-manage-client</artifactId>
      <version>1.4.1-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>net.xianmu.common</groupId>
      <artifactId>xianmu-robot-util</artifactId>
      <version>${xianmu-robot-util.version}</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba.boot</groupId>
      <artifactId>nacos-config-spring-boot-starter</artifactId>
      <version>${nacos-config.version}</version>
    </dependency>
    <!-- dependencyManagement中配置版本号，在core工程中配置依赖 -->
    <dependency>
      <groupId>net.xianmu.starter</groupId>
      <artifactId>xianmu-dubbo-support</artifactId>
      <version>${xianmu-dubbo.version}</version>
    </dependency>
    <!--    <dependency>-->
    <!--      <groupId>org.apache.dubbo</groupId>-->
    <!--      <artifactId>dubbo-registry-nacos</artifactId>-->
    <!--      <version>2.7.15</version>-->
    <!--    </dependency>-->
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-registry-nacos</artifactId>
      <version>2.7.15</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba.boot</groupId>
      <artifactId>nacos-config-spring-boot-starter</artifactId>
      <version>${nacos-config.version}</version>
    </dependency>
    <!-- 鲜沐rpc服务-->
    <dependency>
      <groupId>net.manage.client</groupId>
      <artifactId>manage-client</artifactId>
      <version>1.0.53-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>net.summerfarm</groupId>
      <artifactId>summerfarm-pms-client</artifactId>
      <version>1.3.4-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>net.summerfarm.wms</groupId>
      <artifactId>summerfarm-wms-client</artifactId>
      <version>1.7.1-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>net.summerfarm</groupId>
      <artifactId>summerfarm-wnc-client</artifactId>
      <version>1.2.6-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>com.cosfo</groupId>
      <artifactId>order-center-client</artifactId>
      <version>${ordercenter.client.version}</version>
    </dependency>

    <dependency>
      <groupId>com.cosfo</groupId>
      <artifactId>cosfo-common</artifactId>
      <version>1.0.4</version>
    </dependency>

    <dependency>
      <groupId>net.summerfarm</groupId>
      <artifactId>sf-mall-manage-client</artifactId>
      <version>1.0.0-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>com.cosfo</groupId>
      <artifactId>cosfo-mall-client</artifactId>
      <version>1.0.6</version>
    </dependency>

      <!--  鲜沐下载中心 依赖jar    start-->
    <dependency>
          <groupId>net.summerfarm</groupId>
          <artifactId>common-client</artifactId>
          <version>1.0.17-RELEASE</version>
      </dependency>

      <dependency>
          <groupId>net.summerfarm</groupId>
          <artifactId>xianmu-download-support</artifactId>
          <version>1.0.8</version>
      </dependency>

      <dependency>
          <groupId>net.xianmu.starter</groupId>
          <artifactId>xianmu-oss-support</artifactId>
          <version>1.0.7</version>
      </dependency>

    <dependency>
      <groupId>net.xianmu.starter</groupId>
      <artifactId>xianmu-redis-support</artifactId>
      <version>1.2.4</version>
    </dependency>
      <!--  鲜沐下载中心 依赖jar    end-->

    <dependency>
      <groupId>com.cosfo.summerfarm</groupId>
      <artifactId>saas-to-summerfarm</artifactId>
      <version>1.6.13-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.cosfo</groupId>
      <artifactId>message-client</artifactId>
      <version>1.3.1-RELEASE</version>
    </dependency>
    <!--    核心依赖模块    -->
    <dependency>
      <groupId>net.xianmu.common</groupId>
      <artifactId>xianmu-common</artifactId>
      <!--      根据实际版本修改，线上禁止SNAPSHOT版本     -->
      <version>1.1.7-RELEASE</version>
      <exclusions>
        <exclusion>
          <artifactId>caffeine</artifactId>
          <groupId>com.github.ben-manes.caffeine</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- EasyExcel依赖包-->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>2.2.8</version>
    </dependency>

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>${hutool.version}</version>
    </dependency>

    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
      <version>2.7.0</version>
    </dependency>

      <dependency>
          <groupId>com.alibaba.arms.apm</groupId>
          <artifactId>arms-sdk</artifactId>
          <version>1.7.5</version>
      </dependency>


      <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${org.mapstruct.version}</version>
    </dependency>
<!--    &lt;!&ndash; Spring Security &ndash;&gt;-->
<!--    <dependency>-->
<!--      <groupId>org.springframework.boot</groupId>-->
<!--      <artifactId>spring-boot-starter-security</artifactId>-->
<!--    </dependency>-->
<!--    &lt;!&ndash; Spring Security和JWT整合 &ndash;&gt;-->
<!--    <dependency>-->
<!--      <groupId>org.springframework.security</groupId>-->
<!--      <artifactId>spring-security-jwt</artifactId>-->
<!--      <version>1.0.10.RELEASE</version>-->
<!--    </dependency>-->
<!--    &lt;!&ndash; JWT &ndash;&gt;-->
<!--    <dependency>-->
<!--      <groupId>io.jsonwebtoken</groupId>-->
<!--      <artifactId>jjwt</artifactId>-->
<!--      <version>${jwt}</version>-->
<!--    </dependency>-->
    <!-- 字符串转换需要用到此包 -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>${commons-lang3}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
      <exclusions>
        <exclusion>
          <groupId>io.lettuce</groupId>
          <artifactId>lettuce-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-quartz</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
    </dependency>
    <!-- mybatis-plus核心依赖-->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
      <version>3.5.1</version>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
      <version>3.5.1</version>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
      <version>2.2</version>
    </dependency>
    <!--逆向工程需要模板引擎-->
    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
      <version>2.3.28</version>
    </dependency>

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- https://mvnrepository.com/artifact/com.alibaba/druid-spring-boot-starter -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-pool2</artifactId>
      <version>2.3</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.13</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpmime</artifactId>
      <version>4.5.13</version>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.bind</groupId>
      <artifactId>jaxb-impl</artifactId>
      <version>2.1.12</version>
      <exclusions>
        <exclusion>
          <groupId>javax.xml.bind</groupId>
          <artifactId>jaxb-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--alibaba开源数据库连接池-->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
      <version>1.2.9</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <!--  emoji  -->
    <dependency>
      <groupId>com.vdurmont</groupId>
      <artifactId>emoji-java</artifactId>
      <version>4.0.0</version>
    </dependency>
    <!--解析xml工具xstream-->
    <dependency>
      <groupId>com.thoughtworks.xstream</groupId>
      <artifactId>xstream</artifactId>
      <version>1.4.4</version>
    </dependency>
    <!--   分页插件     -->
    <dependency>
      <groupId>com.github.pagehelper</groupId>
      <artifactId>pagehelper-spring-boot-starter</artifactId>
      <version>1.4.1</version>
      <exclusions>
        <exclusion>
          <artifactId>mybatis</artifactId>
          <groupId>org.mybatis</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- SchedulerX 分布式调度平台 -->
<!--    <dependency>-->
<!--      <groupId>com.aliyun.schedulerx</groupId>-->
<!--      <artifactId>schedulerx2-spring-boot-starter</artifactId>-->
<!--      <version>1.2.9.1</version>-->
<!--      &lt;!&ndash;如果用的是logback，需要把log4j和log4j2排除掉 &ndash;&gt;-->
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <groupId>org.apache.logging.log4j</groupId>-->
<!--          <artifactId>log4j-api</artifactId>-->
<!--        </exclusion>-->
<!--        <exclusion>-->
<!--          <groupId>org.apache.logging.log4j</groupId>-->
<!--          <artifactId>log4j-core</artifactId>-->
<!--        </exclusion>-->
<!--        <exclusion>-->
<!--          <groupId>log4j</groupId>-->
<!--          <artifactId>log4j</artifactId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
<!--    </dependency>-->
    <!-- rocket mq -->

    <!--  七牛上传SDK  -->
    <dependency>
      <groupId>com.qiniu</groupId>
      <artifactId>qiniu-java-sdk</artifactId>
      <version>${qiniu.version}</version>
    </dependency>

    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>${commons-io.version}</version>
    </dependency>

    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
      <version>1.4</version>
    </dependency>
    <dependency>
      <groupId>net.summerfarm</groupId>
      <artifactId>goods-center-client</artifactId>
      <version>${goodscenter-client.version}</version>
    </dependency>
    <dependency>
      <groupId>com.cosfo</groupId>
      <artifactId>item-center-client</artifactId>
      <version>${item.client.version}</version>
    </dependency>
    <dependency>
      <groupId>com.cosfo</groupId>
      <artifactId>jindie-sdk</artifactId>
      <version>${jindie-sdk.version}</version>
    </dependency>

    <dependency>
      <groupId>com.cosfo</groupId>
      <artifactId>cosfo-store-inventory</artifactId>
      <version>${cosfo-store-inventory.version}</version>
    </dependency>
    <dependency>
      <groupId>net.xianmu</groupId>
      <artifactId>scp-service-client</artifactId>
      <version>1.0.4</version>
    </dependency>

    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>1.4.200</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.sun.mail</groupId>
      <artifactId>javax.mail</artifactId>
      <version>1.6.2</version>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>30.1.1-jre</version>
    </dependency>

    <!-- Add explicit OkHttp dependency to resolve version conflict -->
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>3.14.9</version>
    </dependency>

    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter-test</artifactId>
      <version>2.0.1</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>net.xianmu.starter</groupId>
      <artifactId>xianmu-log-support</artifactId>
      <version>${xianmu-log.version}</version>
    </dependency>

    <!-- 开放平台 -->
    <dependency>
      <groupId>net.xianmu.open</groupId>
      <artifactId>open-platform-client</artifactId>
      <version>${open-platform-client.version}</version>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>1.18.16</version>
            </path>
            <!-- This is needed when using Lombok 1.18.16 and above -->
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>0.2.0</version>
            </path>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${org.mapstruct.version}</version>
            </path>
            <!-- other annotation processors -->
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.3.7.RELEASE</version>
        <configuration>
          <mainClass>com.cosfo.manage.CosfoManageApplication</mainClass>
        </configuration>
        <executions>
          <execution>
            <id>repackage</id>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>xianmu.common</groupId>
        <artifactId>xianmu-maven-plugin</artifactId>
        <version>1.1.0</version>
      </plugin>

      <plugin>
        <groupId>org.sonarsource.scanner.maven</groupId>
        <artifactId>sonar-maven-plugin</artifactId>
        <version>3.3.0.603</version>
      </plugin>

    </plugins>
  </build>

</project>
