package com.cosfo.manage.product.repository.impl;

import com.cosfo.manage.product.model.po.ProductStockForewarningReport;
import com.cosfo.manage.product.repository.ProductStockForewarningReportRepository;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Collections;

@SpringBootTest
class ProductStockForewarningReportRepositoryImplTest {

    @Resource
    ProductStockForewarningReportRepository productStockForewarningReportRepository;

    @Test
    void deleteByTenantIdAndSkuId() {
        productStockForewarningReportRepository.deleteByTenantIdAndSkuId(24514L, 27036L);
    }


    @Test
    void insertOrUpdateBatch() {
        ProductStockForewarningReport o1 = new ProductStockForewarningReport();
        o1.setTenantId(2L);
        o1.setSkuId(1L);
        o1.setSkuCode("12345");
        o1.setWarehouseNo(1);
        o1.setGoodsType(1);
        o1.setSaleDays(2);
        o1.setSaleQuantity(2);
        o1.setQuantity(0);
        o1.setForewarningStatus(2);

        productStockForewarningReportRepository.insertOrUpdateBatch(Collections.singletonList(o1));
    }
}