package com.cosfo.manage.bill.service.impl;

import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.common.context.BillTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class FinancialTenantBillServiceImplTest {

    @Resource
    private FinancialTenantBillServiceImpl serviceTest;

    @Test
    public void testChooseFileTemplate() {

        TenantBillQueryDTO test = new TenantBillQueryDTO();
        // test.setSupplierId(SupplierTenantEnum.XM.getId());
        test.setSupplierId(333L);
        test.setType(BillTypeEnum.DIRECT_ASSIGN_BILL.getCode());
       /* ExcelTypeEnum excelTypeEnum = serviceTest.chooseFileTemplate(test);

        log.info(excelTypeEnum.getName());*/

    }

}
