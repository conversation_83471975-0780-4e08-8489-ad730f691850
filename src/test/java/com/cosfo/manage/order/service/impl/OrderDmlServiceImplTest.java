package com.cosfo.manage.order.service.impl;

import com.cosfo.manage.common.util.binlog.impl.OrderAfterSaleDmlServiceImpl;
import com.cosfo.manage.common.util.binlog.impl.OrderDmlServiceImpl;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: fansongsong
 * @Date: 2023-10-20
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderDmlServiceImplTest {

    @Resource
    private OrderDmlServiceImpl orderDmlService;

    @Resource
    private OrderAfterSaleDmlServiceImpl orderAfterSaleDmlService;

    @Test
    public void supplierNotice() {
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put("id","93948");
        dataMap.put("status","10");
        Map<String, String> oldMap = Maps.newHashMap();
        oldMap.put("status","3");
        orderDmlService.supplierNotice(dataMap, oldMap);
    }

    @Test
    public void dealOrderAfterSaleData() {
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put("id","12662");
        dataMap.put("order_id","93870");
        dataMap.put("tenant_id","2");
        Map<String, String> oldMap = Maps.newHashMap();
        orderAfterSaleDmlService.dealOrderAfterSaleData(dataMap, oldMap);
    }

}
