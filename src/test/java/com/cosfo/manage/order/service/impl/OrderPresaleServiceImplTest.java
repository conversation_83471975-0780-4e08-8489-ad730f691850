package com.cosfo.manage.order.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.downloadcenter.presaleorder.PresaleOrderSetDeliveryDateImportHandler;
import com.cosfo.manage.order.model.dto.OrderPresaleBatchDeliveryTimeDTO;
import com.cosfo.manage.order.model.dto.OrderPresaleDeliveryTimeDTO;
import com.cosfo.manage.order.service.OrderPresaleService;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
class OrderPresaleServiceImplTest {

    @Resource
    private OrderPresaleService orderPresaleService;

    @Resource
    private PresaleOrderSetDeliveryDateImportHandler presaleOrderSetDeliveryDateImportHandler;

    @Test
    void setDeliveryTime() {
        OrderPresaleDeliveryTimeDTO orderPresaleDeliveryTimeDTO = new OrderPresaleDeliveryTimeDTO();
        orderPresaleDeliveryTimeDTO.setOrderId(116206L);
        orderPresaleDeliveryTimeDTO.setDeliveryTime("2024-07-16");

        orderPresaleService.setDeliveryTime(orderPresaleDeliveryTimeDTO);
    }

    @Test
    void batchSetDeliveryTime() {
        OrderPresaleBatchDeliveryTimeDTO orderPresaleDeliveryTimeDTO = new OrderPresaleBatchDeliveryTimeDTO();
        orderPresaleDeliveryTimeDTO.setStartTime("2024-07-01");
        orderPresaleDeliveryTimeDTO.setEndTime("2024-07-30");
        orderPresaleDeliveryTimeDTO.setItemIds(Lists.newArrayList(47249L));
        orderPresaleDeliveryTimeDTO.setDeliveryTime("2024-07-15");

        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(2L);

        Long resId = orderPresaleService.batchSetDeliveryTime(orderPresaleDeliveryTimeDTO, loginContextInfoDTO);
        System.err.println(resId);
    }

    @Test
    void handle() {
        DownloadCenterDataMsg downloadCenterDataMsg = new DownloadCenterDataMsg();
//        downloadCenterDataMsg.setAuthUser();
        downloadCenterDataMsg.setResId(19590L);
        downloadCenterDataMsg.setSource("saas");
        downloadCenterDataMsg.setBizType(80);
        downloadCenterDataMsg.setFileOperationType("import");
        downloadCenterDataMsg.setFileOssUrl("test-app-temp:back_file/cosfo-manage/bf08af03-9f4b-4d61-9f62-8236013f86a1-6ba2a81867894a3ba28857864dc93a55.xlsx");

        presaleOrderSetDeliveryDateImportHandler.processData(downloadCenterDataMsg);


    }
}