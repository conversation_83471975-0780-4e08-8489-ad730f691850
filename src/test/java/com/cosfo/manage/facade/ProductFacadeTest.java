package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
class ProductFacadeTest {


    @Resource
    ProductFacade productFacade;

    @Test
    void queryBySkuCodes() {

        List<ProductsMappingResp> list = productFacade.queryBySkuCodes(Lists.newArrayList("596360614183", "596633818178", "970480642171","1052113375511","1052768445032"));
        System.err.println(JSONObject.toJSONString(list));
    }
}