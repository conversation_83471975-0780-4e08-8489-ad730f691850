package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import net.summerfarm.client.req.product.saas.SummerFarmSkuPriceInfoReq;
import net.summerfarm.client.resp.product.saas.SummerFarmSkuPriceInfoResp;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class SfInterfaceServiceFacadeTest {

    @Resource
    private SfInterfaceServiceFacade sfInterfaceServiceFacade;

    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;

    @Test
    void queryAdminSkuPricingInfo() {

//        String reqStr = "[{\"adminId\":4600,\"cityName\":[\"北京市\"],\"citySupplyPriceId\":2035513,\"skuId\":485},{\"adminId\":4600,\"cityName\":[\"天津市\"],\"citySupplyPriceId\":2035512,\"skuId\":485}]";
//        String reqStr = "[{\"adminId\":4599,\"cityName\":[\"绍兴市\"],\"citySupplyPriceId\":2035480,\"skuId\":22659},{\"adminId\":4599,\"cityName\":[\"杭州市\"],\"citySupplyPriceId\":2035479,\"skuId\":22659},{\"adminId\":4599,\"cityName\":[\"杭州市\"],\"citySupplyPriceId\":2035476,\"skuId\":4535},{\"adminId\":4599,\"cityName\":[\"天津市\"],\"citySupplyPriceId\":2035465,\"skuId\":22649}]";
        String reqStr = "[{\"adminId\":4599,\"cityName\":[\"丽水市\"],\"citySupplyPriceId\":2035056,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"台州市\"],\"citySupplyPriceId\":2035055,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"舟山市\"],\"citySupplyPriceId\":2035054,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"衢州市\"],\"citySupplyPriceId\":2035053,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"金华市\"],\"citySupplyPriceId\":2035052,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"绍兴市\"],\"citySupplyPriceId\":2035051,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"湖州市\"],\"citySupplyPriceId\":2035050,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"嘉兴市\"],\"citySupplyPriceId\":2035049,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"温州市\"],\"citySupplyPriceId\":2035048,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"宁波市\"],\"citySupplyPriceId\":2035047,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"杭州市\"],\"citySupplyPriceId\":2035046,\"skuId\":19565},{\"adminId\":4599,\"cityName\":[\"黄冈市\"],\"citySupplyPriceId\":2035040,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"荆州市\"],\"citySupplyPriceId\":2035039,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"襄阳市\"],\"citySupplyPriceId\":2035038,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"中山市\"],\"citySupplyPriceId\":2035037,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"武汉市\"],\"citySupplyPriceId\":2035036,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"福州市\"],\"citySupplyPriceId\":2035035,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"丽水市\"],\"citySupplyPriceId\":2035034,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"杭州市\"],\"citySupplyPriceId\":2035033,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"衡水市\"],\"citySupplyPriceId\":2035032,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"廊坊市\"],\"citySupplyPriceId\":2035031,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"沧州市\"],\"citySupplyPriceId\":2035030,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"承德市\"],\"citySupplyPriceId\":2035029,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"张家口市\"],\"citySupplyPriceId\":2035028,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"保定市\"],\"citySupplyPriceId\":2035027,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"邢台市\"],\"citySupplyPriceId\":2035026,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"邯郸市\"],\"citySupplyPriceId\":2035025,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"秦皇岛市\"],\"citySupplyPriceId\":2035024,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"唐山市\"],\"citySupplyPriceId\":2035023,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"石家庄市\"],\"citySupplyPriceId\":2035022,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"北京市\"],\"citySupplyPriceId\":2035021,\"skuId\":19309},{\"adminId\":4599,\"cityName\":[\"杭州市\"],\"citySupplyPriceId\":2034997,\"skuId\":19213}]";

        List<SummerFarmSkuPriceInfoReq> reqs = JSONArray.parseArray(reqStr, SummerFarmSkuPriceInfoReq.class);
        List<SummerFarmSkuPriceInfoResp> newList = sfInterfaceServiceFacade.queryAdminSkuPricingInfo(reqs);
        System.err.println(JSON.toJSONString(newList));

        List<net.summerfarm.manage.client.saas.req.SummerFarmSkuPriceInfoReq> oldReqs = JSONArray.parseArray(reqStr, net.summerfarm.manage.client.saas.req.SummerFarmSkuPriceInfoReq.class);
        List<net.summerfarm.manage.client.saas.resp.SummerFarmSkuPriceInfoResp> oldList = summerFarmInterfaceServiceFacade.queryAdminSkuPricingInfo(oldReqs);
        System.err.println(JSON.toJSONString(oldList));

        System.err.println(JSON.toJSONString(newList).equals(JSON.toJSONString(oldList)));

    }
}