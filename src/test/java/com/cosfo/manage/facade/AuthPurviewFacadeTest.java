package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class AuthPurviewFacadeTest {

    @Resource
    private AuthPurviewFacade authPurviewFacade;

    @Test
    void createMerchantStoreAccount() {
        ArrayList<Long> storeIds = Lists.newArrayList(765L, 104741L, 104750L, 104677L, 764L, 879L);

        List<String> menuNameTile = authPurviewFacade.listMenuNameTile(2L, storeIds);
        System.out.println(JSON.toJSONString(menuNameTile));

    }
}