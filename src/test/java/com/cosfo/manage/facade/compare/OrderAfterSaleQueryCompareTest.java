package com.cosfo.manage.facade.compare;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleEnableDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleWithOrderDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleEnableResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.cosfo.ordercenter.client.service.OrderAfterSaleQueryService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderAfterSaleQueryCompareTest {

    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryService orderAfterSaleQueryService;

    @Test
    void queryByOrderIdCompareTest() {
        DubboResponse<List<OrderAfterSaleResp>> listDubboResponse = orderAfterSaleQueryProvider.queryByOrderId(93554L, 2L);
        DubboResponse<List<OrderAfterSaleDTO>> listDubboResponse1 = orderAfterSaleQueryService.queryByOrderId(93554L, 2L);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void queryEnableApplyCompareTest() {
        OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setOrderId(112494L);
        req.setTenantId(2L);
        DubboResponse<Map<Long, OrderAfterSaleEnableResp>> mapDubboResponse = orderAfterSaleQueryProvider.queryEnableApply(req);
        DubboResponse<Map<Long, OrderAfterSaleEnableDTO>> map = orderAfterSaleQueryService.queryEnableApply(req);
        Assertions.assertEquals(JSON.toJSONString(mapDubboResponse.getData()), JSON.toJSONString(map.getData()));
    }

    @Test
    void countOrderAfterSaleCompareTest() {
        OrderAfterSaleCountReq req = new OrderAfterSaleCountReq();
        req.setTenantId(2L);
        req.setOrderIds(Lists.newArrayList(93554L));
        req.setStoreId(4338L);
        DubboResponse<Integer> integerDubboResponse = orderAfterSaleQueryProvider.countOrderAfterSale(req);
        DubboResponse<Integer> integerDubboResponse1 = orderAfterSaleQueryService.countOrderAfterSale(req);
        Assertions.assertEquals(JSON.toJSONString(integerDubboResponse.getData()), JSON.toJSONString(integerDubboResponse1.getData()));
    }

    @Test
    void queryListCompareTest() {
        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setOrderIds(Lists.newArrayList(93554L));
        req.setTenantId(2L);
        DubboResponse<List<OrderAfterSaleResp>> listDubboResponse = orderAfterSaleQueryProvider.queryList(req);
        DubboResponse<List<OrderAfterSaleDTO>> listDubboResponse1 = orderAfterSaleQueryService.queryList(req);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void queryPageCompareTest() {
        OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
        req.setTenantId(2L);
        req.setPageNum(1);
        req.setPageSize(10);
        DubboResponse<PageInfo<OrderAfterSaleWithOrderResp>> mapDubboResponse = orderAfterSaleQueryProvider.queryPage(req);
        DubboResponse<PageInfo<OrderAfterSaleWithOrderDTO>> mapDubboResponse1 = orderAfterSaleQueryService.queryPage(req);
        Assertions.assertEquals(JSON.toJSONString(mapDubboResponse.getData()), JSON.toJSONString(mapDubboResponse1.getData()));
    }

    @Test
    void queryByNosCompareTest() {
        DubboResponse<List<OrderAfterSaleResp>> listDubboResponse = orderAfterSaleQueryProvider.queryByNos(Lists.newArrayList("AS1769976056249917447"));
        DubboResponse<List<OrderAfterSaleDTO>> listDubboResponse1 = orderAfterSaleQueryService.queryByNos(Lists.newArrayList("AS1769976056249917447"));
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void queryByIdsCompareTest() {
        DubboResponse<List<OrderAfterSaleDTO>> listDubboResponse = orderAfterSaleQueryService.queryByIds(Lists.newArrayList(20114L, 20120L));
        DubboResponse<List<OrderAfterSaleResp>> listDubboResponse1 = orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(20114L, 20120L));
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }

    @Test
    void calculateRefundPriceCompareTest() {
        OrderAfterSaleCalRefundPriceReq req = new OrderAfterSaleCalRefundPriceReq();
        req.setOrderItemId(289315L);
        req.setQuantity(10);
        DubboResponse<BigDecimal> bigDecimalDubboResponse = orderAfterSaleQueryProvider.calculateRefundPrice(req);

        DubboResponse<BigDecimal> bigDecimalDubboResponse1 = orderAfterSaleQueryService.calculateRefundPrice(req);
        Assertions.assertEquals(JSON.toJSONString(bigDecimalDubboResponse.getData()), JSON.toJSONString(bigDecimalDubboResponse1.getData()));
    }

    @Test
    void getRecentlyUsedReturnAddressIdCompareTest() {
        DubboResponse<Long> longDubboResponse = orderAfterSaleQueryProvider.getRecentlyUsedReturnAddressId(2L);
        DubboResponse<Long> longDubboResponse1 = orderAfterSaleQueryService.getRecentlyUsedReturnAddressId(2L);
        Assertions.assertEquals(JSON.toJSONString(longDubboResponse.getData()), JSON.toJSONString(longDubboResponse1.getData()));
    }

    @Test
    void queryOrderAfterSaleForBillCompareTest() {
        QueryBillOrderAfterSaleReq req = new QueryBillOrderAfterSaleReq();
        req.setTenantId(2L);
        req.setStartTime(LocalDateTime.now().minusDays(2));
        req.setEndTime(LocalDateTime.now().minusDays(1));
        DubboResponse<List<OrderAfterSaleResp>> listDubboResponse = orderAfterSaleQueryProvider.queryOrderAfterSaleForBill(req);
        DubboResponse<List<OrderAfterSaleDTO>> listDubboResponse1 = orderAfterSaleQueryService.queryOrderAfterSaleForBill(req);
        Assertions.assertEquals(JSON.toJSONString(listDubboResponse.getData()), JSON.toJSONString(listDubboResponse1.getData()));
    }
}
