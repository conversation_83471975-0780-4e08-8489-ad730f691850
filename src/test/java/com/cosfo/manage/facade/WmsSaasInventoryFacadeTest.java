package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import net.summerfarm.manage.client.saas.req.SummerFarmSkuSupplyStatusReq;
import net.summerfarm.manage.client.saas.resp.SummerFarmSkuSupplyStatusResp;
import net.summerfarm.wms.saleinventory.dto.dto.CitySupplyStatusDTO;
import net.summerfarm.wms.saleinventory.dto.res.QueryCitySupplyStatusResp;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
class WmsSaasInventoryFacadeTest {

    @Resource
    private WmsSaasInventoryFacade wmsSaasInventoryFacade;
    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;


    @Test
    void queryCitySupplyStatus() {
//        String reqStr = "[{\"cityName\":\"宁波市\",\"citySupplyPriceId\":2035654,\"skuId\":23505},{\"cityName\":\"丽水市\",\"citySupplyPriceId\":2035653,\"skuId\":23505},{\"cityName\":\"杭州市\",\"citySupplyPriceId\":2035652,\"skuId\":23505},{\"cityName\":\"温州市\",\"citySupplyPriceId\":2035651,\"skuId\":23505},{\"cityName\":\"景德镇市\",\"citySupplyPriceId\":2035640,\"skuId\":26128},{\"cityName\":\"南昌市\",\"citySupplyPriceId\":2035639,\"skuId\":26128},{\"cityName\":\"秦皇岛市\",\"citySupplyPriceId\":2035638,\"skuId\":26128},{\"cityName\":\"北京市\",\"citySupplyPriceId\":2035513,\"skuId\":485},{\"cityName\":\"天津市\",\"citySupplyPriceId\":2035512,\"skuId\":485},{\"cityName\":\"天津市\",\"citySupplyPriceId\":2035511,\"skuId\":21719}]";
        String reqStr = "[{\"cityName\":\"金华市\",\"citySupplyPriceId\":2035659,\"skuId\":23010},{\"cityName\":\"丽水市\",\"citySupplyPriceId\":2035658,\"skuId\":23010},{\"cityName\":\"温州市\",\"citySupplyPriceId\":2035657,\"skuId\":23010},{\"cityName\":\"杭州市\",\"citySupplyPriceId\":2035656,\"skuId\":23010},{\"cityName\":\"宁波市\",\"citySupplyPriceId\":2035654,\"skuId\":23505},{\"cityName\":\"丽水市\",\"citySupplyPriceId\":2035653,\"skuId\":23505},{\"cityName\":\"杭州市\",\"citySupplyPriceId\":2035652,\"skuId\":23505},{\"cityName\":\"温州市\",\"citySupplyPriceId\":2035651,\"skuId\":23505},{\"citySupplyPriceId\":2035502},{\"citySupplyPriceId\":2035482},{\"citySupplyPriceId\":2035481},{\"cityName\":\"杭州市\",\"citySupplyPriceId\":2035182,\"skuId\":16492},{\"cityName\":\"天津市\",\"citySupplyPriceId\":2035181,\"skuId\":16492},{\"cityName\":\"杭州市\",\"citySupplyPriceId\":2035086,\"skuId\":19573},{\"cityName\":\"荆门市\",\"citySupplyPriceId\":2035079,\"skuId\":19615},{\"cityName\":\"黄冈市\",\"citySupplyPriceId\":2035078,\"skuId\":19615},{\"cityName\":\"荆州市\",\"citySupplyPriceId\":2035077,\"skuId\":19615},{\"cityName\":\"襄阳市\",\"citySupplyPriceId\":2035076,\"skuId\":19615},{\"cityName\":\"中山市\",\"citySupplyPriceId\":2035075,\"skuId\":19615},{\"cityName\":\"武汉市\",\"citySupplyPriceId\":2035074,\"skuId\":19615},{\"cityName\":\"福州市\",\"citySupplyPriceId\":2035073,\"skuId\":19615},{\"cityName\":\"丽水市\",\"citySupplyPriceId\":2035072,\"skuId\":19615},{\"cityName\":\"杭州市\",\"citySupplyPriceId\":2035071,\"skuId\":19615},{\"cityName\":\"衡水市\",\"citySupplyPriceId\":2035070,\"skuId\":19615},{\"cityName\":\"廊坊市\",\"citySupplyPriceId\":2035069,\"skuId\":19615},{\"cityName\":\"沧州市\",\"citySupplyPriceId\":2035068,\"skuId\":19615},{\"cityName\":\"承德市\",\"citySupplyPriceId\":2035067,\"skuId\":19615},{\"cityName\":\"张家口市\",\"citySupplyPriceId\":2035066,\"skuId\":19615},{\"cityName\":\"保定市\",\"citySupplyPriceId\":2035065,\"skuId\":19615},{\"cityName\":\"邢台市\",\"citySupplyPriceId\":2035064,\"skuId\":19615},{\"cityName\":\"邯郸市\",\"citySupplyPriceId\":2035063,\"skuId\":19615},{\"cityName\":\"秦皇岛市\",\"citySupplyPriceId\":2035062,\"skuId\":19615},{\"cityName\":\"唐山市\",\"citySupplyPriceId\":2035061,\"skuId\":19615},{\"cityName\":\"石家庄市\",\"citySupplyPriceId\":2035060,\"skuId\":19615},{\"cityName\":\"北京市\",\"citySupplyPriceId\":2035059,\"skuId\":19615},{\"cityName\":\"杭州市\",\"citySupplyPriceId\":2035045,\"skuId\":2512}]";

        List<CitySupplyStatusDTO> citySupplyStatusDTOList = JSONArray.parseArray(reqStr, CitySupplyStatusDTO.class);
        List<QueryCitySupplyStatusResp> newList = wmsSaasInventoryFacade.queryCitySupplyStatus(citySupplyStatusDTOList);

        List<SummerFarmSkuSupplyStatusReq> summerfarmSkuSupplyStatusInputs = JSONArray.parseArray(reqStr, SummerFarmSkuSupplyStatusReq.class);
        List<SummerFarmSkuSupplyStatusResp> oldList = summerFarmInterfaceServiceFacade.queryCitySupplyStatus(summerfarmSkuSupplyStatusInputs);

        System.err.println("new" + JSON.toJSONString(newList));
        System.err.println("old" + JSON.toJSONString(oldList));

        System.err.println(JSON.toJSONString(newList).equals(JSON.toJSONString(oldList)));

    }
}