package com.cosfo.manage.tenant.dao;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.tenant.model.po.TenantCompanyAccount;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantCompanyAccountDaoTest {

    @Resource
    private TenantCompanyAccountDao tenantCompanyAccountDao;

    @Test
    void queryAccountMap() {
        Map<Long, TenantCompanyAccount> map = tenantCompanyAccountDao.queryAccountMap(Sets.newHashSet(1L), 2L);
        System.out.println(JSON.toJSONString(map));
    }
}