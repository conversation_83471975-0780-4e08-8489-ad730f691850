package com.cosfo.manage.tenant.service;

import com.cosfo.manage.common.context.FlowRuleAuditBizTypeEnum;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
class TenantFlowSchemeServiceTest {

    @Resource
    private TenantFlowSchemeService tenantFlowSchemeService;

    @Test
    void getAuditByStoreIdAndBizType() {
    }

    @Test
    void getAuditSwitchByStoreIdAndBizType() {
        boolean flag = tenantFlowSchemeService.getAuditSwitchByStoreIdAndBizType(4260L, FlowRuleAuditBizTypeEnum.ORDER_AUDIT);
        System.err.println(flag);
    }

    @Test
    void canAuditByAccountIdAndBizType() {
        tenantFlowSchemeService.canAuditByAccountIdAndBizType(1292L, 4260L, FlowRuleAuditBizTypeEnum.ORDER_AUDIT);
    }

    @Test
    void canAuditStoreIdsByAccountIdAndBizType() {
//        System.err.println(tenantFlowSchemeService.canAuditStoreIdsByAccountIdAndBizType(1292L, Lists.newArrayList(4260L, 148531L), FlowRuleAuditBizTypeEnum.ORDER_AUDIT));
        System.err.println(tenantFlowSchemeService.canAuditStoreIdsByAccountIdAndBizType(11731L, Lists.newArrayList(4260L, 4262L, 148531L), FlowRuleAuditBizTypeEnum.ORDER_AUDIT));
    }

    @Test
    void initDefaultFlowScheme() {
        tenantFlowSchemeService.initDefaultFlowScheme(1003L);
    }
}