package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.dto.TenantMeasureReportQueryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-09
 **/
@SpringBootTest
public class TenantMeasureTestService {

    @Resource
    private TenantMeasureService tenantMeasureService;
    @Test
    public void testMeasure() {
        tenantMeasureService.measure(2L);
    }

    @Test
    public void testQueryMeasureResult() {
        TenantMeasureReportQueryDTO tenantMeasureReportQueryDTO = new TenantMeasureReportQueryDTO();
        tenantMeasureReportQueryDTO.setTenantId(2L);
        tenantMeasureReportQueryDTO.setId(1L);
        tenantMeasureService.queryMeasureResult(tenantMeasureReportQueryDTO);
    }

    @Test
    public void testDoMeasure() {
        tenantMeasureService.doMeasure(31L);
    }

    @Test
    public void testCancelMeasure() {
        tenantMeasureService.cancelMeasure(8L);
    }
}
