package com.cosfo.manage.tenant.service;

import com.cosfo.manage.tenant.model.vo.TenantStoreConfigVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;


@SpringBootTest
class TenantStoreConfigServiceTest {

    @Resource
    private TenantStoreConfigService tenantStoreConfigService;

    @Test
    void queryTenantStoreConfigList() {

        List<Long> storeIds = Arrays.asList(162463L);
        String configKey = "delivery_note_print_price";
        List<TenantStoreConfigVO> list = tenantStoreConfigService.queryTenantStoreConfigList(storeIds, configKey);
        System.err.println(list);
    }
}