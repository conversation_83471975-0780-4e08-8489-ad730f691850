package com.cosfo.manage.tenant.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.ThreadTokenHolder;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import com.cosfo.manage.tenant.model.vo.MenuPurviewVO;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class AuthMenuPurviewServiceTest {

    @Resource
    private AuthMenuPurviewService authMenuPurviewService;

    @BeforeEach
    void init() {
        LoginContextInfoDTO dto = new LoginContextInfoDTO();
        dto.setAuthUserId(6L);
        dto.setTenantId(2L);
        ThreadTokenHolder.setToken(dto);
    }
    @Test
    void listMenuPurview() {
        List<MenuPurviewVO> menuPurviewVOS = authMenuPurviewService.listMenuPurview(2L);
        assertNotNull(menuPurviewVOS);
    }

    @Test
    void listUserMenuPurview() {
        List<MenuPurviewVO> menuPurviewVOS = authMenuPurviewService.listUserMenuPurview();
        assertNotNull(menuPurviewVOS);
    }

    @Test
    void addMenuPurview() {
        MenuPurviewDTO menuPurviewDTO = new MenuPurviewDTO();
        menuPurviewDTO.setMenuName("测试1");
        menuPurviewDTO.setWeight(1);
        menuPurviewDTO.setDescription("试试看");
        Boolean aBoolean = authMenuPurviewService.addMenuPurview(menuPurviewDTO);
        assertTrue(aBoolean);
    }

    @Test
    void addChildMenuPurview() {
        MenuPurviewDTO menuPurviewDTO = new MenuPurviewDTO();
        menuPurviewDTO.setMenuName("测试1-child1");
        menuPurviewDTO.setWeight(1);
        menuPurviewDTO.setDescription("试试看-child1");
        menuPurviewDTO.setParentId(615);
        menuPurviewDTO.setUrl("cosfo_manage:role:add");
        Boolean aBoolean = authMenuPurviewService.addMenuPurview(menuPurviewDTO);
        assertTrue(aBoolean);
    }

    @Ignore
    @Test
    void addChildChildMenuPurview() {
        MenuPurviewDTO menuPurviewDTO = new MenuPurviewDTO();
        menuPurviewDTO.setMenuName("测试1-child1-child1");
        menuPurviewDTO.setWeight(1);
        menuPurviewDTO.setDescription("试试看-child1-child1");
        menuPurviewDTO.setParentId(616);
        menuPurviewDTO.setUrl("cosfo_manage:role:add");
        Boolean aBoolean = authMenuPurviewService.addMenuPurview(menuPurviewDTO);
        // 前端判断
        assertFalse(aBoolean);
    }
    @Test
    void updateMenuPurview() {
        MenuPurviewDTO menuPurviewDTO = new MenuPurviewDTO();
        menuPurviewDTO.setId(617L);
        menuPurviewDTO.setMenuName("测试1-child1-child2");
        menuPurviewDTO.setWeight(1);
        menuPurviewDTO.setDescription("试试看-child1-child2");
        menuPurviewDTO.setParentId(616);
        menuPurviewDTO.setUrl("cosfo_manage:role:add");
        Boolean aBoolean = authMenuPurviewService.updateMenuPurview(menuPurviewDTO);
        assertTrue(aBoolean);
    }

    @Test
    void delMenuPurview() {
        MenuPurviewDTO menuPurviewDTO = new MenuPurviewDTO();
        menuPurviewDTO.setId(617L);
        Boolean aBoolean = authMenuPurviewService.delMenuPurview(menuPurviewDTO);
        assertTrue(aBoolean);
    }

    @Test
    void sortMenuPurview() {
    }
}