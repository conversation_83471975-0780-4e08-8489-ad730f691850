package com.cosfo.manage.tenant.repository;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.tenant.model.po.TenantFlowRuleAudit;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@RunWith(SpringRunner.class)
class TenantFlowRuleAuditRepositoryTest {

    @Resource
    TenantFlowRuleAuditRepository tenantFlowRuleAuditRepository;

    @Test
    void selectByTenantAndFlowSchemeId() {
        List<TenantFlowRuleAudit> list = tenantFlowRuleAuditRepository.selectByTenantAndFlowSchemeId(2L, 1L);
        System.err.println(JSON.toJSONString(list));
    }
}