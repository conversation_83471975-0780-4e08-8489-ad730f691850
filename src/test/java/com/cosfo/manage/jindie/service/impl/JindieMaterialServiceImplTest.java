package com.cosfo.manage.jindie.service.impl;

import com.cosfo.manage.jindie.service.JindieMaterialService;
import com.kingdee.service.data.entity.MaterialDetailReply;
import com.kingdee.service.data.entity.MaterialListReply;
import com.kingdee.service.data.entity.MaterialMaterialListReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * 金蝶物料服务测试类
 *
 * 注意：运行此测试需要确保以下条件：
 * 1. 已配置好金蝶API的相关参数（ClientApiConfig中的clientId和clientSecret）
 * 2. JindieFacade能够正确获取token
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class JindieMaterialServiceImplTest {

    @Resource
    private JindieMaterialService jindieMaterialService;

    /**
     * 测试通过ID获取物料详情
     * 注意：此测试需要提前知道一个有效的物料ID
     */
    @Test
    public void getMaterialDetail() {
        String materialNumber = "lsfw01";

        MaterialDetailReply materialDetail = jindieMaterialService.getMaterialDetail(null, materialNumber);
        System.out.println(materialDetail);
    }

    @Test
    public void testGetMaterialList() {
        MaterialMaterialListReq request = new MaterialMaterialListReq();
        MaterialListReply materialList = jindieMaterialService.getMaterialList(request);
        System.out.println(materialList);
    }

}