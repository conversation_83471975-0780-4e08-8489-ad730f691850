package com.cosfo.manage.common.repository;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.context.DocCodeChannelTypeEnum;
import com.cosfo.manage.common.model.po.DocCodeMapping;
import com.cosfo.manage.common.repository.DocCodeMappingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest
@ActiveProfiles("dev4")
class DocCodeMappingRepositoryIntegrationTest {

    @Autowired
    private DocCodeMappingRepository docCodeMappingRepository;

    @BeforeEach
    void setUp() {
    }

    @Test
    @DisplayName("Should insert batch of DocCodeMapping successfully")
    void shouldBatchInsertSuccessfully() {
        List<DocCodeMapping> docCodeMappingList = Arrays.asList(new DocCodeMapping(), new DocCodeMapping());

        docCodeMappingRepository.batchInsert(docCodeMappingList);

        List<DocCodeMapping> result = docCodeMappingRepository.list();
        assertNotNull(result);
        assertEquals(docCodeMappingList.size(), result.size());
    }

    @Test
    @DisplayName("Should select by target code and type successfully")
    void shouldSelectByTargetCodeAndTypeSuccessfully() {
        List<String> targetCodeList = Arrays.asList("123");
        Integer type = 2;
        Long tenantId = 1L;

        List<DocCodeMapping> result = docCodeMappingRepository.selectByTargetCodeAndType(targetCodeList, type, DocCodeChannelTypeEnum.MEI_TUAN.getCode(), tenantId);

        System.out.println(JSON.toJSONString(result));
        assertNotNull(result);
        assertEquals(targetCodeList.size(), result.size());
    }
}