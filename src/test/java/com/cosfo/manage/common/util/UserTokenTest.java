package com.cosfo.manage.common.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class UserTokenTest {

    @Test
    void getTokenId(){
       log.info("authUserId:" + UserLoginContextUtils.getAuthUserId());
       log.info("tenantId:" + UserLoginContextUtils.getTenantId());
    }
}

