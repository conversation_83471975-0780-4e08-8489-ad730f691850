package com.cosfo.manage.common.task;

import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.repository.PaymentRepository;
import com.cosfo.manage.common.context.RedisKeyEnum;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;


@RunWith(SpringRunner.class)
@SpringBootTest
class MarketItemSalesValidationTaskTest {

    @Resource
    private PaymentRepository paymentRepository;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Test
    void testCorrectData() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startOfDay = yesterday.atStartOfDay();
        LocalDateTime endOfDay = yesterday.atTime(23, 59, 59);

        List<Payment> payments = paymentRepository.selectSuccessPaymentByTime(startOfDay, endOfDay);
        payments.forEach(payment -> {
            String cacheKey = RedisKeyEnum.CM00008.join(payment.getId().toString());
            redisTemplate.delete(cacheKey);
        });
    }

}