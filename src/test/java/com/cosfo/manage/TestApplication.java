package com.cosfo.manage;

import com.alibaba.schedulerx.SchedulerxAutoConfigure;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;

/**
 * 加载指定类测试
 * @author: xiaowk
 * @date: 2023/4/16 上午3:10
 */
//@SpringBootApplication
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableAutoConfiguration(exclude = {SchedulerxAutoConfigure.class})
@ComponentScan(basePackages = {"com.cosfo", "net.xianmu.authentication"},
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = { CosfoManageApplication.class }),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.cosfo\\.manage\\.common\\.task..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.cosfo\\.manage\\..*\\.controller.*")
        })
@DubboComponentScan(basePackages = "com.cosfo.manage.**.provider")
public class TestApplication {

}