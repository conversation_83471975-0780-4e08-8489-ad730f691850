package com.cosfo.manage.market.repository.impl;

import com.cosfo.manage.bill.repository.PaymentRepository;
import com.cosfo.manage.market.repository.MarketItemOrderSummaryRepository;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest
class MarketItemOrderSummaryRepositoryImplTest {

    @Resource
    private MarketItemOrderSummaryRepository marketItemOrderSummaryRepository;

    @Test
    public void testTotalAmountValidation() {

        // 查询30天的
        for (int i = 0; i < 30; i++) {
            LocalDate date = LocalDate.now().minusDays(i);
            LocalDateTime startOfDate = date.atStartOfDay();
            LocalDateTime endOfDate = date.atTime(LocalTime.MAX);
            BigDecimal totalPriceByPayment = marketItemOrderSummaryRepository.sumTotalOrderPriceByPayments(startOfDate, endOfDate);
            BigDecimal totalPriceInSummary = marketItemOrderSummaryRepository.sumTotalOrderPricePerDay(date);
            assertEquals(totalPriceByPayment, totalPriceInSummary);
        }
    }

    @Test
    public void testTotalQuantityValidation() {
        // 查询30天的数据对不对
        for (int i = 0; i < 30; i++) {
            LocalDate date = LocalDate.now().minusDays(i);
            LocalDateTime startOfDate = date.atStartOfDay();
            LocalDateTime endOfDate = date.atTime(LocalTime.MAX);
            Integer totalQuantityByPayment = marketItemOrderSummaryRepository.sumTotalOrderQuantityByPayments(startOfDate, endOfDate);
            Integer totalQuantityInSummary = marketItemOrderSummaryRepository.sumTotalOrderQuantityPerDay(date);
            assertEquals(totalQuantityByPayment, totalQuantityInSummary);
        }
    }
}