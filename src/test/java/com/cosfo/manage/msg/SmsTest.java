package com.cosfo.manage.msg;

import com.cosfo.manage.common.constant.SmsConstants;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.sms.SmsSender;
import com.cosfo.manage.common.sms.model.Sms;
import com.cosfo.manage.common.sms.model.SmsSenderFactory;
import net.xianmu.common.exception.BizException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @description: SMS测试
 * @author: George
 * @date: 2023-05-15
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class SmsTest {

    @Resource
    private SmsSenderFactory smsSenderFactory;

    @Test
    public void testSendCode() {
        Sms sms = new Sms();
        sms.setPhone("15605658291");
        sms.setSceneId(3L);
        List<String> args = Arrays.asList("15605658291", "本来不该有", "15605658291", "2023@0515");
        sms.setArgs(args);

        // 发送验证码
        boolean success = smsSenderFactory.getSmsSender().sendSms(sms);
        if (!success) {
            throw new BizException(ResultDTOEnum.SEND_CODE_FAIL.getMessage());
        }
    }
}
