package com.cosfo.manage.merchant.service.deliveryfeerule;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleEditDTO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleSpecialVO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryStepFeeVO;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class NoWarehouseDeliveryFeeRuleStrategyTest {

    @Resource
    private NoWarehouseDeliveryFeeRuleStrategy noWarehouseDeliveryFeeRuleStrategy;

    @Test
    void saveDeliveryFeeRule() {
        MerchantDeliveryFeeRuleEditDTO ruleEditDTO = new MerchantDeliveryFeeRuleEditDTO();
        ruleEditDTO.setTenantId(24457L);
        ruleEditDTO.setType(noWarehouseDeliveryFeeRuleStrategy.type());
        ruleEditDTO.setRuleType(0);
        ruleEditDTO.setDefaultType(1);
        ruleEditDTO.setFreeDeliveryType(0);
        MerchantDeliveryStepFeeVO stepFeeVO = new MerchantDeliveryStepFeeVO();
        stepFeeVO.setStepThreshold(BigDecimal.ZERO);
        stepFeeVO.setDeliveryFee(new BigDecimal("10"));
        ruleEditDTO.setStepFeeList(Lists.newArrayList(stepFeeVO));

        boolean result = noWarehouseDeliveryFeeRuleStrategy.saveDeliveryFeeRule(ruleEditDTO);
        assertTrue(result);
    }

    @Test
    void saveDeliveryFeeRuleWithSpecial() {
        MerchantDeliveryFeeRuleEditDTO ruleEditDTO = new MerchantDeliveryFeeRuleEditDTO();
        ruleEditDTO.setTenantId(24457L);
        ruleEditDTO.setType(noWarehouseDeliveryFeeRuleStrategy.type());
        ruleEditDTO.setDefaultType(1);
        ruleEditDTO.setFreeDeliveryType(0);
        ruleEditDTO.setId(634L);

        List<MerchantDeliveryStepFeeVO> defaultStepFeeList = new ArrayList<>();

        MerchantDeliveryStepFeeVO stepFeeVO = new MerchantDeliveryStepFeeVO();
        stepFeeVO.setStepThreshold(BigDecimal.ZERO);
        stepFeeVO.setDeliveryFee(new BigDecimal("123"));
        defaultStepFeeList.add(stepFeeVO);
        MerchantDeliveryStepFeeVO stepFeeVO2 = new MerchantDeliveryStepFeeVO();
        stepFeeVO2.setStepThreshold(new BigDecimal("14"));
        stepFeeVO2.setDeliveryFee(new BigDecimal("5"));
        defaultStepFeeList.add(stepFeeVO2);
        ruleEditDTO.setStepFeeList(defaultStepFeeList);


        MerchantDeliveryFeeRuleEditDTO specialVO = new MerchantDeliveryFeeRuleEditDTO();
        specialVO.setId(635L);
        specialVO.setTenantId(24457L);
        specialVO.setType(noWarehouseDeliveryFeeRuleStrategy.type());
        specialVO.setHitItemIds(Lists.newArrayList(1L, 2L));
        specialVO.setHitAreas(Lists.newArrayList(Lists.newArrayList("浙江省", "杭州市", "西湖区"), Lists.newArrayList("浙江省", "杭州市", "余杭区")));
        List<MerchantDeliveryStepFeeVO> stepFeeList = new ArrayList<>();

        MerchantDeliveryStepFeeVO stepFeeVO3 = new MerchantDeliveryStepFeeVO();
        stepFeeVO3.setStepThreshold(new BigDecimal("10"));
        stepFeeVO3.setDeliveryFee(new BigDecimal("10"));
        stepFeeList.add(stepFeeVO3);

        MerchantDeliveryStepFeeVO stepFeeVO4 = new MerchantDeliveryStepFeeVO();
        stepFeeVO4.setStepThreshold(new BigDecimal("10"));
        stepFeeVO4.setDeliveryFee(new BigDecimal("5"));
        stepFeeList.add(stepFeeVO4);
        specialVO.setStepFeeList(stepFeeList);


        ruleEditDTO.setSpecialDeliveryFeeRule(Lists.newArrayList(specialVO));

        boolean result = noWarehouseDeliveryFeeRuleStrategy.saveDeliveryFeeRule(ruleEditDTO);
        assertTrue(result);
    }

    @Test
    void getRule() {
        MerchantDeliveryFeeRuleVO rule = noWarehouseDeliveryFeeRuleStrategy.getRule(24457L);
        System.out.println(JSON.toJSONString(rule));
    }

    @Test
    void saveNoItemIdsSpecialRule() {
        MerchantDeliveryFeeRuleEditDTO ruleEditDTO = new MerchantDeliveryFeeRuleEditDTO();
        ruleEditDTO.setTenantId(24457L);
        ruleEditDTO.setType(noWarehouseDeliveryFeeRuleStrategy.type());
        ruleEditDTO.setDefaultType(1);
        ruleEditDTO.setFreeDeliveryType(0);
        ruleEditDTO.setId(634L);

        MerchantDeliveryFeeRuleEditDTO specialVO = new MerchantDeliveryFeeRuleEditDTO();
        specialVO.setId(635L);
        specialVO.setTenantId(24457L);
        specialVO.setType(noWarehouseDeliveryFeeRuleStrategy.type());
        specialVO.setAllItemHit(true);
        List<MerchantDeliveryStepFeeVO> stepFeeList = new ArrayList<>();

        MerchantDeliveryStepFeeVO stepFeeVO3 = new MerchantDeliveryStepFeeVO();
        stepFeeVO3.setStepThreshold(new BigDecimal("10"));
        stepFeeVO3.setDeliveryFee(new BigDecimal("10"));
        stepFeeList.add(stepFeeVO3);

        ruleEditDTO.setSpecialDeliveryFeeRule(Lists.newArrayList(specialVO));

        noWarehouseDeliveryFeeRuleStrategy.saveDeliveryFeeRule(ruleEditDTO);


    }


    @Test
    void stepFeeSortedTest() {
        MerchantDeliveryFeeRuleVO rule = noWarehouseDeliveryFeeRuleStrategy.getRule(1003L);
        System.out.println(JSON.toJSONString(rule));
    }
}