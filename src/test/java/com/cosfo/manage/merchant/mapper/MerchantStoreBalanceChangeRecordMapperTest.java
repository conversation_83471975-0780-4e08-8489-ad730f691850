package com.cosfo.manage.merchant.mapper;

import com.cosfo.manage.merchant.model.dto.balance.BalanceRecordDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalanceChangeRecord;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MerchantStoreBalanceChangeRecordMapperTest {

    @Resource
    private MerchantStoreBalanceChangeRecordMapper merchantStoreBalanceChangeRecordMapper;

    @Test
    void list() {
        BalanceRecordDTO balanceRecordDTO = new BalanceRecordDTO();
        balanceRecordDTO.setStartTime(LocalDateTime.now());
        balanceRecordDTO.setEndTime(LocalDateTime.now());
        balanceRecordDTO.setTenantId(2L);
        balanceRecordDTO.setPageIndex(1);
        balanceRecordDTO.setPageSize(10);


        List<MerchantStoreBalanceChangeRecord> list = merchantStoreBalanceChangeRecordMapper.list(balanceRecordDTO);
        System.out.println(list);
    }
}