package com.cosfo.manage.report.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.product.model.dto.ProductStockWarningQueryDTO;
import com.cosfo.manage.product.model.vo.ProductStockWarningVO;
import com.cosfo.manage.report.model.dto.ProductStockChangeDTO;
import com.cosfo.manage.report.model.dto.ProductStockWarnInput;
import com.cosfo.manage.report.model.dto.StockForWaringConfigDTO;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@SpringBootTest
class ProductStockForewarningReportServiceTest {

    @Resource
    private ProductStockForewarningReportService productStockForewarningReportService;

    @Test
    void updateReportConfig() {
        StockForWaringConfigDTO stockForWaringConfigDTO = new StockForWaringConfigDTO();
        stockForWaringConfigDTO.setWaringType(0);
        stockForWaringConfigDTO.setDay(7);
        stockForWaringConfigDTO.setIsActive(false);
        stockForWaringConfigDTO.setUpdateTime(LocalDateTime.now());
        productStockForewarningReportService.updateReportConfig(stockForWaringConfigDTO, 1L);
    }

    @Test
    void getRecentReportConfigTest() {
        System.out.println(JSON.toJSONString(productStockForewarningReportService.getRecentReportConfig(2L)));
    }

    @Test
    void getReportConfig() {
        StockForWaringConfigDTO reportConfig = productStockForewarningReportService.getReportConfig(1L, true);
        System.out.println(reportConfig);
    }

    @Test
    void exportTest() throws InterruptedException {
        ProductStockWarnInput input = new ProductStockWarnInput();
        input.setTenantId(2L);
        productStockForewarningReportService.exportProductWarnList(input);
        Thread.sleep(5000);
    }

    @Test
    void queryPage() {
        ProductStockWarnInput input = new ProductStockWarnInput();
        input.setPageSize(10);
        input.setPageIndex(1);
        input.setTenantId(2L);
        PageInfo<ProductStockWarningVO> productStockWarningVOPageInfo = productStockForewarningReportService.queryProductWarnList(input);
        System.out.println(JSON.toJSONString(productStockWarningVOPageInfo));
    }

    @Test
    void updateAfterDataRefreshed() {
        productStockForewarningReportService.updateReportConfigWithDateRefreshed(1L);
    }

    @Test
    void refreshSaleAmountPerDay() {
        productStockForewarningReportService.refreshSaleAmountPerDay(2L);
    }

    @Test
    void updateStockWarn() {
        ProductStockChangeDTO productStockChangeDTO = new ProductStockChangeDTO();
        productStockForewarningReportService.updateStockWarn(productStockChangeDTO);
    }

    @Test
    void testQuerySummary() {
        ProductStockWarningQueryDTO productStockWarningQueryDTO = new ProductStockWarningQueryDTO();
        productStockWarningQueryDTO.setTenantId(2L);
        productStockWarningQueryDTO.setStockStatus(1);
        productStockWarningQueryDTO.setPageIndex(1);
        productStockWarningQueryDTO.setPageSize(10);
        productStockForewarningReportService.queryWarningSummary(productStockWarningQueryDTO);
    }
}