package com.cosfo.manage.agentorder.dao;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.manage.agentorder.model.param.PlanOrderQueryParam;
import com.cosfo.manage.agentorder.model.po.PlanOrder;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
class PlanOrderDaoTest {


    @Resource
    private PlanOrderDao planOrderDao;


    @Test
    void queryPage() {

        PlanOrderQueryParam queryParam = new PlanOrderQueryParam();
        queryParam.setPageIndex(1);
        queryParam.setPageSize(5);
        queryParam.setTenantId(2L);
//        queryParam.setStoreIds(Lists.newArrayList());
//        queryParam.setPlanOrderStatus(0);
//        queryParam.setAgentOrderNo("");
//        queryParam.setPlanOrderNo("");
//        queryParam.setItemIds(Lists.newArrayList(35489L));

        Page<PlanOrder> planOrderPage = planOrderDao.queryPage(queryParam);

        System.err.println(JSON.toJSONString(planOrderPage));
    }

    @Test
    void getByPlanOrderNo() {
        PlanOrder planOrder = planOrderDao.getByPlanOrderNo("PO170721190770922");
        System.err.println(JSON.toJSONString(planOrder));
    }
}