<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.MerchantStoreOrderProportionAnalysisMapper">

    <select id="querySumByCondition"
            resultType="com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportion">
        select
            ifnull(sum(order_amount), 0) orderAmount,
            ifnull(sum(order_price), 0) orderPrice,
            <if test="dimension != null and dimension == 1">
                store_type storeType
            </if>
            <if test="dimension != null and dimension == 2">
                store_group_name storeGroupName
            </if>
        from merchant_store_order_proportion_analysis
        <where>
            tenant_id = #{tenantId} and time_tag = #{timeTag} and type = #{type}
            <if test="title != null and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="storeIds != null">
                and store_id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="dimension != null and dimension == 1">
            group by store_type
        </if>
        <if test="dimension != null and dimension == 2">
            group by store_group_name
        </if>
    </select>
    <select id="listByCondition"
            resultType="com.cosfo.manage.merchant.model.po.MerchantStoreOrderProportionAnalysis">
        select
        store_id storeId,
        ifnull(sum(order_amount), 0) orderAmount,
        ifnull(sum(total_order_amount), 0)  totalOrderAmount,
        ifnull(sum(order_amount_upper_period), 0) orderAmountUpperPeriod,
        ifnull(sum(total_order_amount_upper_period), 0) totalOrderAmountUpperPeriod,
        ifnull(sum(order_price), 0) orderPrice,
        ifnull(sum(total_order_price), 0) totalOrderPrice,
        ifnull(sum(order_price_upper_period), 0) orderPriceUpperPeriod,
        ifnull(sum(total_order_price_upper_period), 0) totalOrderPriceUpperPeriod,
        store_group_name storeGroupName,
        store_type storeType
        from merchant_store_order_proportion_analysis
        <where>
            tenant_id = #{tenantId} and time_tag = #{timeTag} and type = #{type}
            <if test="title != null and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="storeIds != null">
                and store_id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        group by store_id
        <if test="orderAmountSort != null and orderAmountSort != ''">
            order by orderAmount ${orderAmountSort}
        </if>
        <if test="orderPriceSort != null and orderPriceSort != ''">
            order by orderPrice ${orderPriceSort}
        </if>
    </select>

    <select id="exportByCondition"
            resultType="com.cosfo.manage.merchant.model.po.MerchantStoreOrderProportionAnalysis" fetchSize="1000">
        select
        store_id storeId,
        ifnull(sum(order_amount), 0) orderAmount,
        ifnull(sum(total_order_amount), 0)  totalOrderAmount,
        ifnull(sum(order_amount_upper_period), 0) orderAmountUpperPeriod,
        ifnull(sum(total_order_amount_upper_period), 0) totalOrderAmountUpperPeriod,
        ifnull(sum(order_price), 0) orderPrice,
        ifnull(sum(total_order_price), 0) totalOrderPrice,
        ifnull(sum(order_price_upper_period), 0) orderPriceUpperPeriod,
        ifnull(sum(total_order_price_upper_period), 0) totalOrderPriceUpperPeriod,
        store_group_name storeGroupName,
        store_type storeType
        from merchant_store_order_proportion_analysis
        <where>
            tenant_id = #{tenantId} and time_tag = #{timeTag} and type = #{type}
            <if test="title != null and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="storeIds != null">
                and store_id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        group by store_id
        <if test="orderPriceSort != null and orderPriceSort != ''">
            order by orderPrice ${orderPriceSort}
        </if>
        <if test="orderAmountSort != null and orderAmountSort != ''">
            order by orderAmount ${orderAmountSort}
        </if>
    </select>
</mapper>
