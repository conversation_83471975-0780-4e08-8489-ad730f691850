<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.MarketItemOnSaleSoldOutDetailMapper">


    <select id="querySummary" resultType="com.cosfo.manage.market.model.po.MarketItemOnSaleSoldOutDetail">
        SELECT ifnull(SUM(sold_out_time), 0) soldOutTime, ifnull(SUM(on_sale_time), 0) AS onSaleTime
        FROM market_item_on_sale_sold_out_detail
        WHERE tenant_id = #{tenantId}
        AND time_tag <![CDATA[>=]]> #{startTime}
        AND time_tag <![CDATA[<=]]> #{endTime}
    </select>
</mapper>
