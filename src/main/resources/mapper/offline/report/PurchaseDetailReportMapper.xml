<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.PurchaseDetailReportMapper">

    <select id="queryPurchaseSummaryListBySku" resultType="com.cosfo.manage.report.model.vo.PurchaseSummarySkuVO">
        SELECT
        sku_id,
        `name`,
        specification,
        unit,
        category_id,
        brand_name,
        (COALESCE(SUM(purchase_quantity), 0) - COALESCE(SUM(back_quantity), 0)) AS totalQuantity,
        (COALESCE(SUM(purchase_amount), 0) - COALESCE(SUM(back_amount), 0)) AS totalAmount
        FROM
        purchase_detail_report
        WHERE
        tenant_id = #{queryDTO.tenantId}
        AND inbound_status = '全部入库'
        <if test="queryDTO.startTime != null and queryDTO.endTime != null">
            AND purchase_date BETWEEN #{queryDTO.startTime} AND #{queryDTO.endTime}
        </if>
        <if test="queryDTO.skuId != null">
            AND sku_id = #{queryDTO.skuId}
        </if>
        <if test="queryDTO.name != null">
            AND name LIKE CONCAT('%', #{queryDTO.name}, '%')
        </if>
        <if test="queryDTO.brandName != null">
            AND brand_name LIKE CONCAT('%', #{queryDTO.brandName}, '%')
        </if>
        <if test="queryDTO.categoryIds != null and queryDTO.categoryIds.size() > 0">
            AND category_id IN
            <foreach collection="queryDTO.categoryIds" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        GROUP BY sku_id
        <if test="queryDTO.sortList != null and queryDTO.sortList.size() > 0">
            ORDER BY
            <foreach item="item" index="index" collection="queryDTO.sortList" separator=",">
                ${item.sortBy} ${item.orderBy}
            </foreach>
        </if>
        <if test="queryDTO.sortList == null">
            ORDER BY totalAmount desc, sku_id desc
        </if>
    </select>

    <select id="queryPurchaseSummaryListBySupplier" resultType="com.cosfo.manage.report.model.vo.PurchaseSummarySupplierVO">
        SELECT
        supplier_id,
        sku_id,
        supplier,
        `name`,
        specification,
        unit,
        category_id,
        brand_name,
        (COALESCE(SUM(purchase_quantity), 0) - COALESCE(SUM(back_quantity), 0)) AS totalQuantity,
        (COALESCE(SUM(purchase_amount), 0) - COALESCE(SUM(back_amount), 0)) AS totalAmount,
        ROUND((COALESCE(SUM(purchase_amount), 0) - COALESCE(SUM(back_amount), 0)) / (COALESCE(SUM(purchase_quantity), 0) - COALESCE(SUM(back_quantity), 0)), 2) AS averagePrice
        FROM
        purchase_detail_report
        WHERE
        tenant_id = #{queryDTO.tenantId}
        AND inbound_status = '全部入库'
        <if test="queryDTO.startTime != null and queryDTO.endTime != null">
            AND purchase_date BETWEEN #{queryDTO.startTime} AND #{queryDTO.endTime}
        </if>
        <if test="queryDTO.skuId != null">
            AND sku_id = #{queryDTO.skuId}
        </if>
        <if test="queryDTO.name != null">
            AND name LIKE CONCAT('%', #{queryDTO.name}, '%')
        </if>
        <if test="queryDTO.brandName != null">
            AND brand_name LIKE CONCAT('%', #{queryDTO.brandName}, '%')
        </if>
        <if test="queryDTO.categoryIds != null and queryDTO.categoryIds.size() > 0">
            AND category_id IN
            <foreach collection="queryDTO.categoryIds" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        <if test="queryDTO.supplierId != null">
            AND supplier_id = #{queryDTO.supplierId}
        </if>
        GROUP BY supplier_id, sku_id
        <if test="queryDTO.sortList != null and queryDTO.sortList.size() > 0">
            ORDER BY
            <foreach item="item" index="index" collection="queryDTO.sortList" separator=",">
                ${item.sortBy} ${item.orderBy}
            </foreach>
        </if>
        <if test="queryDTO.sortList == null">
            ORDER BY supplier_id desc, totalAmount desc
        </if>
    </select>
</mapper>
