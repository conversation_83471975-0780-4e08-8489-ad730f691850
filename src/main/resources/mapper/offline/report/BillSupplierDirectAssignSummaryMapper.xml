<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.BillSupplierDirectAssignSummaryMapper">
    <select id="querySummary" resultType="com.cosfo.manage.bill.model.po.BillSupplierDirectAssignSummary">
        select
            ifnull(sum(total_purchase_amount), 0) totalPurchaseAmount,
            ifnull(sum(total_purchase_amount_wechat_pay), 0) totalPurchaseAmountWechatPay,
            ifnull(sum(total_purchase_amount_bill_balance_pay), 0) totalPurchaseAmountBillBalancePay,
            ifnull(sum(total_amount_of_purchase_and_after_sales_wechat_pay), 0) totalAmountOfPurchaseAndAfterSalesWechatPay,
            ifnull(sum(total_amount_of_purchase_and_after_sales_bill_balance_pay), 0) totalAmountOfPurchaseAndAfterSalesBillBalancePay,
            ifnull(sum(total_purchase_amount_remove_refund_wechat_pay), 0) totalPurchaseAmountRemoveRefundWechatPay,
            ifnull(sum(total_purchase_amount_remove_refund_bill_balance_pay), 0) totalPurchaseAmountRemoveRefundBillBalancePay,
            ifnull(sum(goods_delivery_fee_remove_refund), 0) goodsDeliveryFeeRemoveRefund,
            ifnull(sum(total_sales_amount), 0) totalSalesAmount,
            ifnull(sum(total_sales_amount_wechat_pay), 0) totalSalesAmountWechatPay,
            ifnull(sum(total_sales_amount_bill_balance_pay), 0) totalSalesAmountBillBalancePay,
            ifnull(sum(after_sale_amount_wechat_pay), 0) afterSaleAmountWechatPay,
            ifnull(sum(after_sale_amount_bill_balance_pay), 0) afterSaleAmountBillBalancePay,
            ifnull(sum(deduct_after_sales_amount_wechat_pay), 0) deductAfterSalesAmountWechatPay,
            ifnull(sum(deduct_after_sales_amount_bill_balance_pay), 0) deductAfterSalesAmountBillBalancePay,
            ifnull(sum(sales_and_purchase_difference_wechat_pay), 0) salesAndPurchaseDifferenceWechatPay,
            ifnull(sum(sales_and_purchase_difference_bill_balance_pay), 0) salesAndPurchaseDifferenceBillBalancePay,
            ifnull(sum(delivery_fee_deduct_after_sales_amount), 0) deliveryFeeDeductAfterSalesAmount
        from bill_supplier_direct_assign_summary
        where tenant_id = #{tenantId}
          and time_tag between #{startTime} and #{endTime}
          and supplier_id = #{supplierId}
    </select>
</mapper>
