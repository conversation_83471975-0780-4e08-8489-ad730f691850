<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.report.mapper.ProductMovementMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductMovement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="time_tag" jdbcType="VARCHAR" property="timeTag" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="on_sale_num" jdbcType="INTEGER" property="onSaleNum" />
    <result column="last_on_sale_num" jdbcType="INTEGER" property="lastOnSaleNum" />
    <result column="on_sale_chain" jdbcType="VARCHAR" property="onSaleChain" />
    <result column="pay_success_num" jdbcType="INTEGER" property="paySuccessNum" />
    <result column="last_pay_success_num" jdbcType="INTEGER" property="lastPaySuccessNum" />
    <result column="pay_success_chain" jdbcType="VARCHAR" property="paySuccessChain" />
    <result column="sale_rate" jdbcType="DECIMAL" property="saleRate" />
    <result column="last_sale_rate" jdbcType="DECIMAL" property="lastSaleRate" />
    <result column="sale_rate_chain" jdbcType="VARCHAR" property="saleRateChain" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="warehouse_type" property="warehouseType"/>
    <result column="delivery_type" property="deliveryType"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, time_tag, `type`, on_sale_num, last_on_sale_num, on_sale_chain, pay_success_num,
    last_pay_success_num, pay_success_chain, sale_rate, last_sale_rate, sale_rate_chain,
    create_time, update_time, warehouse_type, delivery_type
  </sql>
  <select id="querySummary" resultMap="BaseResultMap">
    select sum(ifnull(on_sale_num, 0)) on_sale_num,
           sum(IFNULL(last_on_sale_num, 0)) last_on_sale_num,
           sum(IFNULL(on_sale_chain, 0)) on_sale_chain,
           sum(IFNULL(pay_success_num, 0)) pay_success_num,
           sum(ifnull(last_pay_success_num, 0)) last_pay_success_num,
           sum(ifnull(pay_success_chain, 0)) pay_success_chain,
           sum(ifnull(sale_rate, 0)) sale_rate,
           sum(ifnull(last_sale_rate, 0)) last_sale_rate,
           sum(ifnull(sale_rate_chain, 0)) sale_rate_chain
    from product_movement
    where tenant_id = #{tenantId} and time_tag = #{timeTag} and type = #{type}
    <if test="goodsType != null">
      and goods_type = #{goodsType}
    </if>
  </select>

  <select id="batchQuerySummary" resultMap="BaseResultMap">
    select
    time_tag,
    sum(ifnull(on_sale_num, 0)) on_sale_num,
    sum(IFNULL(last_on_sale_num, 0)) last_on_sale_num,
    sum(IFNULL(on_sale_chain, 0)) on_sale_chain,
    sum(IFNULL(pay_success_num, 0)) pay_success_num,
    sum(ifnull(last_pay_success_num, 0)) last_pay_success_num,
    sum(ifnull(pay_success_chain, 0)) pay_success_chain,
    sum(ifnull(sale_rate, 0)) sale_rate,
    sum(ifnull(last_sale_rate, 0)) last_sale_rate,
    sum(ifnull(sale_rate_chain, 0)) sale_rate_chain
    from product_movement
    where tenant_id = #{tenantId} and type = #{type} and time_tag in
    <foreach collection="timeTags" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    <if test="goodsType != null">
      and goods_type = #{goodsType}
    </if>
    group by tenant_id, time_tag
  </select>
</mapper>
