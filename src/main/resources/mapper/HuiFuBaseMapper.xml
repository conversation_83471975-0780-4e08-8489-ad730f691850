<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.HuiFuBaseMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.HuiFuBase">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="regName" column="reg_name" jdbcType="VARCHAR"/>
            <result property="huifuId" column="huifu_id" jdbcType="VARCHAR"/>
            <result property="settleType" column="settle_type" jdbcType="VARCHAR"/>
            <result property="openingBank" column="opening_bank" jdbcType="VARCHAR"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="accountNumber" column="account_number" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,reg_name,
        huifu_id,settle_type,opening_bank,
        account_name,account_number,create_time,
        update_time
    </sql>
</mapper>
