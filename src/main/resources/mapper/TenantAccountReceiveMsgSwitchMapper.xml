<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantAccountReceiveMsgSwitchMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.TenantAccountReceiveMsgSwitch">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="channelType" column="channel_type" jdbcType="TINYINT"/>
            <result property="channelCode" column="channel_code" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="tenantAccountId" column="tenant_account_id" jdbcType="BIGINT"/>
            <result property="availableStatus" column="available_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        channle_type,channle_code,tenant_id,
        tenant_account_id,available_status
    </sql>
</mapper>
