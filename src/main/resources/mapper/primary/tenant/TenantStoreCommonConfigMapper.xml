<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantStoreCommonConfigMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tenant_store_common_config (tenant_id, store_id, config_key, config_value, config_desc)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantId}, #{item.storeId}, #{item.configKey}, #{item.configValue}, #{item.configDesc})
        </foreach>
        ON DUPLICATE KEY UPDATE
        tenant_id = VALUES(tenant_id),
        store_id = VALUES(store_id),
        config_value = VALUES(config_value),
        config_desc = VALUES(config_desc);
    </insert>

</mapper>
