<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantCompanyMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.TenantCompany">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="credit_code" jdbcType="VARCHAR" property="creditCode" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="business_license" jdbcType="VARCHAR" property="businessLicense" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="opening_bank" jdbcType="VARCHAR" property="openingBank" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, company_name, credit_code, province, city, area, address, phone, business_license, 
    create_time, update_time, opening_bank, account_name, account_number
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tenant_company
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tenant_company
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.cosfo.manage.tenant.model.po.TenantCompany">
    insert into tenant_company (id, tenant_id, company_name, 
      credit_code, province, city, 
      area, address, phone, 
      business_license, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{companyName,jdbcType=VARCHAR}, 
      #{creditCode,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{businessLicense,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cosfo.manage.tenant.model.po.TenantCompany">
    insert into tenant_company
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="creditCode != null">
        credit_code,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="businessLicense != null">
        business_license,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null">
        #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="businessLicense != null">
        #{businessLicense,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.tenant.model.po.TenantCompany">
    update tenant_company
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null">
        credit_code = #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="businessLicense != null">
        business_license = #{businessLicense,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.tenant.model.po.TenantCompany">
    update tenant_company
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      company_name = #{companyName,jdbcType=VARCHAR},
      credit_code = #{creditCode,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      business_license = #{businessLicense,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByTenantId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from tenant_company
    where `tenant_id` = #{tenantId}
    limit 1
  </select>

  <select id="selectByCompanyName" resultType="long">
    select tenant_id
    from tenant_company tc
    left join tenant t on tc.tenant_id = t.id
    where t.type = 1
      and tc.company_name like concat("%", #{companyName}, "%")
  </select>

  <select id="batchQueryByTenantIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from tenant_company
    where `tenant_id` in
    <foreach collection="tenantIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    limit 1
  </select>
</mapper>