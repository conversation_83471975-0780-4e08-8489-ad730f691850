<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantFundAccountConfigMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.tenant.model.po.TenantFundAccountConfig">
    <!--@mbg.generated-->
    <!--@Table tenant_fund_account_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="limit_goods_group" jdbcType="LONGVARCHAR" property="limitGoodsGroup" />
    <result column="allow_shipping_fee" jdbcType="TINYINT" property="allowShippingFee" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, account_name, limit_goods_group, allow_shipping_fee, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tenant_fund_account_config
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="getInfoByTenantId" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from tenant_fund_account_config
    where tenant_id = #{tenantId,jdbcType=BIGINT} limit 1
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from tenant_fund_account_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantFundAccountConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tenant_fund_account_config (tenant_id, account_name, limit_goods_group, 
      allow_shipping_fee, create_time, update_time
      )
    values (#{tenantId,jdbcType=BIGINT}, #{accountName,jdbcType=VARCHAR}, #{limitGoodsGroup,jdbcType=LONGVARCHAR}, 
      #{allowShippingFee,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.tenant.model.po.TenantFundAccountConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tenant_fund_account_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="limitGoodsGroup != null">
        limit_goods_group,
      </if>
      <if test="allowShippingFee != null">
        allow_shipping_fee,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="limitGoodsGroup != null">
        #{limitGoodsGroup,jdbcType=LONGVARCHAR},
      </if>
      <if test="allowShippingFee != null">
        #{allowShippingFee,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.tenant.model.po.TenantFundAccountConfig">
    <!--@mbg.generated-->
    update tenant_fund_account_config
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="limitGoodsGroup != null">
        limit_goods_group = #{limitGoodsGroup,jdbcType=LONGVARCHAR},
      </if>
      <if test="allowShippingFee != null">
        allow_shipping_fee = #{allowShippingFee,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.tenant.model.po.TenantFundAccountConfig">
    <!--@mbg.generated-->
    update tenant_fund_account_config
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      account_name = #{accountName,jdbcType=VARCHAR},
      limit_goods_group = #{limitGoodsGroup,jdbcType=LONGVARCHAR},
      allow_shipping_fee = #{allowShippingFee,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>