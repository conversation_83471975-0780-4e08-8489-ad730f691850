<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.tenant.mapper.TenantPrepaymentAccountMapper">
    <select id="calAccountSumGroupByPayTargetType" resultType="com.cosfo.manage.tenant.model.po.TenantPrepaymentAccount">
        SELECT
            payable_target AS payableTarget,
            sum(total_amount) AS availableAmount
        FROM
            tenant_prepayment_account
        WHERE
            tenant_id = #{tenantId}
        GROUP BY
            payable_target
    </select>
</mapper>
