<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.agentorder.mapper.AgentOrderItemMapper">

    <resultMap type="com.cosfo.manage.agentorder.model.po.AgentOrderItem" id="AgentOrderItemMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="agentOrderNo" column="agent_order_no" jdbcType="VARCHAR"/>
        <result property="itemId" column="item_id" jdbcType="INTEGER"/>
        <result property="itemQuantity" column="item_quantity" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="creatorUserId" column="creator_user_id" jdbcType="INTEGER"/>
        <result property="updateUserId" column="update_user_id" jdbcType="INTEGER"/>
    </resultMap>

</mapper>

