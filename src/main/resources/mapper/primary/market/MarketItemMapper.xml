<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.market.mapper.MarketItemMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.market.model.po.MarketItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="market_id" property="marketId"/>
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="specification_unit" jdbcType="VARCHAR" property="specificationUnit" />
    <result column="brand_id" property="brandId"/>
    <result column="brand_name" property="brandName"/>
    <result column="item_code" property="itemCode"/>
    <result column="supplier_id" property="supplierId"/>
    <result column="supplier_name" property="supplierName"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="weight_notes" property="weightNotes"/>
    <result column="goods_type" property="goodsType"/>
    <result column="on_sale" property="onSale"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, market_id, sku_id, specification,
    specification_unit, brand_id, create_time, update_time, brand_name, supplier_id, supplier_name, weight_notes, goods_type, on_sale
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="listAll" resultMap="BaseResultMap">
    select mi.id, mi.tenant_id, mi.market_id, mi.sku_id, mi.specification,
    mi.specification_unit, mi.brand_id, mi.brand_name, mi.supplier_id, mi.supplier_name, mi.weight_notes
    from market_item mi
    inner join market m on m.id = mi.market_id
    <where>
      <if test="tenantId != null">
        and mi.tenant_id = #{tenantId}
      </if>
      <if test="id != null">
        and mi.id = #{id}
      </if>
      <if test="title != null and title != ''">
        and m.title like concat('%',#{title},'%')
      </if>
    </where>
  </select>
  <select id="selectBySkuId" resultType="com.cosfo.manage.market.model.po.MarketItem">
    select <include refid="Base_Column_List"/>
    from market_item
    where tenant_id = #{tenantId} and sku_id = #{skuId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from market_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.market.model.po.MarketItem" useGeneratedKeys="true">
    insert into market_item (tenant_id, market_id, sku_id, specification, weight_notes, specification_unit, brand_id,brand_name, supplier_id, supplier_name,item_code, after_sale_unit, max_after_sale_amount,goods_type)
    values (#{tenantId,jdbcType=BIGINT}, #{marketId}, #{skuId,jdbcType=BIGINT},  #{specification,jdbcType=VARCHAR}, #{weightNotes}, #{specificationUnit,jdbcType=VARCHAR}, #{brandId}, #{brandName}, #{supplierId}, #{supplierName}, #{itemCode}, #{afterSaleUnit}, #{maxAfterSaleAmount},#{goodsType})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.market.model.po.MarketItem" useGeneratedKeys="true">
    insert into market_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="marketId != null">
        market_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="weightNotes != null">
        weight_notes,
      </if>
      <if test="specificationUnit != null">
        specification_unit,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="goodsType != null">
        goods_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="marketId != null">
        #{marketId},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="weightNotes != null">
        #{weightNotes},
      </if>
      <if test="specificationUnit != null">
        #{specificationUnit,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        #{brandId},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierId != null">
        #{supplierId},
      </if>
      <if test="goodsType != null">
        #{goodsType},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.market.model.po.MarketItem">
    update market_item
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="marketId != null">
        market_id = #{marketId},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="weightNotes != null">
        weight_notes = #{weightNotes},
      </if>
      <if test="specificationUnit != null">
        specification_unit = #{specificationUnit,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId},
      </if>
      <if test="goodsType != null">
        goods_type = #{goodsType},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.market.model.po.MarketItem">
    update market_item
    set
      title = #{title,jdbcType=VARCHAR},
      sub_title = #{subTitle,jdbcType=VARCHAR},
      origin = #{origin,jdbcType=VARCHAR},
      main_picture = #{mainPicture,jdbcType=VARCHAR},
      detail_picture = #{detailPicture,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByMarketQueryInput" resultMap="BaseResultMap">
    select
    i.id, i.tenant_id, i.market_id, i.sku_id, i.specification, i.weight_notes,
    i.specification_unit, i.brand_id, i.create_time, i.update_time, i.brand_name
    from
    market_item i
    left join market_area_item a on i.id = a.item_id
    <where>
      <if test="itemId != null">
        and i.id = #{itemId}
      </if>
      <if test="brandName != null and brandName != ''">
        and i.brand_name like concat('%',#{brandName},'%')
      </if>
      <if test="onSale != null">
        and a.on_sale = #{onSale}
      </if>
      <if test="deleteFlag != null">
        and i.delete_flag = #{deleteFlag}
      </if>
      <if test="itemCode != null and itemCode != ''">
        and i.item_code = #{itemCode}
      </if>
      <if test="skuId != null">
        and i.sku_id = #{skuId}
      </if>
      <if test="goodsType != null">
        and i.goods_type = #{goodsType}
      </if>

    </where>
  </select>

    <select id="selectSaleBySkuIds" resultType="java.lang.Long">
      select distinct sku_id
      from
      market_item
      <where>
        delete_flag = 1
        and sku_id in
        <foreach collection="skuIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
        <if test="onSale != null">
          and on_sale = #{onSale}
        </if>
        <if test="tenantId != null">
          and tenant_id = #{tenantId}
        </if>
      </where>
    </select>

  <select id="selectSaleStatusBySkuIds" resultType="com.cosfo.manage.market.model.po.MarketItem">
    select <include refid="Base_Column_List"/>
    from
    market_item
    <where>
      delete_flag = 1
      and sku_id in
      <foreach collection="skuIds" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
    </where>
  </select>
</mapper>
