<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductPricingSupplyCityMappingMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductPricingSupplyCityMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_pricing_supply_id" jdbcType="BIGINT" property="productPricingSupplyId" />
    <result column="city_id" jdbcType="BIGINT" property="cityId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="supply_type" jdbcType="TINYINT" property="supplyType" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, product_pricing_supply_id, city_id, `type`, supply_type, price, start_time, end_time,
    create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from product_pricing_supply_city_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.product.model.po.ProductPricingSupplyCityMapping">
    update product_pricing_supply_city_mapping
    <set>
      <if test="productPricingSupplyId != null">
        product_pricing_supply_id = #{productPricingSupplyId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="supplyType != null">
        supply_type = #{supplyType,jdbcType=TINYINT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.product.model.po.ProductPricingSupplyCityMapping">
    update product_pricing_supply_city_mapping
    set product_pricing_supply_id = #{productPricingSupplyId,jdbcType=BIGINT},
      city_id = #{cityId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=TINYINT},
      supply_type = #{supplyType,jdbcType=TINYINT},
      price = #{price,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="queryCitySupplyPriceRange" resultType="com.cosfo.manage.product.model.dto.ProductPricingSupplyCityRangeDTO">
    select
    min(start_time) as startTime,max(end_time) as endTime, min(price) as minPrice, max(price) as maxPrice, count(id) cityNum, count(city_id) supplyCityCount
    from
    product_pricing_supply_city_mapping
    where product_pricing_supply_id = #{productPricingSupplyId} and start_time &lt;= now() and end_time &gt;= now()
  </select>

  <select id="batchQueryCitySupplyPriceRange" resultType="com.cosfo.manage.product.model.dto.ProductPricingSupplyCityRangeDTO">
    select
      product_pricing_supply_id as productPricingSupplyId,  min(start_time) as startTime,max(end_time) as endTime, min(price) as minPrice, max(price) as maxPrice, count(id) cityNum, count(city_id) supplyCityCount
    from
      product_pricing_supply_city_mapping
    where product_pricing_supply_id in
    <foreach close=")" collection="productPricingSupplyIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    group by product_pricing_supply_id
  </select>

  <select id="queryByIdAndCityName" resultType="com.cosfo.manage.product.model.vo.ProductPricingSupplyCityVO">
    select
        city.`name` cityName, p.id , p.product_pricing_supply_id productPricingSupplyId, p.city_id cityId, p.`type` type, p.supply_type supplyType,
           p.price, p.start_time startTime, p.end_time endTime, p.strategy_value strategyValue,
    case
    when p.end_time &lt; now() Then 0
    when p.start_time &gt; now() Then 2
    when p.start_time &lt;= now() and p.end_time &gt;= now() Then 1 end as expireStatus
    from product_pricing_supply_city_mapping p
    left join common_location_city city on p.city_id = city.id
    where p.product_pricing_supply_id = #{productPricingSupplyId} and p.start_time &lt;= now() and p.end_time &gt;= now()
      <if test="cityName != null and cityName != ''">
        and city.`name` like concat('%',#{cityName},'%')
      </if>
  </select>

  <select id="queryByTenantIdAndSupplySkuId" resultType="com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO">
    select
      m.id, m.product_pricing_supply_id productPricingSupplyId, m.city_id cityId, m.`type`, m.supply_type supplyType, m.price, m.start_time startTime, m.end_time endTime,
      m.create_time createTime, m.update_time updateTime, m.deleted, p.tenant_id tenantId, p.supply_tenant_id supplyTenantId, p.supply_sku_id supplySkuId,
      c.name cityName,m.strategy_value strategyValue
    from
      product_pricing_supply_city_mapping m
    left join product_pricing_supply p on p.id = m.product_pricing_supply_id
    left join common_location_city c on m.city_id = c.id
    where p.tenant_id = #{tenantId} and p.supply_sku_id in
    <foreach close=")" collection="supplySkuIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and m.supply_type = 1 and m.start_time &lt;= now() and m.end_time &gt;= now()
  </select>



  <select id="queryExpiredSupplyCityByTime"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from product_pricing_supply_city_mapping
    where end_time <![CDATA[>=]]> #{expiredStartTime}
    and end_time <![CDATA[<]]> #{expiredEndTime}
  </select>
</mapper>
