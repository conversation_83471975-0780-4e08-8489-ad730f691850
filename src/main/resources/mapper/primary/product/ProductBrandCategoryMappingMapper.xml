<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductBrandCategoryMappingMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductBrandCategoryMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, brand_id, category_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from brand_category_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="queryByBrandAndCategory" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from brand_category_mapping
    where brand_id = #{brandId} and category_id = #{categoryId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from brand_category_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductBrandCategoryMapping" useGeneratedKeys="true">
    insert into brand_category_mapping (brand_id, category_id, create_time,
      update_time)
    values (#{brandId,jdbcType=BIGINT}, #{categoryId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductBrandCategoryMapping" useGeneratedKeys="true">
    insert into brand_category_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brandId != null">
        #{brandId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.product.model.po.ProductBrandCategoryMapping">
    update brand_category_mapping
    <set>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.product.model.po.ProductBrandCategoryMapping">
    update brand_category_mapping
    set brand_id = #{brandId,jdbcType=BIGINT},
      category_id = #{categoryId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
