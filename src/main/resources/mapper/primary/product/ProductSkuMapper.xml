<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductSkuMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.manage.product.model.po.ProductSku">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="spu_id" jdbcType="BIGINT" property="spuId"/>
        <result column="specification" jdbcType="VARCHAR" property="specification"/>
        <result column="specification_unit" jdbcType="VARCHAR" property="specificationUnit"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="volume" jdbcType="VARCHAR" property="volume"/>
        <result column="volume_unit" jdbcType="BIGINT" property="volumeUnit"/>
        <result column="weight" jdbcType="DOUBLE" property="weight"/>
        <result column="amount" jdbcType="BIGINT" property="amount"/>
        <result column="agent_type" jdbcType="TINYINT" property="agentType"/>
        <result column="approve_status" jdbcType="TINYINT" property="approveStatus"/>
        <result column="place_type" jdbcType="TINYINT" property="placeType"/>
        <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime"/>
        <result column="commit_time" jdbcType="TIMESTAMP" property="commitTime"/>
        <result column="custom_sku_code" jdbcType="VARCHAR" property="customSkuCode"/>
    </resultMap>
    <resultMap id="DTOMap" type="com.cosfo.manage.product.model.dto.ProductSkuDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="spu_id" jdbcType="BIGINT" property="spuId"/>
        <result column="specification" jdbcType="VARCHAR" property="specification"/>
        <result column="specification_unit" jdbcType="VARCHAR" property="specificationUnit"/>
        <result column="on_sale" jdbcType="VARCHAR" property="onSale"/>
        <result column="price" jdbcType="VARCHAR" property="price"/>
        <result column="warehouse_type" property="warehouseType"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="title" property="title"/>
        <result column="mini_order_quantity" jdbcType="INTEGER" property="miniOrderQuantity" />
        <result column="main_picture" property="mainPicture"/>
        <result column="sku" property="sku"/>
        <result column="main_picture" property="mainPicture"/>
        <result column="brand_name" property="brandName"/>
        <result column="category_id" property="categoryId"/>
        <result column="sub_title" property="subTitle"/>
        <result column="storage_location" property="storageLocation"/>
        <result column="storage_temperature" property="storageTemperature"/>
        <result column="origin" property="origin"/>
        <result column="guarantee_period" property="guaranteePeriod"/>
        <result column="guarantee_unit" property="guaranteeUnit"/>
        <result column="brand_id" property="brandId"/>
        <result column="associated" property="associated"/>
        <result column="agent_sku_id" property="agentSkuId"/>
        <result column="agent_sku_code" property="agentSkuCode"/>
        <result column="name" property="cityName"/>
        <result column="quantity" property="quantity"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, tenant_id, spu_id, specification, specification_unit, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from product_sku
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectBatchByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from product_sku
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="listAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from product_sku sk
        <where>
            <if test="spuId != null">
                and spu_id = #{spuId}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>
<!--    <select id="selectAll" resultMap="DTOMap">-->
<!--        select sk.id, sk.specification, sk.specification_unit, mai.on_sale, mai.warehouse_type, mai.delivery_type, sk.tenant_id,mai.mini_order_quantity, mai.price_type priceType-->
<!--        from product_sku sk-->
<!--        left join market_area_item mai on sk.id = mai.sku_id-->
<!--        <where>-->
<!--            <if test="spuId != null">-->
<!--                and spu_id = #{spuId}-->
<!--            </if>-->
<!--            <if test="tenantId != null">-->
<!--                and sk.tenant_id = #{tenantId}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from product_sku
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductSku"
            useGeneratedKeys="true">
        insert into product_sku (id, tenant_id, spu_id, specification,
                                 specification_unit)
        values (#{id}, #{tenantId,jdbcType=BIGINT}, #{spuId,jdbcType=BIGINT}, #{specification,jdbcType=VARCHAR},
                #{specificationUnit,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.manage.product.model.po.ProductSku" useGeneratedKeys="true">
        insert into product_sku
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="spuId != null">
                spu_id,
            </if>
            <if test="specification != null">
                specification,
            </if>
            <if test="specificationUnit != null">
                specification_unit,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="volume != null">
                volume,
            </if>
            <if test="weight != null">
                weight,
            </if>
            <if test="agentType != null">
                agent_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="customSkuCode != null and customSkuCode != ''">
                custom_sku_code,
            </if>
            <if test="createType != null">
                create_type,
            </if>
            <if test="skuPicture != null">
                sku_picture,
            </if>
            <if test="skuTitle!=null ">
                sku_title,
            </if>
            <if test="ownerId != null">
                owner_id,
            </if>
            <if test="useFlag != null">
                use_flag,
            </if>
            <if test="subAgentType != null">
                sub_agent_type,
            </if>
            <if test="placeType != null">
                place_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="spuId != null">
                #{spuId,jdbcType=BIGINT},
            </if>
            <if test="specification != null">
                #{specification,jdbcType=VARCHAR},
            </if>
            <if test="specificationUnit != null">
                #{specificationUnit,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="volume != null">
                #{volume},
            </if>
            <if test="weight != null">
                #{weight},
            </if>
            <if test="agentType != null">
                #{agentType},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customSkuCode != null and customSkuCode != ''">
                #{customSkuCode},
            </if>
            <if test="createType != null">
                #{createType},
            </if>
            <if test="skuPicture != null">
                #{skuPicture},
            </if>
            <if test="skuTitle!=null ">
                #{skuTitle},
            </if>
            <if test="ownerId != null">
                #{ownerId},
            </if>
            <if test="useFlag != null">
                #{useFlag},
            </if>
            <if test="subAgentType!=null">
                #{subAgentType},
            </if>
            <if test="placeType != null">
                #{placeType},
            </if>
        </trim>
    </insert>
    <insert id="saveBatch" useGeneratedKeys="true" keyProperty="id">
        insert into product_sku (`tenant_id`, `spu_id`, `agent_type`, `approve_status`, `place_type`, `volume`, `specification`, `weight`, `amount`, `approve_time`, `commit_time`, `specification_unit`, `volume_unit`, `tax_rate_value`,
        `custom_sku_code`,owner_id, specification_type)
        values
        <foreach collection="skuList" item="item" index="index" separator="," >
            (#{item.tenantId,jdbcType=BIGINT}, #{item.spuId,jdbcType=BIGINT}, #{item.agentType,jdbcType=TINYINT},
            #{item.approveStatus,jdbcType=TINYINT}, #{item.placeType,jdbcType=TINYINT}, #{item.volume,jdbcType=VARCHAR}, #{item.specification,jdbcType=VARCHAR}, #{item.weight,jdbcType=DOUBLE},
             #{item.amount,jdbcType=BIGINT}, #{item.approveTime,jdbcType=TIMESTAMP},#{item.commitTime,jdbcType=TIMESTAMP}, #{item.specificationUnit,jdbcType=VARCHAR}, #{item.volumeUnit,jdbcType=BIGINT},
             #{item.taxRateValue,jdbcType=DECIMAL},#{item.customSkuCode,jdbcType=VARCHAR},#{item.ownerId},#{item.specificationType})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.product.model.po.ProductSku">
        update product_sku
        <set>
            <if test="spuId != null">
                spu_id = #{spuId,jdbcType=BIGINT},
            </if>
            <if test="specification != null">
                specification = #{specification,jdbcType=VARCHAR},
            </if>
            <if test="specificationUnit != null">
                specification_unit = #{specificationUnit,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                sku = #{sku},
            </if>
            <if test="volume != null">
                volume = #{volume},
            </if>
            <if test="weight != null">
                weight = #{weight},
            </if>
            <if test="agentType != null">
                agent_type = #{agentType},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customSkuCode != null and customSkuCode != ''">
                custom_sku_code = #{customSkuCode},
            </if>
            <if test="createType != null">
                create_type = #{createType},
            </if>
            <if test="skuPicture != null">
                sku_picture = #{skuPicture},
            </if>
            <if test="skuTitle!=null ">
                sku_title = #{skuTitle},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId},
            </if>
            <if test="useFlag != null">
                use_flag=#{useFlag},
            </if>
            <if test="subAgentType != null">
                sub_agent_type=#{subAgentType},
            </if>
            <if test="placeType != null">
                place_type=#{placeType},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.product.model.po.ProductSku">
        update product_sku
        set tenant_id          = #{tenantId,jdbcType=BIGINT},
            spu_id             = #{spuId,jdbcType=BIGINT},
            specification      = #{specification,jdbcType=VARCHAR},
            specification_unit = #{specificationUnit,jdbcType=VARCHAR},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_time        = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="querySupplySkuInfo" resultMap="DTOMap">
        select
            spu.title, sku.id,sku.sku, sku.specification, sku.specification_unit, sku.tenant_id, spu.title, spu.main_picture , sku.sku, spu.brand_id, spu.category_id
        from product_sku sku
        left join product_spu spu on sku.spu_id = spu.id
        where sku.id in
        <foreach collection="supplySkuIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="querySkuInfo" resultMap="DTOMap">
        select spu.title,
               sku.id,
               sku.specification,
               sku.specification_unit,
               sku.tenant_id,
               spu.main_picture,
               m.agent_sku_code sku,
               spu.brand_name,
               spu.category_id,
               spu.sub_title,
               spu.tenant_id,
               spu.storage_location,
               spu.storage_temperature,
               spu.origin,
               spu.guarantee_period,
               spu.guarantee_unit,
               spu.id spu_id,
               spu.brand_id,
               sku.associated
        from product_sku sku
                 left join product_spu spu on sku.spu_id = spu.id
                 left join product_agent_sku_mapping m on m.sku_id = sku.id
        where sku.id = #{skuId}
    </select>
    <select id="querySkuCount" resultType="java.lang.Integer">
        select count(*)
        from product_sku
        where spu_id = #{spuId}
    </select>
    <select id="queryBySpuId" resultMap="DTOMap">
        select id, specification, specification_unit
        from product_sku
        where spu_id = #{spuId}
    </select>

    <select id="selectAgentSkuByTenantId" resultMap="DTOMap">
        select
            spu.title, sku.id, sku.specification, sku.specification_unit, sku.tenant_id, spu.title, spu.main_picture , sku.sku, spu.brand_name, spu.category_id
        from product_sku sku
        left join product_spu spu on sku.spu_id = spu.id
        <where>
            spu.tenant_id = #{tenantId}
        <if test="productAgentWarehouseDateQueryDTO.title != null and productAgentWarehouseDateQueryDTO.title != ''">
            and spu.title like concat('%',#{productAgentWarehouseDateQueryDTO.title},'%')
        </if>
        <if test="productAgentWarehouseDateQueryDTO.skuId != null">
            and sku.id = #{productAgentWarehouseDateQueryDTO.skuId}
        </if>
        <if test="productAgentWarehouseDateQueryDTO.skuIds.size() &gt; 0">
            and sku.id in
            <foreach collection="productAgentWarehouseDateQueryDTO.skuIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        </where>
    </select>

    <update id="updateAssociated">
        update product_sku
        set associated = #{associated}
        where tenant_id = #{tenantId} and id = #{skuId}
    </update>

    <select id="listByCondition"  resultType="com.cosfo.manage.product.model.dto.ProductSkuDTO">
        select
            sku.id id, sku.sku sku, sku.tenant_id tenantId, spu.id spuId, sku.specification, sku.specification_unit specificationUnit, spu.brand_name brandName,
        spu.title, spu.main_picture mainPicture, spu.category_id categoryId, spu.origin, spu.storage_temperature storageTemperature,
        spu.guarantee_period guaranteePeriod, spu.guarantee_unit guaranteeUnit,  sku.tax_rate_value taxRateValue, sku.place_type placeType, sku.volume volume,
        sku.volume_unit volumeUnit, sku.weight weight, sku.weight_notes weightNotes,custom_sku_code customSkuCode
        from product_sku sku
        left join product_spu spu on sku.spu_id = spu.id
        <where>
            <if test="tenantId != null">
               and sku.tenant_id = #{tenantId}
            </if>
            <if test="title != null">
                and spu.title like concat(#{title},'%')
            </if>
            <if test="brandName != null">
                and spu.brand_name like concat(#{brandName},'%')
            </if>
            <if test="categoryIds != null and categoryIds.size() &gt; 0">
                and spu.category_id in
                <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="skuIds != null and skuIds.size() &gt; 0">
                and sku.id in
                <foreach collection="skuIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAgentTenantSkuId" resultType="java.lang.Long">
        select
            sku.id
        from
        product_pricing_supply t
        left join product_sku sku on t.supply_sku_id = sku.id
        left join product_agent_sku_mapping pasm on pasm.sku_id = sku.id
        where
            t.tenant_id = #{tenantId}
            and pasm.agent_tenant_id = #{agentTenantId}
    </select>

    <select id="selectAgentTenantSkuByTenantId" resultMap="DTOMap">
        select
        spu.title, sku.id, sku.specification, sku.specification_unit, sku.tenant_id, spu.main_picture , sku.sku, spu.brand_name, spu.category_id,
        pasm.agent_sku_id, pasm.agent_sku_code, clc.name, pcs.quantity
        from
        product_pricing_supply t
        inner join product_sku sku on t.supply_sku_id = sku.id
        inner join product_agent_sku_mapping pasm on pasm.sku_id = sku.id
        inner join product_spu spu on spu.id = sku.spu_id
        inner join product_pricing_supply_city_mapping ppscm on t.id = ppscm.product_pricing_supply_id
        inner join common_location_city clc on clc.id = ppscm.city_id
        left join product_city_stock pcs on pcs.sku_id = sku.id and pcs.city_id = clc.id
        <where>
            pasm.agent_tenant_id = #{agentTenantId}
            and ppscm.supply_type = 1
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId}
            </if>
            <if test="productAgentStockQueryDTO.effectTime != null">
                and ppscm.start_time <![CDATA[<]]> #{productAgentStockQueryDTO.effectTime} and ppscm.end_time <![CDATA[>]]> #{productAgentStockQueryDTO.effectTime}
            </if>
            <if test="productAgentStockQueryDTO.title != null and productAgentStockQueryDTO.title != ''">
                and spu.title like concat('%',#{productAgentStockQueryDTO.title},'%')
            </if>
            <if test="productAgentStockQueryDTO.cityIds != null and productAgentStockQueryDTO.cityIds.size() &gt; 0">
                and clc.id in
                <foreach collection="productAgentStockQueryDTO.cityIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="productAgentStockQueryDTO.skuIds != null and productAgentStockQueryDTO.skuIds.size() &gt; 0">
                and sku.id in
                <foreach collection="productAgentStockQueryDTO.skuIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="productAgentStockQueryDTO.saleOut != null and productAgentStockQueryDTO.saleOut == 0">
                and pcs.quantity > 0
            </if>
            <if test="productAgentStockQueryDTO.saleOut != null and productAgentStockQueryDTO.saleOut == 1">
                and pcs.quantity <![CDATA[<=]]> 0
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="dumpAgentTenantSkuInfoByAgentTenantId"
      parameterType="com.cosfo.manage.product.model.dto.AgentTenantSkuQueryDTO"
      resultType="com.cosfo.manage.product.model.dto.AgentSkuDumpDTO">
        SELECT GROUP_CONCAT( DISTINCT product_agent_sku_mapping.`agent_sku_code` ) as agentSkuCodeList,
            common_location_city.name as city,
            common_location_city.id as cityId
        FROM product_pricing_supply
        INNER JOIN product_agent_sku_mapping ON product_agent_sku_mapping.`sku_id` = product_pricing_supply.`supply_sku_id`
        LEFT JOIN product_pricing_supply_city_mapping ON product_pricing_supply_city_mapping.product_pricing_supply_id = product_pricing_supply.`id`
        LEFT JOIN common_location_city on common_location_city.id = product_pricing_supply_city_mapping.city_id
        WHERE `supply_tenant_id` = #{agentTenantId}
        and product_pricing_supply_city_mapping.supply_type = 1
        GROUP BY common_location_city.name,common_location_city.id;
    </select>

    <select id="selectAgentTenantSkuInfoByAgentTenantId"
      parameterType="com.cosfo.manage.product.model.dto.AgentTenantSkuQueryDTO"
      resultType="com.cosfo.manage.product.model.dto.AgentTenantSkuDTO">
        select distinct sku.id, pasm.agent_sku_id agentSkuId, pasm.agent_sku_code agentSkuCode,clc.id cityId, clc.name cityName
        from
        product_pricing_supply t
        inner join product_sku sku on t.supply_sku_id = sku.id
        inner join product_agent_sku_mapping pasm on pasm.sku_id = sku.id
        inner join product_spu spu on spu.id = sku.spu_id
        inner join product_pricing_supply_city_mapping ppscm on t.id = ppscm.product_pricing_supply_id
        inner join common_location_city clc on clc.id = ppscm.city_id
        <where>
            pasm.agent_tenant_id = #{agentTenantId}
            and ppscm.supply_type = 1
            <if test="skuId != null">
                and sku.id = #{skuId}
            </if>
            <if test="supplyIdList != null and supplyIdList.size() > 0">
                and t.id  in
                <foreach collection="supplyIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityNames != null and cityNames.size() > 0">
                and clc.name  in
                <foreach collection="cityNames" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        order by ppscm.city_id desc
    </select>

</mapper>
