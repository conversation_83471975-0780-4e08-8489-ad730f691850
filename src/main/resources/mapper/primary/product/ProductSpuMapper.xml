<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.product.mapper.ProductSpuMapper">

    <resultMap type="com.cosfo.manage.product.model.po.ProductSpu" id="ProductSpuMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
        <result property="brandId" column="brand_id" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="subTitle" column="sub_title" jdbcType="VARCHAR"/>
        <result property="mainPicture" column="main_picture" jdbcType="VARCHAR"/>
        <result property="detailPicture" column="detail_picture" jdbcType="VARCHAR"/>
        <result property="storageLocation" column="storage_location" jdbcType="VARCHAR"/>
        <result property="storageTemperature" column="storage_temperature" jdbcType="VARCHAR"/>
        <result property="guaranteePeriod" column="guarantee_period" jdbcType="INTEGER"/>
        <result property="guaranteeUnit" column="guarantee_unit" jdbcType="INTEGER"/>
        <result property="origin" column="origin" jdbcType="VARCHAR"/>
        <result property="brandName" column="brand_name" jdbcType="VARCHAR"/>
        <result column="agent_type" jdbcType="TINYINT" property="agentType"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, tenant_id, category_id, brand_id, title, sub_title, main_picture, detail_picture, storage_location, storage_temperature,
guarantee_period, guarantee_unit, origin,  brand_name, create_time, update_time
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="ProductSpuMap">
        select id,
               tenant_id,
               category_id,
               brand_id,
               title,
               sub_title,
               main_picture,
               detail_picture,
               storage_location,
               storage_temperature,
               guarantee_period,
               guarantee_unit,
               origin,
               brand_name,
               agent_type,
               create_time,
               update_time
        from product_spu
        where id = #{id}
    </select>

    <select id="selectBatchByPrimaryKey" resultMap="ProductSpuMap">
        select <include refid="Base_Column_List"/>
        from product_spu
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>
    <select id="listAll" resultType="com.cosfo.manage.product.model.dto.ProductSpuDTO">
        select sp.id, sp.title, sp.sub_title subTitle, sp.main_picture mainPicture, sp.brand_id brandId,
               sp.category_id categoryId, sp.detail_picture detailPicture, sp.storage_temperature storageTemperature,
               sp.storage_location storageLocation, sp.guarantee_period guaranteePeriod, sp.guarantee_unit guaranteeUnit, sp.origin,  sp.brand_name
        from product_spu sp
        left join product_sku sk on sp.id = sk.spu_id
        <where>
            sp.tenant_id = #{tenantId}
            <if test="title != null and title.length != 0">
                and title like concat('%',#{title},'%')
            </if>
            <if test="brandName != null and brandName.length != 0">
                and sp.brand_name like concat('%',#{brandName},'%')
            </if>
            <if test="categoryIds != null and categoryIds.size() > 0">
                and category_id in
                <foreach collection="categoryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">
                and sp.id = #{id}
            </if>
            <if test="skuId != null">
                and sk.id = #{skuId}
            </if>
        </where>
        group by sp.id
        order by sp.id desc
    </select>
    <select id="querySummary" resultType="com.cosfo.manage.product.model.dto.ProductSpuDTO">
        select ifnull(max(price), 0) highestPrice, ifnull(min(price), 0) lowestPrice, ifnull(count(mai.on_sale = 1 or null), 0) onSaleSkuAmount
        from product_spu sp
        left join product_sku sk on sp.id = sk.spu_id
        left join market_area_item mai on sk.id = mai.sku_id
        where sp.id = #{id}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into product_spu(id, tenant_id, category_id, brand_id, title, sub_title, main_picture,
                                detail_picture, storage_location, storage_temperature, guarantee_period,
                                guarantee_unit, origin,
                                 agent_type, brand_name, custom_spu_code)
        values (#{id}, #{tenantId}, #{categoryId}, #{brandId}, #{title}, #{subTitle}, #{mainPicture}, #{detailPicture},
                #{storageLocation}, #{storageTemperature}, #{guaranteePeriod}, #{guaranteeUnit}, #{origin}, #{agentType}, #{brandName}, #{customSpuCode})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.product.model.po.ProductSpu" useGeneratedKeys="true">
        insert into product_spu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="categoryId != null">
                category_id,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="subTitle != null">
                sub_title,
            </if>
            <if test="mainPicture != null">
                main_picture,
            </if>
            <if test="detailPicture != null">
                detail_picture,
            </if>
            <if test="storageLocation != null">
                storage_location,
            </if>
            <if test="storageTemperature != null">
                storage_temperature,
            </if>
            <if test="guaranteePeriod != null">
                guarantee_period,
            </if>
            <if test="guaranteeUnit != null">
                guarantee_unit,
            </if>
            <if test="origin != null">
                origin,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="brandName != null">
                brand_name,
            </if>
            <if test="customSpuCode != null">
                custom_spu_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="categoryId != null">
                #{categoryId,jdbcType=BIGINT},
            </if>
            <if test="brandId != null">
                #{brandId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="subTitle != null">
                #{subTitle,jdbcType=VARCHAR},
            </if>
            <if test="mainPicture != null">
                #{mainPicture,jdbcType=VARCHAR},
            </if>
            <if test="detailPicture != null">
                #{detailPicture,jdbcType=VARCHAR},
            </if>
            <if test="storageLocation != null">
                #{storageLocation,jdbcType=TINYINT},
            </if>
            <if test="storageTemperature != null">
                #{storageTemperature,jdbcType=VARCHAR},
            </if>
            <if test="guaranteePeriod != null">
                #{guaranteePeriod,jdbcType=INTEGER},
            </if>
            <if test="guaranteeUnit != null">
                #{guaranteeUnit,jdbcType=TINYINT},
            </if>
            <if test="origin != null">
                #{origin,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="brandName != null">
                #{brandName},
            </if>
            <if test="customSpuCode != null">
                #{customSpuCode},
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="updateByPrimaryKeySelective">
        update product_spu
        <set>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="brandId != null">
                brand_id = #{brandId},
            </if>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="subTitle != null and subTitle != ''">
                sub_title = #{subTitle},
            </if>
            <if test="mainPicture != null and mainPicture != ''">
                main_picture = #{mainPicture},
            </if>
            <if test="detailPicture != null and detailPicture != ''">
                detail_picture = #{detailPicture},
            </if>
            <if test="storageLocation != null">
                storage_location = #{storageLocation},
            </if>
            <if test="storageTemperature != null and storageTemperature != ''">
                storage_temperature = #{storageTemperature},
            </if>
            <if test="guaranteePeriod != null">
                guarantee_period = #{guaranteePeriod},
            </if>
            <if test="guaranteeUnit != null">
                guarantee_unit = #{guaranteeUnit},
            </if>
            <if test="origin != null and origin != ''">
                origin = #{origin},
            </if>
            <if test="brandName != null and origin != ''">
                brand_name = #{brandName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="customSpuCode != null and customSpuCode != ''">
                custom_spu_code = #{customSpuCode},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键修改数据-->
    <update id="update">
        update product_spu
        set category_id         = #{categoryId},
            brand_id            = #{brandId},
            title               = #{title},
            sub_title           = #{subTitle},
            main_picture        = #{mainPicture},
            detail_picture      = #{detailPicture},
            storage_location    = #{storageLocation},
            storage_temperature = #{storageTemperature},
            guarantee_period    = #{guaranteePeriod},
            guarantee_unit      = #{guaranteeUnit},
            origin              = #{origin},
        brand_name              = #{brandName},
            agent_type              = #{agentType}
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from cosfodb.product_spu
        where id = #{id}
    </delete>

    <select id="listByCondition" resultMap="ProductSpuMap">
        select
        distinct spu.id, spu.tenant_id, spu.category_id, spu.brand_id, spu.title, spu.sub_title, spu.main_picture, spu.detail_picture, spu.storage_location, spu.storage_temperature,
        spu.guarantee_period, spu.guarantee_unit, spu.origin,  spu.brand_name, spu.create_time, spu.update_time
        from
        product_spu spu
        left join product_sku sku on spu.id = sku.spu_id
        <where>
            <if test="productQueryInput.title != null">
                and spu.title like concat('%',#{productQueryInput.title},'%')
            </if>
            <if test="productQueryInput.categoryIds != null and productQueryInput.categoryIds.size() > 0 ">
                and spu.category_id in
                <foreach collection="productQueryInput.categoryIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="tenantId != null">
                and spu.tenant_id = #{tenantId}
            </if>
            <if test="productQueryInput.spuId != null">
                and spu.id = #{productQueryInput.spuId}
            </if>
            <if test="productQueryInput.associated != null">
                and sku.associated = #{productQueryInput.associated}
            </if>
            <if test="productQueryInput.useFlag != null">
                and sku.use_flag = #{productQueryInput.useFlag}
            </if>
            <if test="productQueryInput.paramSkuId != null">
                and sku.id = #{productQueryInput.paramSkuId}
            </if>
            <if test="productQueryInput.agentType != null">
                and sku.agent_type = #{productQueryInput.agentType}
            </if>
        </where>
        order by spu.id desc
    </select>
    <select id="queryBySupplySkuIds" resultType="com.cosfo.manage.product.model.vo.ProductSpuVO">
        select spu.id id, sku.id skuId, sku.specification, sku.specification_unit
        specificationUnit, spu.title, c.name categoryName, spu.category_id categoryId,
        b.name brandName, spu.main_picture mainPicture, sku.sku
        from
        product_spu spu
        left join product_sku sku on spu.id = sku.spu_id
        left join category c on c.id = spu.category_id
        left join brand b on b.id = spu.brand_id
        where
        sku.id in
        <foreach collection="supplySkuIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
</mapper>

