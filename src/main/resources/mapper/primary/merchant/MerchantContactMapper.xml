<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantContactMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantContact">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="address_id" jdbcType="BIGINT" property="addressId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="default_flag" jdbcType="INTEGER" property="defaultFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, address_id, `name`, phone, default_flag, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_contact
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByStoreId" resultType="com.cosfo.manage.merchant.model.dto.MerchantContactDTO">
      select mc.id contactId, mc.name contactName, mc.phone, mc.default_flag defaultFlag
      from merchant_contact mc
             inner join merchant_address ma on mc.tenant_id = ma.tenant_id and mc.address_id = ma.id
             inner join merchant_store ms on ma.store_id = ms.id and ms.tenant_id = #{tenantId}
      where ms.id = #{storeId} and ms.tenant_id = #{tenantId}
      order by mc.id asc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_contact
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.merchant.model.po.MerchantContact" useGeneratedKeys="true">
    insert into merchant_contact (tenant_id, address_id, `name`,
      phone, default_flag)
    values (#{tenantId,jdbcType=BIGINT}, #{addressId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR},
      #{phone,jdbcType=VARCHAR}, #{defaultFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.merchant.model.po.MerchantContact" useGeneratedKeys="true">
    insert into merchant_contact
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="addressId != null">
        address_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="defaultFlag != null">
        default_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="addressId != null">
        #{addressId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="defaultFlag != null">
        #{defaultFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.merchant.model.po.MerchantContact">
    update merchant_contact
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="addressId != null">
        address_id = #{addressId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="defaultFlag != null">
        default_flag = #{defaultFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.merchant.model.po.MerchantContact">
    update merchant_contact
    set
      `name` = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      default_flag = #{defaultFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
