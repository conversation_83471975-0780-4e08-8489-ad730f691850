<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantStoreGroupMappingMapper">


    <select id="listAll" resultType="com.cosfo.manage.merchant.model.dto.MerchantStoreDTO">
        select ms.id,
        ms.tenant_id tenantId,
        ms.store_name storeName,
        ms.type,
        ms.register_time registerTime,
        ms.status,
        ms.bill_switch billSwitch,
        ms.store_no storeNo,
        ms.audit_time auditTime,
        ms.remark,
        ms.online_payment onlinePayment,
        concat(ma.province, ma.city, ma.area, ma.address, ifnull(ma.house_number, '')) deliveryAddress,
        ma.province,
        ma.city,
        ma.area,
        ma.address
        from merchant_store_group_mapping msgm
        left join merchant_store ms on  msgm.tenant_id = #{tenantId} and msgm.group_id = #{groupId} and msgm.tenant_id = ms.tenant_id and msgm.store_id = ms.id
        left join  merchant_address ma on ma.tenant_id = #{tenantId} and ma.store_id = msgm.store_id
        <where>
            <if test="tenantId != null">
                and ms.tenant_id = #{tenantId}
            </if>
            <if test="id != null">
                and ms.id = #{id}
            </if>
            <if test="storeNo != null and storeNo != ''">
                and ms.store_no = #{storeNo}
            </if>
            <if test="storeName != null">
                and ms.store_name like concat('%',#{storeName},'%')
            </if>
            <if test="status != null">
                and ms.status = #{status}
            </if>
            <if test="type != null">
                and ms.type = #{type}
            </if>
            <if test="storeIds != null and storeIds.size() >0 ">
                and ms.id in
                <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="province != null and province != ''">
                and ma.province = #{province}
            </if>
            <if test="city != null and city != ''">
                and ma.city = #{city}
            </if>
            <if test="area != null and area != ''">
                and ma.area = #{area}
            </if>
        </where>
        order by ms.id desc
    </select>

    <select id="countStoreNumByGroupId" resultType="java.lang.Integer">
        select count(1)
        from merchant_store s
        left join merchant_store_group_mapping m on s.id=m.store_id
        where m.tenant_id=#{tenantId}
        and m.group_id=#{groupId}
    </select>

</mapper>
