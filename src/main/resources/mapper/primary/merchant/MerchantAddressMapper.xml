<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantAddressMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantAddress">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="house_number" jdbcType="VARCHAR" property="houseNumber" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="poi_note" jdbcType="VARCHAR" property="poiNote" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, province, city, area, address, house_number, poi_note, create_time,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_address
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByStoreId" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from merchant_address
      where store_id = #{storeId} and tenant_id = #{tenantId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.merchant.model.po.MerchantAddress" useGeneratedKeys="true">
    insert into merchant_address (tenant_id, store_id,
      province, city, area,
      address, house_number, poi_note)
    values (#{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR},
      #{address,jdbcType=VARCHAR}, #{houseNumber,jdbcType=VARCHAR}, #{poiNote,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cosfo.manage.merchant.model.po.MerchantAddress" >
    insert into merchant_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="houseNumber != null">
        house_number,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null">
        #{houseNumber,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.merchant.model.po.MerchantAddress">
    update merchant_address
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null">
        house_number = #{houseNumber,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.merchant.model.po.MerchantAddress">
    update merchant_address
    set
      store_id = #{storeId,jdbcType=BIGINT},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      house_number = #{houseNumber,jdbcType=VARCHAR},
      poi_note = #{poiNote,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByStoreIds" resultType="com.cosfo.manage.merchant.model.dto.MerchantAddressDTO">
    select
      ma.store_id storeId,
      mc.name contactName,
      concat(ma.province, ma.city, ma.area, ma.address, ifnull(ma.house_number, '')) deliveryAddress,
      ma.province,
      ma.city,
      ma.area,
      ma.address,
      ma.house_number houseNumber,
      mc.phone contactPhone
    from merchant_address ma
      left join merchant_contact mc on ma.id = mc.address_id and mc.tenant_id = #{tenantId} and mc.default_flag = 1
    where ma.tenant_id = #{tenantId}
    and ma.store_id in
    <foreach collection="storeIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="queryStore" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from merchant_address
    <where>
      <if test="province != null">
        and province = #{province}
      </if>
      <if test="city != null">
        and city = #{city}
      </if>
      <if test="area != null">
        and area = #{area}
      </if>
    </where>
  </select>

  <select id="queryByCityList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from merchant_address
    <where>
        <if test="cityList != null and cityList.size() >0 ">
        and city in
        <foreach collection="cityList" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
        </if>
    </where>
  </select>

  <select id="queryAllStoreCity" resultType="string">
    select
    distinct city
    from merchant_address
    where tenant_id = #{tenantId}
  </select>
  <select id="selectByTenantId" resultType="java.lang.String">
    select distinct(CONCAT( province,"-", city,"-", area)) from merchant_address  where tenant_id = #{tenantId}
  </select>
  <select id="listStoreIdAndAddress" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from merchant_address where tenant_id = #{tenantId}
    <if test="storeIds != null and storeIds.size() >0 ">
      and store_id in
      <foreach collection="storeIds" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
  </select>

</mapper>
