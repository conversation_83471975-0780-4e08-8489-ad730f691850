<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.order.mapper.OrderAgentSkuFeeRuleSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.order.model.po.OrderAgentSkuFeeRuleSnapshot">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="fee_rule_type" jdbcType="TINYINT" property="feeRuleType" />
    <result column="rule" jdbcType="VARCHAR" property="rule" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="hit_rule" jdbcType="VARCHAR" property="hitRule" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, order_id, account_id, fee_rule_type, `rule`, price, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_agent_sku_fee_rule_snapshot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_agent_sku_fee_rule_snapshot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.order.model.po.OrderAgentSkuFeeRuleSnapshot" useGeneratedKeys="true">
    insert into order_agent_sku_fee_rule_snapshot (tenant_id, order_id, account_id, 
      fee_rule_type, `rule`, price, 
      create_time, update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, 
      #{feeRuleType,jdbcType=TINYINT}, #{rule,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.order.model.po.OrderAgentSkuFeeRuleSnapshot" useGeneratedKeys="true">
    insert into order_agent_sku_fee_rule_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="feeRuleType != null">
        fee_rule_type,
      </if>
      <if test="rule != null">
        `rule`,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="feeRuleType != null">
        #{feeRuleType,jdbcType=TINYINT},
      </if>
      <if test="rule != null">
        #{rule,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.order.model.po.OrderAgentSkuFeeRuleSnapshot">
    update order_agent_sku_fee_rule_snapshot
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="feeRuleType != null">
        fee_rule_type = #{feeRuleType,jdbcType=TINYINT},
      </if>
      <if test="rule != null">
        `rule` = #{rule,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.order.model.po.OrderAgentSkuFeeRuleSnapshot">
    update order_agent_sku_fee_rule_snapshot
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      fee_rule_type = #{feeRuleType,jdbcType=TINYINT},
      `rule` = #{rule,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByOrderIdAndTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    order_agent_sku_fee_rule_snapshot
    where order_id = #{orderId} and tenant_id = #{tenantId} and account_id = #{tenantId}
  </select>

  <select id="queryHitRule" resultType="string">
    SELECT
      hit_rule
    FROM
      order_agent_sku_fee_rule_snapshot
    WHERE
      order_id = #{orderId}
      AND tenant_id = #{tenantId}
      LIMIT 1
  </select>

  <select id="batchQueryOrderHitAgentRule" resultMap="BaseResultMap">
    SELECT
    DISTINCT  hit_rule, order_id
    FROM
      order_agent_sku_fee_rule_snapshot
    WHERE
      order_id in
      <foreach collection="ids" item="id" separator="," open="(" close=")" >
        #{id}
      </foreach>
      AND tenant_id = #{tenantId}
  </select>
</mapper>