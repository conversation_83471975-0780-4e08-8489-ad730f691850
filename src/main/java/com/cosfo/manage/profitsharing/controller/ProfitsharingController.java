package com.cosfo.manage.profitsharing.controller;


import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.profitsharing.service.ProfitsharingService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import com.cosfo.manage.profitsharing.model.ProfitSharingSwitchQueryDTO;
/**
 * 分账配置controller
 */
@RestController
@RequestMapping("/profit-sharing/config")
public class ProfitsharingController {

    @Autowired
    private ProfitsharingService profitsharingService;
    /**
     * 查询分账 开关
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/query/switch")
    public CommonResult<Boolean> querySwitch(@Valid @RequestBody ProfitSharingSwitchQueryDTO dto) {
        return CommonResult.ok(profitsharingService.querySwitch(dto));
    }
}

