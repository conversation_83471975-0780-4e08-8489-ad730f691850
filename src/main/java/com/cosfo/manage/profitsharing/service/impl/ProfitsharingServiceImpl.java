package com.cosfo.manage.profitsharing.service.impl;

import com.cosfo.manage.profitsharing.model.ProfitSharingSwitchQueryDTO;
import com.cosfo.manage.profitsharing.service.ProfitsharingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
@Service
@Slf4j
public class ProfitsharingServiceImpl implements ProfitsharingService {

    @Override
    public Boolean querySwitch(ProfitSharingSwitchQueryDTO dto) {
        //todo 还不确定结构
        dto.getProfitSharingMemberId ();
        dto.getProfitSharingMemberType ();
        return Boolean.FALSE;
    }
}
