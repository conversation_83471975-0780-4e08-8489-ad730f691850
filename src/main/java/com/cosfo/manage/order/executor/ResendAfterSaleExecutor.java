//package com.cosfo.manage.order.executor;
//
//import com.cosfo.aftersale.common.context.OrderAfterSaleStatusEnum;
//import com.cosfo.aftersale.common.context.OrderAfterSaleTypeEnum;
//import com.cosfo.aftersale.model.dto.OrderAfterSaleAuditDTO;
//import com.cosfo.aftersale.model.po.OrderAfterSale;
//import com.cosfo.aftersale.service.OrderAfterSaleService;
//import com.cosfo.manage.common.context.StockRecordType;
//import com.cosfo.manage.common.context.WarehouseTypeEnum;
//import com.cosfo.manage.common.exception.DefaultServiceException;
//import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
//import com.cosfo.manage.common.result.ResultDTO;
//import com.cosfo.manage.common.result.ResultDTOEnum;
//import com.cosfo.manage.common.util.StringUtils;
//import com.cosfo.manage.facade.OfcAfterSaleFacade;
//import com.cosfo.manage.facade.SaleInventoryCommandFacade;
//import com.cosfo.manage.facade.ordercenter.OrderFacade;
//import com.cosfo.manage.merchant.service.MerchantAddressService;
//import com.cosfo.manage.order.model.dto.OrderAfterSaleDeliveryDTO;
//import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
//import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
//import com.cosfo.manage.stock.model.po.Stock;
//import com.cosfo.manage.stock.model.po.StockRecord;
//import com.cosfo.manage.stock.service.StockRecordService;
//import com.cosfo.manage.stock.service.StockService;
//import com.cosfo.ordercenter.client.resp.OrderDTO;
//import com.cosfo.ordercenter.client.resp.OrderItemDTO;
//import com.cosfo.ordercenter.client.resp.OrderItemSnapshotDTO;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import net.summerfarm.wms.instore.enums.SaleStockChangeTypeEnum;
//import net.summerfarm.wms.saleinventory.dto.req.OrderOccupyBySpecifyWarehouseReqDTO;
//import net.summerfarm.wms.saleinventory.dto.req.OrderOccupyReqDTO;
//import net.summerfarm.wms.saleinventory.dto.req.OrderOccupySkuDetailReqDTO;
//import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.LinkedList;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @description
// * @date 2022/8/24 11:13
// */
//@Component
//@Slf4j
//public class ResendAfterSaleExecutor extends AfterSaleAbstractExecutor {
//
//    @Resource
//    private OrderAfterSaleService afterSaleService;
//    @Resource
//    private OfcAfterSaleFacade ofcAfterSaleFacade;
//    @Resource
//    private SaleInventoryCommandFacade saleInventoryCommandFacade;
//    @Resource
//    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
//    @Resource
//    private MerchantAddressService merchantAddressService;
//    @Resource
//    private StockService stockService;
//    @Resource
//    private StockRecordService stockRecordService;
//
//    @Resource
//    private OrderFacade orderFacade;
//
//    @Override
//    public ResultDTO reviewSuccess(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO, LoginContextInfoDTO requestContextInfoDTO, OrderAfterSale afterSale, OrderDTO order) {
//        // 配送仓类型
//        Integer warehouseType = afterSale.getWarehouseType();
//        // 售后类型
//        Integer afterSaleType = afterSale.getAfterSaleType();
//
//        // 补发售后只有【配送后售后】能发起
//        if (OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(afterSaleType)) {
//            throw new DefaultServiceException("配送前售后不支持补发");
//        }
//
//        if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType)) {
//            OrderAfterSaleDeliveryDTO orderAfterSaleDeliveryDTO = new OrderAfterSaleDeliveryDTO();
//            orderAfterSaleDeliveryDTO.setAfterSaleOrderNo(orderAfterSaleAuditDTO.getAfterSaleOrderNo());
//            orderAfterSaleDeliveryDTO.setDeliveryType(orderAfterSaleAuditDTO.getDeliveryType());
//            orderAfterSaleDeliveryDTO.setDeliveryCompany(orderAfterSaleAuditDTO.getDeliveryCompany());
//            orderAfterSaleDeliveryDTO.setDeliveryNo(orderAfterSaleAuditDTO.getDeliveryNo());
//            orderAfterSaleDeliveryDTO.setRemark(orderAfterSaleAuditDTO.getRemark());
//            // rpc调用创建售后补发配送清单
//            ofcAfterSaleFacade.insertAfterSaleLogistics(orderAfterSaleDeliveryDTO, orderAfterSaleAuditDTO.getOperatorName());
//
//            // 变更状态
//            OrderAfterSale update = new OrderAfterSale();
//            update.setId(afterSale.getId());
//            update.setStatus(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getCode());
//            update.setHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
//            update.setOperatorName(orderAfterSaleAuditDTO.getOperatorName());
//            update.setHandleTime(LocalDateTime.now());
//            // 预计补发时间，无仓补发售后没有，只用创建补发物流
//            update.setRecycleTime(orderAfterSaleAuditDTO.getRecycleTime());
//            update.setAmount(orderAfterSaleAuditDTO.getAmount());
//            afterSaleService.updateRecordByIdAndSourceStatus(afterSale.getId(), OrderAfterSaleStatusEnum.UNAUDITED.getCode(), update);
//
//            OrderItemDTO orderItemDTO = orderFacade.getOrderItemById(afterSale.getOrderItemId());
//            if (orderItemDTO == null) {
//                throw new DefaultServiceException("售后对应订单不存在");
//            }
//            // 无仓扣库存
//            try {
//                lockStockForNonWarehouse(afterSale.getTenantId(), orderItemDTO.getItemId(), orderAfterSaleAuditDTO.getAmount(), afterSale.getAfterSaleOrderNo());
//            } catch (Exception e) {
//                log.error("无仓补发扣库存错误，afterSaleNo=" + afterSale.getAfterSaleOrderNo(), e);
//                throw new DefaultServiceException("库存不足，请确认库存足够后再试");
//            }
//
//        } else if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
//            // 仓库库存是否充足校验，补发单冻结库存，调RPC接口，成功更新三方处理中，失败抛异常
//            try {
//                OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO = convertToOrderOccupyBySpecifyWarehouseReqDTO(afterSale.getTenantId(), Long.valueOf(order.getWarehouseNo()), order.getOrderNo(), SaleStockChangeTypeEnum.SAAS_AFTER_SALE, afterSale, orderAfterSaleAuditDTO.getAmount());
//                // 冻结指定仓库库存
//                saleInventoryCommandFacade.orderOccupyBySpecifyWarehouseAndSku(orderOccupyBySpecifyWarehouseReqDTO);
//            } catch (Exception e) {
//                //捕获异常，前面没有sql修改操作，无需sql回滚
//                return ResultDTO.fail(ResultDTOEnum.RESENT_STOCK_LOCK_ERROR);
//            }
//
//            OrderAfterSale update = new OrderAfterSale();
//            update.setId(afterSale.getId());
//            update.setStatus(OrderAfterSaleStatusEnum.THIRD_PROCESSING.getCode());
//            update.setHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
//            update.setOperatorName(requestContextInfoDTO.getTenantName());
//            update.setHandleTime(LocalDateTime.now());
//            // 预计补发时间，自营仓补发售后有值
//            update.setRecycleTime(orderAfterSaleAuditDTO.getRecycleTime());
//            update.setAmount(orderAfterSaleAuditDTO.getAmount());
//            afterSaleService.updateRecordByIdAndSourceStatus(afterSale.getId(), OrderAfterSaleStatusEnum.UNAUDITED.getCode(), update);
//
//            // ofc履约中心会监听三方处理中的状态，去生成出库单，后续补发出库成功，mq消息通知
//        } else {
//            throw new DefaultServiceException("不支持的配送仓类型：" + warehouseType);
//
//        }
//
//        return ResultDTO.success();
//    }
//
//    /**
//     * 转化为冻结库存接口
//     *
//     * @param tenantId
//     * @param warehouseNo
//     * @param orderNo
//     * @param saleStockChangeTypeEnum
//     * @param afterSale
//     * @param qty
//     * @return
//     */
//    private OrderOccupyBySpecifyWarehouseReqDTO convertToOrderOccupyBySpecifyWarehouseReqDTO(Long tenantId, Long warehouseNo, String orderNo, SaleStockChangeTypeEnum saleStockChangeTypeEnum, OrderAfterSale afterSale, Integer qty){
//        OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO = new OrderOccupyBySpecifyWarehouseReqDTO();
//        orderOccupyBySpecifyWarehouseReqDTO.setTenantId(tenantId);
//        orderOccupyBySpecifyWarehouseReqDTO.setWarehouseNo(warehouseNo);
//        orderOccupyBySpecifyWarehouseReqDTO.setOrderNo(afterSale.getAfterSaleOrderNo());
//        orderOccupyBySpecifyWarehouseReqDTO.setOrderType(saleStockChangeTypeEnum.getTypeName());
//        orderOccupyBySpecifyWarehouseReqDTO.setOperatorNo(afterSale.getAfterSaleOrderNo());
//        orderOccupyBySpecifyWarehouseReqDTO.setIdempotentNo(afterSale.getAfterSaleOrderNo());
//        List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOS = new LinkedList<>();
//        OrderOccupySkuDetailReqDTO orderOccupySkuDetailReqDTO = new OrderOccupySkuDetailReqDTO();
//        OrderItemSnapshotDTO orderItemSnapshotDTO = orderFacade.getOrderItemSnapshotById(afterSale.getOrderItemId());
//        orderOccupySkuDetailReqDTO.setSkuCode(getSkuCode(orderItemSnapshotDTO.getSupplierTenantId(), orderItemSnapshotDTO.getSupplierSkuId(), tenantId));
//        orderOccupySkuDetailReqDTO.setOccupyQuantity(qty);
//        orderOccupySkuDetailReqDTOS.add(orderOccupySkuDetailReqDTO);
//        orderOccupyBySpecifyWarehouseReqDTO.setOrderOccupySkuDetailReqDTOS(orderOccupySkuDetailReqDTOS);
//        return orderOccupyBySpecifyWarehouseReqDTO;
//    }
//
//    private void lockStockForNonWarehouse(Long tenantId, Long marketItemId, int changeQty, String afterSaleNo) {
//        Stock stock = stockService.selectByItemId(tenantId, marketItemId);
//
//        Integer beforeAmount = stock.getAmount();
//        Integer afterAmount = stock.getAmount() - changeQty;
//        stock.setAmount(-changeQty);
//        stockService.update(stock);
//
//        StockRecord stockRecord = StockRecord.builder()
//                .tenantId(tenantId)
//                .stockSkuId(marketItemId)
//                .type(StockRecordType.AFTER_SALE.getType())
//                .beforeAmount(beforeAmount)
//                .changeAmount(changeQty)
//                .afterAmount(afterAmount)
//                .recordNo(afterSaleNo)
//                .build();
//        stockRecordService.insert(stockRecord);
//    }
//
//    private OrderOccupyReqDTO convertOrderOccupyReqDTO(OrderAfterSale afterSale, OrderDTO order, Integer qty, Long tenantId) {
//        OrderOccupyReqDTO orderOccupyReqDTO = new OrderOccupyReqDTO();
//        orderOccupyReqDTO.setTenantId(tenantId);
//        orderOccupyReqDTO.setOrderNo(afterSale.getAfterSaleOrderNo());
//        orderOccupyReqDTO.setOrderType(SaleStockChangeTypeEnum.SAAS_AFTER_SALE.getTypeName());
//        orderOccupyReqDTO.setOperatorNo(afterSale.getAfterSaleOrderNo());
//        orderOccupyReqDTO.setIdempotentNo(afterSale.getAfterSaleOrderNo());
//        MerchantAddressResultResp merchantAddress = getMerchantAddress(tenantId, order.getStoreId());
//        orderOccupyReqDTO.setProvince(merchantAddress.getProvince());
//        orderOccupyReqDTO.setCity(merchantAddress.getCity());
//        orderOccupyReqDTO.setArea(merchantAddress.getArea());
//        orderOccupyReqDTO.setAddress(merchantAddress.getAddress());
//        orderOccupyReqDTO.setPoi(merchantAddress.getPoiNote());
//        OrderOccupySkuDetailReqDTO orderOccupySkuDetailReqDTO = new OrderOccupySkuDetailReqDTO();
////        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotMapper.selectByItemId(afterSale.getTenantId(), afterSale.getOrderItemId());
//        OrderItemSnapshotDTO orderItemSnapshotDTO = orderFacade.getOrderItemSnapshotById(afterSale.getOrderItemId());
//        orderOccupySkuDetailReqDTO.setSkuCode(getSkuCode(orderItemSnapshotDTO.getSupplierTenantId(), orderItemSnapshotDTO.getSupplierSkuId(), tenantId));
//        orderOccupySkuDetailReqDTO.setOccupyQuantity(qty);
//        orderOccupyReqDTO.setOrderOccupySkuDetailReqDTOS(Lists.newArrayList(orderOccupySkuDetailReqDTO));
//        return orderOccupyReqDTO;
//    }
//
//    private MerchantAddressResultResp getMerchantAddress(Long tenantId, Long storeId) {
//        MerchantAddressResultResp merchantAddress = merchantAddressService.selectByStoreId(tenantId, storeId);
//        if (merchantAddress == null) {
//            throw new DefaultServiceException("门店地址不存在");
//        }
//        return merchantAddress;
//    }
//
//    private String getSkuCode(Long agentTenantId, Long agentSkuId, Long tenantId) {
//        ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByAgentSkuIdAndAgentTenantIdAndTenantId(agentTenantId, agentSkuId, tenantId);
//        if (productAgentSkuMapping == null || StringUtils.isEmpty(productAgentSkuMapping.getAgentSkuCode())) {
//            throw new DefaultServiceException("找不到SkuCode");
//        }
//        return productAgentSkuMapping.getAgentSkuCode();
//    }
//}
