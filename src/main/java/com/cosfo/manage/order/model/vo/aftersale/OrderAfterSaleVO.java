package com.cosfo.manage.order.model.vo.aftersale;

import com.alibaba.fastjson.annotation.JSONField;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleVO extends OrderAfterSaleDTO {


    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 门店类型
     */
    private Integer storeType;
    /**
     * 联系方式
     */
    private String contactPhone;
    /**
     * 配送仓类型
     */
    private Integer warehouseType;
    /**
     * 仓库编号
     */
    private String warehouseNo;
    /**
     * 配送仓名称
     */
    private String warehouseName;
    /**
     * 仓库服务商
     */
    private String warehouseServiceName;
    /**
     * 供应商租户Id
     */
    private Long supplierTenantId;
    /**
     * 订单创建时间
     */
    private LocalDateTime orderTime;

    /**
     * 下单账号
     */
    private String accountName;
    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 收获地址信息
     */
    private OrderAfterSaleAddressVO orderAfterSaleAddressVO;
    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 下单数量
     */
    private Integer orderAmount;
    /**
     * 下单单价
     */
    private BigDecimal price;
    /**
     * 下单金额
     */
    private BigDecimal orderPrice;
    /**
     * 申请账号
     */
    private String applyAccount;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 回收时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDateTime recycleTime;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 分组名称
     */
    private String merchantStoreGroupName;
}
