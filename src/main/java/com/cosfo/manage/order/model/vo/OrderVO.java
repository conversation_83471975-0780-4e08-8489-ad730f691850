package com.cosfo.manage.order.model.vo;

import com.cosfo.manage.bill.model.vo.PaymentCombinedDetailVO;
import com.cosfo.manage.bill.model.vo.PaymentCombinedOrderDetailVO;
import com.cosfo.manage.order.model.dto.OrderSelfLiftingDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/16
 */
@Data
public class OrderVO {
    /**
     * 订单编号
     */
    private Long orderId;
    /**
     * 租户数量
     */
    private Long tenantId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 门店类型
     */
    private Integer storeType;
    /**
     * 门店联系方式(店长)
     */
    private String storeContactPhone;
    /**
     * 联系方式
     */
    private String contactPhone;
    /**
     * 订单类型 0无仓订单 1,三方仓订单 2自营仓订单
     */
    private Integer warehouseType;
    /**
     * 供应商租户Id
     */
    private Long supplierTenantId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 商品总额
     */
    private BigDecimal productTotalPrice;
    /**
     * 应付金额
     */
    private BigDecimal payablePrice;
    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 税费
     */
    private BigDecimal taxAmount;

    /**
     * 税率，百分位，如5表示5%
     */
    private BigDecimal taxRate;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 订单创建时间
     */
    private LocalDateTime orderTime;
    /**
     * 账号Id
     */
    private Long accountId;
    /**
     * 下单账号
     */
    private String accountName;
    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 开始配送时间
     */
    private LocalDateTime beginDeliveryTime;

    /**
     * 支付方式 1、微信支付 2、账期 3、余额支付 4、支付宝支付 5、无需支付 6、线下支付 7、非现金支付 8、组合支付
     */
    private Integer payType;

    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 仓库名
     */
    private String deliveryWarehouseName;
    /**
     * 服务商
     */
    private String deliveryServiceName;
    /**
     * 总件数
     */
    private Integer totalAmount;
    /**
     * 收获地址信息
     */
    private OrderAddressVO orderAddressVO;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 订单项
     */
    private List<OrderItemVO> orderItemVOS;

    /**
     * 自提信息
     */
    private List<OrderSelfLiftingDTO> orderSelfLiftingDTOS;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 可申请售后时效
     */
    private Integer applyEndTime;
    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;

    /**
     * 门店Id
     */
    private Long storeId;

    /**
     * 分组名称
     */
    private String merchantStoreGroupName;

    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer storeStatus;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 当订单是待配送状态时:0:无需展示;1:立即配送;2:继续配送;
     */
    private Integer deliveryStatus;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 订单类型 0-普通订单,1=组合订单,2=预售订单
     */
    private Integer orderType;
    /**
     * 组合品item_id
     */
    private Integer combineItemId;
    /**
     * 组合订单Id
     */
    private Long combineOrderId;

    /**
     * 成本价
     */
    private BigDecimal supplyPrice;

    /**
     * 是否可以审核订单
     */
    private Boolean canAuditOrder;
    /**
     * 是否可以改单
     */
    private Boolean canChangeOrder;


    /**
     * 订单来源:0：内部系统; 1：openapi调用; 2:总部代下单
     */
    private Integer orderSource;

    /**
     * 计划单编号
     */
    private String planOrderNo;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;

    /**
     * 计划下单方式 create_plan_order-生成计划单(配货单) create_order-创建订单 create_force_plan_order-计划单强制下单(铺货单)
     */
    private String planType;

    /**
     * 签收凭证图片列表，只有三方仓订单tms配送，有数据
     */
    private List<String> signProofList;
    /**
     * 支付凭证
     */
    private String paymentReceipt;
    /**
     * 财务凭证
     */
    private String financialReceipt;

    /**
     * 组合支付 明细
     */
    private List<PaymentCombinedOrderDetailVO> paymentCombinedDetailVOS;

    /**
     * 售后是否可以选择非现金账号退款 false-否 ture-是
     */
    private Boolean canNonCashRefund;

    /**
     * 下单数量
     */
    private Integer orderNum;

    /**
     * 下单重量
     */
    private BigDecimal orderTotalWeight;

    /**
     * 发货数量
     */
    private Integer orderDeliveryNum;

    /**
     * 发货重量
     */
    private BigDecimal orderDeliveryTotalWeight;

    /**
     * 非现金金额
     */
    private BigDecimal nonCashAmount;

}
