package com.cosfo.manage.order.model.dto.aftersale;

import com.alibaba.fastjson.annotation.JSONField;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * desc: 售后DTO
 *
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleBizDTO {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 订单项Id
     */
    private Long orderItemId;

    /**
     * 门店Id
     */
    private Long storeId;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 售后订单编号
     */
    private String afterSaleOrderNo;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 售后类型 0-配送后售后 1-配送前售后
     *
     * @see OrderAfterSaleTypeEnum
     */
    private Integer afterSaleType;

    /**
     * 售后服务类型 1 退款 2 退款录入账单 3 退货退款 4 退货退款录入账单 5 换货 6 补发
     */
    private Integer serviceType;

    /**
     * 申请金额
     */
    private BigDecimal applyPrice;

    /**
     * 售后金额
     */
    private BigDecimal totalPrice;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 售后原因
     */
    private String reason;

    /**
     * 客户留言
     */
    private String userRemark;

    /**
     * 售后凭证照片
     */
    private String proofPicture;

    /**
     * 状态 1待审核 2处理中 3退款中 4已同意 5已拒绝 6已取消 7库存退还失败 8 待退款 9 三方处理中
     *
     * @see OrderAfterSaleStatusEnum
     */
    private Integer status;

    /**
     * 处理结果
     */
    private String handleRemark;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

    /**
     * sku编码
     */
    private Long skuId;

    /**
     * 下单账号
     */
    private String accountName;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 售后类型描述
     */
    private String afterSaleTypeDesc;
    /**
     * 商品名称
     */
    private String title;

    /**
     * 售后服务类型描述
     */
    private String serviceTypeDesc;

    /**
     * 实际退款金额
     */
    private BigDecimal returnPrice;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 状态 1,待审核 2，已成功 3，已失败 4 ，已取消描述
     */
    private String statusDesc;
    /**
     * 下单日期
     */
    private LocalDateTime orderTime;

    /**
     * 回收时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDateTime recycleTime;

    /**
     * 回收详情
     */
    private String recycleDetails;

    /**
     * 回收凭证照片
     */
    private String recyclePicture;

    /**
     * 责任类型0供应商1品牌方2门店
     */
    private Integer responsibilityType;

    /**
     * 审核时间
     */
    private LocalDateTime handleTime;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 售后请求发起来源，mall-小程序发起、manage-manage系统发起、oms-oms系统发起
     * 为空，默认mall-小程序发起
     */
    private String reqSource;


    /**
     * 配送仓类型
     */
    private Integer warehouseType;

    /**
     * 物流信息对象
     */
    private DeliveryInfo deliveryInfo;

    /**
     * 后台售后备注，可以修改
     */
    private String adminRemark;

    /**
     * 后台售后备注更新时间
     */
    private LocalDateTime adminRemarkTime;

    /**
     * 退货退款二次审核说明
     */
    private String secondHandleRemark;

    /**
     * 退回地址Id
     */
    private Long returnAddressId;

    /**
     * 自营仓补发出库信息
     */
    private ResentOutboundInfo resentOutboundInfo;

    /**
     * 自营仓补发出库信息列表
     */
    private List<ResentOutboundInfo> resentOutboundInfoList;


    /**
     * 自营仓退货入库信息
     */
    private ReturnGoodsInboundInfo returnGoodsInboundInfo;

    /**
     * 退货信息-退回地址
     */
    private ReturnGoodsInfo returnGoodsInfo;

    /**
     * 自营退货库存仓no
     */
    private String returnWarehouseNo;

    /**
     * 自营退货仓名称
     */
    private String returnWarehouseName;

    /**
     * 供应价
     */
    private BigDecimal supplyPrice;

    /**
     * 单价
     */
    private BigDecimal payablePrice;

    /**
     * 供应商申请退款金额
     */
    private BigDecimal supplyApplyRefundPrice;

    /**
     * 供应商实退金额
     */
    private BigDecimal supplyTotalRefundPrice;

    /**
     * 服务商审核时间
     */
    private LocalDateTime serviceProviderAuditTime;

    /**
     * 组合支付方式明细
     */
    private List<Integer> combinedPayTypes;


    /**
     *  退款凭证
     */
    private String refundReceipt;
    @Data
    public static class DeliveryInfo {

        /**
         * 配送方式 0其他 1物流快递
         */
        private Integer deliveryType;

        /**
         * 物流公司
         */
        private String deliveryCompany;

        /**
         * 配送单号
         */
        private String deliveryNo;

        /**
         * 备注
         */
        private String remark;

        /**
         * 图片
         */
        private String pics;

        /**
         * 物流创建时间（买家退回时间）
         */
        private Date createTime;

        /**
         * 如果有物流快递，物流跳转链接
         */
        private String jumpLink;

        /**
         * 批次号
         */
        private String batchNo;

        /**
         * 出库单号
         */
        private String outBoundNo;

        /**
         * 库存号
         */
        private Integer warehouseNo;

        /**
         * 库存仓名称
         */
        private String warehouseName;

        /**
         * 库存仓服务商名称
         */
        private String warehouseServiceName;

        /**
         * 订单/售后单号
         */
        private String orderNo;
    }

    @Data
    public static class ResentOutboundInfo {

        /**
         * 配送方式 0其他 1物流快递
         */
        private Integer deliveryType;

        /**
         * 物流公司
         */
        private String deliveryCompany;

        /**
         * 配送单号
         */
        private String deliveryNo;

        /**
         * 备注
         */
        private String remark;

        /**
         * 如果有物流快递，物流跳转链接
         */
        private String jumpLink;

        /**
         * 批次号
         */
        private String batchNo;

        /**
         * 出库单号
         */
        private String outBoundNo;

        /**
         * 配送数量
         */
        private Integer quantity;

        /**
         * 库存号
         */
        private Integer warehouseNo;

        /**
         * 库存仓名称
         */
        private String warehouseName;

        /**
         * 库存仓服务商名称
         */
        private String warehouseServiceName;

        /**
         * 商品id
         */
        private String itemId;

        /**
         * 商品id
         */
        private String title;
        /**
         * 规格
         */
        private String specification;
        /**
         * 规格单位
         */
        private String specificationUnit;

        /**
         * 商品主图
         */
        private String mainPicture;

        /**
         * 鲜沐sku编码
         */
        private String skuCode;

        /**
         * 订单/售后单
         */
        private String orderNo;

    }

    @Data
    public static class ReturnGoodsInboundInfo {

        /**
         * 入库时间
         */
        private LocalDateTime inBoundTime;

        /**
         * 入库凭证照片
         */
        private String inboundPics;


        /**
         * 计划量
         */
        private Integer quantity;


        /**
         * 实际入库量
         */
        private Integer actualQuantity;

        /**
         * 原因，入库说明
         */
        private String reason;

        /**
         * 入库状态,0-未完成，1-已完成
         *
         */
        private Integer inboundStatus;

    }

    @Data
    public static class ReturnGoodsInfo {
        /**
         * 无仓退回地址主键id
         */
        private Long returnAddressId;

        /**
         * 退货买家的退回地址
         */
        private String address;

        /**
         * 联系人
         */
        private String contact;

        /**
         * 手机号
         */
        private String mobile;
    }

}
