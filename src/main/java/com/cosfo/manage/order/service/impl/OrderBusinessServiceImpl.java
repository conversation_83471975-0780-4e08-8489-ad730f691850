package com.cosfo.manage.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.agentorder.dao.PlanOrderDao;
import com.cosfo.manage.agentorder.model.po.PlanOrder;
import com.cosfo.manage.bill.mapper.PaymentCombinedOrderDetailMapper;
import com.cosfo.manage.bill.mapper.PaymentItemMapper;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.dto.PaymentItemDTO;
import com.cosfo.manage.bill.model.po.PaymentCombinedDetail;
import com.cosfo.manage.bill.model.po.PaymentItem;
import com.cosfo.manage.bill.model.vo.BillOrderVO;
import com.cosfo.manage.bill.model.vo.PaymentCombinedOrderDetailVO;
import com.cosfo.manage.bill.service.PaymentCombinedDetailService;
import com.cosfo.manage.bizlog.convert.BizLogConvert;
import com.cosfo.manage.bizlog.model.dto.BizLogQueryDTO;
import com.cosfo.manage.bizlog.model.vo.BizLogListVO;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.common.config.WeChatOaConfig;
import com.cosfo.manage.common.constant.*;
import com.cosfo.manage.common.context.OrderStatusEnum;
import com.cosfo.manage.common.context.PayTypeEnum;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.context.order.BatchDeliveryEnum;
import com.cosfo.manage.common.context.order.DeliveryButtonStatusEnum;
import com.cosfo.manage.common.context.order.NoWarehouseDeliveryTypeEnum;
import com.cosfo.manage.common.context.warehouse.WarehouseQueryEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.*;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import com.cosfo.manage.facade.*;
import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import com.cosfo.manage.facade.ofc.OfcDeliveryQueryFacade;
import com.cosfo.manage.facade.ordercenter.*;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.market.model.dto.MarketClassificationDTO;
import com.cosfo.manage.market.model.dto.MarketItemDTO;
import com.cosfo.manage.market.model.dto.MarketItemInfoDTO;
import com.cosfo.manage.market.model.dto.MarketItemQueryDTO;
import com.cosfo.manage.market.model.po.MarketItem;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.cosfo.manage.market.service.MarketClassificationService;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.market.service.MarketService;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryAddressResultDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.manage.merchant.model.vo.MerchantStoreAccountVO;
import com.cosfo.manage.merchant.service.*;
import com.cosfo.manage.order.convert.OrderAddressConvert;
import com.cosfo.manage.order.convert.OrderConvert;
import com.cosfo.manage.order.mapper.OrderSelfLiftingMapper;
import com.cosfo.manage.order.model.dto.*;
import com.cosfo.manage.order.model.po.OrderSelfLifting;
import com.cosfo.manage.order.model.vo.*;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.supplier.service.SupplierService;
import com.cosfo.manage.tenant.mapper.TenantFundAccountConfigMapper;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.model.po.*;
import com.cosfo.manage.tenant.model.vo.TenantAccountVO;
import com.cosfo.manage.tenant.model.vo.TenantCommonConfigVO;
import com.cosfo.manage.tenant.service.*;
import com.cosfo.message.client.enums.JumpUrlTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.TemplateWechatEnum;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderItemCommandProvider;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.req.OrderItemUpdateDeliveryQuantityReq.OrderItemQuantity;
import com.cosfo.ordercenter.client.req.event.OrderAuditReq;
import com.cosfo.ordercenter.client.req.event.OrderCloseReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleEnableResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.*;
import com.cosfo.summerfarm.model.dto.SummerfarmOrderOutDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.summerfarm.ofc.client.req.QueryFulfillmentDeliveryReq;
import net.summerfarm.ofc.client.req.QueryFulfillmentWaitDeliveryReq;
import net.summerfarm.ofc.client.req.StartFulfillmentReq;
import net.summerfarm.ofc.client.resp.FulfillmentDeliveryResp;
import net.summerfarm.ofc.client.resp.FulfillmentWaitDeliveryResp;
import net.summerfarm.ofc.client.resp.StartFulfillmentErrorOrderResp;
import net.summerfarm.ofc.client.resp.StartFulfillmentResp;
import net.summerfarm.ofc.client.resp.fulfillment.OrderDeliveryInfoStandardResp;
import net.summerfarm.ofc.client.resp.fulfillment.OrderTmsDeliverySiteStandardDTO;
import net.summerfarm.ofc.client.resp.goodssupply.GoodsSupplyOrderDetailQueryResp;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.resp.FastMallResp;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.log.annation.BizLogRecord;
import net.xianmu.log.config.BizLogRecordContext;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.apache.commons.io.FileUtils;
import org.apache.commons.math3.util.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.Collator;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 描述: 订单服务类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
@Slf4j
@Service
public class OrderBusinessServiceImpl implements OrderBusinessService {

    @Resource
    @Lazy
    private MerchantStoreService merchantStoreService;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @Resource
    private TenantService tenantService;
    @Resource
    private CommonService commonService;
    @Resource
    private OrderSelfLiftingMapper orderSelfLiftingMapper;
    @Resource
    private MarketClassificationService marketClassificationService;
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private MarketService marketService;
    @Resource
    private OfcFacade ofcFacade;
    @Resource
    private MerchantStoreGroupMappingService merchantStoreGroupMappingService;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;
    @Resource
    private FulfillmentOrderOperateFacade fulfillmentOrderOperateFacade;
    @Resource
    private TenantAccountService tenantAccountService;
    @Resource
    private FulfillmentOrderQueryFacade fulfillmentOrderQueryFacade;
    @Resource
    private FastMallServiceQueryFacade fastMallServiceQueryFacade;
    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private AuthRoleService authRoleService;
    @Resource
    private TenantAccountSupplierMappingService tenantAccountSupplierMappingService;
    @Resource
    private TenantProvider tenantProvider;
    @Resource
    private ProductFacade productFacade;
    @Resource
    private TenantCompanyService tenantCompanyService;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;
    @Resource
    private BizLogFacade bizLogFacade;
    @DubboReference
    private OrderCommandProvider orderCommandProvider;
    @DubboReference
    private OrderItemCommandProvider orderItemCommandProvider;
    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;
    @Resource
    private AuthWechatFacade wechatFacade;
    @Resource
    private MessageServiceFacade messageServiceFacade;
    @Resource
    private WeChatOaConfig weChatOaConfig;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private TenantFlowSchemeService tenantFlowSchemeService;
    @Resource
    private PlanOrderDao planOrderDao;
    @Resource
    private OrderStatisticsQueryFacade orderStatisticsQueryFacade;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemSnapshotQueryFacade orderItemSnapshotQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;
    @Resource
    private OrderAddressQueryFacade orderAddressQueryFacade;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;
    @Resource
    private OfcDeliveryQueryFacade ofcDeliveryQueryFacade;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentCombinedOrderDetailMapper paymentCombinedOrderDetailMapper;
    @Resource
    private TenantFundAccountConfigMapper tenantFundAccountConfigMapper;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;
    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;

    /**
     * 中文校验
     */
    private static final Pattern CN_PATTERN = Pattern.compile("[\u4e00-\u9fa5]");
    /**
     * 售后取消状态，已取消、已拒绝
     */
    private static List<Integer> AFTERSALE_STATUS_LIST_FOR_CLOSE = Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_FAILED.getValue(), OrderAfterSaleStatusEnum.CANCEL.getValue());


    @Override
    public PageInfo<OrderVO> list(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO requestContextInfoDTO) {
        // 处理查询条件
        orderQueryDTO.setTenantId(requestContextInfoDTO.getTenantId());
        boolean hasResultFlag = dealQueryConditions(orderQueryDTO, requestContextInfoDTO);
        if (!hasResultFlag) {
            return PageInfo.emptyPageInfo();
        }

        OrderQueryReq orderPageQueryReq = OrderQueryReq.builder()
                .tenantId(orderQueryDTO.getTenantId())
                .orderId(orderQueryDTO.getOrderId())
                .storeType(orderQueryDTO.getStoreType())
                .storeName(orderQueryDTO.getStoreName())
                .orderNo(orderQueryDTO.getOrderNo())
                .warehouseType(orderQueryDTO.getWarehouseType())
                .phone(orderQueryDTO.getPhone())
//                .createStartTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getStartTime()))
//                .createEndTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getEndTime()))
                .storeIds(orderQueryDTO.getStoreIds())
                .accountIds(orderQueryDTO.getAccountIds())
                .itemIds(orderQueryDTO.getItemIds())
                .skuId(orderQueryDTO.getSkuId())
                .payType(orderQueryDTO.getPayType())
                .orderItemIds(orderQueryDTO.getOrderItemIds())
                .warehouseNo(orderQueryDTO.getWarehouseNo() == null ? null : orderQueryDTO.getWarehouseNo().toString())
                .supplierTenantIds(orderQueryDTO.getSupplierIds())
                .orderSource(orderQueryDTO.getOrderSource())
                .planOrderNo(orderQueryDTO.getPlanOrderNo())
                .orderType(orderQueryDTO.getOrderType())
                .sortOrderIdAsc(orderQueryDTO.getSortOrderIdAsc())
                .timeQueryType(orderQueryDTO.getTimeQueryType())
                .queryStartTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getStartTime()))
                .queryEndTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getEndTime()))
                .build();

        if (orderQueryDTO.getStatus() != null && orderQueryDTO.getStatus() == 10) {
            orderPageQueryReq.setStatusList(Lists.newArrayList(OrderStatusEnum.WAITING_DELIVERY.getCode(), OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode()));
        } else if (orderQueryDTO.getStatus() != null && orderQueryDTO.getStatus() == 4) {
            orderPageQueryReq.setStatusList(Lists.newArrayList(OrderStatusEnum.DELIVERING.getCode(), OrderStatusEnum.OUT_OF_STORAGE.getCode()));
        } else {
            orderPageQueryReq.setStatus(orderQueryDTO.getStatus());
        }

        orderPageQueryReq.setPageNum(orderQueryDTO.getPageIndex());
        orderPageQueryReq.setPageSize(orderQueryDTO.getPageSize());
        PageInfo<OrderVO> orderVOPageInfo = queryOrderPage(orderPageQueryReq);
        if (orderVOPageInfo == null || CollectionUtils.isEmpty(orderVOPageInfo.getList())) {
            return PageInfo.emptyPageInfo();
        }

        List<OrderVO> orderVOS = orderVOPageInfo.getList();

        // 查询门店信息、查询账号信息
        List<Long> storeIds = orderVOS.stream().map(OrderVO::getStoreId).distinct().collect(Collectors.toList());
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(storeIds);
        Map<Long, MerchantStoreResultResp> storeMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, Function.identity(), (v1, v2) -> v1));
        List<Long> accountIds = orderVOS.stream().map(OrderVO::getAccountId).distinct().collect(Collectors.toList());
        List<MerchantStoreAccountResultResp> merchantStoreAccountList = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(accountIds);
        Map<Long, MerchantStoreAccountResultResp> accountMap = merchantStoreAccountList.stream().collect(Collectors.toMap(MerchantStoreAccountResultResp::getId, Function.identity(), (v1, v2) -> v1));


        // 查询订单项信息
        List<Long> orderIds = orderVOS.stream().map(OrderVO::getOrderId).collect(Collectors.toList());

        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(orderIds);
        orderItemQueryReq.setSupplierIds(orderQueryDTO.getSupplierIds());
        List<OrderItemVO> orderItems = orderItemQueryFacade.queryOrderItemListVO(orderItemQueryReq);
        if (CollectionUtils.isEmpty(orderItems)) {
            return PageInfo.emptyPageInfo();
        }

        // 供应商信息
        Map<Long, SupplierInfoDTO> supplierInfoDTOMap = supplierService.batchQuerySupplierMap(requestContextInfoDTO.getTenantId(), orderItems.stream().map(OrderItemVO::getSupplierTenantId).collect(Collectors.toList()));

        // 订单商品项信息
        Map<Long, List<OrderItemVO>> orderItemMap = orderItems.stream().collect(Collectors.groupingBy(OrderItemVO::getOrderId));

        // 订单售后信息
        Map<Long, List<OrderAfterSaleResp>> orderAfterSaleMap = Collections.emptyMap();
        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setTenantId(requestContextInfoDTO.getTenantId());
        req.setOrderIds(orderIds);
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryList(req);
        if (!CollectionUtils.isEmpty(orderAfterSaleResps)) {
            orderAfterSaleMap = orderAfterSaleResps.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));
        }

        // 查询门店分组
        Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(requestContextInfoDTO.getTenantId(), storeIds);
        // 查询门店地址
        List<OrderAddressResp> orderAddressResps = orderAddressQueryFacade.queryByOrderIds(requestContextInfoDTO.getTenantId(), orderIds);
        Map<Long, OrderAddressResp> orderAddressMap = orderAddressResps.stream().collect(Collectors.toMap(OrderAddressResp::getOrderId, item -> item));

        List<Integer> warehouseNos = orderVOS.stream()
                .map(OrderVO::getWarehouseNo)
                .filter(warehouseNo -> !StringUtils.isEmpty(warehouseNo))
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        Map<Integer, String> warehouseStorageMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(warehouseNos)) {
            // 查询自营仓名称
            List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(requestContextInfoDTO.getTenantId(), WarehouseSourceEnum.SAAS_WAREHOUSE, warehouseNos);
            warehouseStorageMap = warehouseStorageResps.stream()
                    .collect(Collectors.toMap(WarehouseStorageResp::getWarehouseNo, WarehouseStorageResp::getWarehouseName, (v1, v2) -> v1));
        }


        // 售后详情
        Map<Long, Integer> afterSaleAmountMap = new HashMap<>();
        Map<Long, Integer> afterSaleCountMap = new HashMap<>();
        Map<Long, BigDecimal> afterSalePriceMap = new HashMap<>();
        for (Long orderItemId : orderAfterSaleMap.keySet()) {
            List<OrderAfterSaleResp> afterSaleList = orderAfterSaleMap.get(orderItemId);
            afterSaleAmountMap.put(orderItemId, afterSaleList.size());
            afterSaleList = afterSaleList.stream().filter(e -> !AFTERSALE_STATUS_LIST_FOR_CLOSE.contains(e.getStatus())).collect(Collectors.toList());
            afterSaleCountMap.put(orderItemId, afterSaleList.stream().mapToInt(OrderAfterSaleResp::getAmount).sum());
            afterSalePriceMap.put(orderItemId, afterSaleList.stream().map(OrderAfterSaleResp::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        Map<Long, Boolean> storeIdAuditFlagMap = tenantFlowSchemeService.canAuditStoreIdsByAccountIdAndBizType(requestContextInfoDTO.getAuthUserId(), storeIds, FlowRuleAuditBizTypeEnum.ORDER_AUDIT);

        //假如是组合支付 查询支付明细
        Map<Long, List<PaymentCombinedOrderDetailVO>> paymentCombinedDetailMap = null;
        Set<Long> combinedPaymentOrderIds = orderVOS.stream().filter(orderVO -> orderVO.getPayType().equals(com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getCode()))
                .map(OrderVO::getOrderId).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(combinedPaymentOrderIds)) {
            List<PaymentCombinedOrderDetailVO> paymentCombinedDetailVOS = paymentCombinedOrderDetailMapper.getListByOrderIds(combinedPaymentOrderIds);
            paymentCombinedDetailMap = paymentCombinedDetailVOS.stream().collect(Collectors.groupingBy(PaymentCombinedOrderDetailVO::getOrderId));
        }

        for (OrderVO orderVO : orderVOS) {
            //组装组合支付信息
            if (CollectionUtil.isNotEmpty(paymentCombinedDetailMap) && paymentCombinedDetailMap.containsKey(orderVO.getOrderId())) {
                List<PaymentCombinedOrderDetailVO> paymentCombinedDetailVOS = paymentCombinedDetailMap.get(orderVO.getOrderId());
                paymentCombinedDetailVOS.forEach(e -> {
                    e.setTradeTypeDesc(PaymentTradeTypeEnum.getPaymentTradeType(e.getTradeType()) == null ?
                            null : PaymentTradeTypeEnum.getPaymentTradeType(e.getTradeType()).getDetailDesc());
                    e.setPayType(PaymentTradeTypeEnum.getPaymentTradeType(e.getTradeType()) == null ?
                            null : PaymentTradeTypeEnum.getPaymentTradeType(e.getTradeType()).getPayType());
                });
                orderVO.setPaymentCombinedDetailVOS(paymentCombinedDetailVOS);
            }

            // 订单是否可审核状态
            boolean isOrderAuditStatus = OrderStatusEnum.WAIT_AUDIT.getCode().equals(orderVO.getStatus());
            orderVO.setCanAuditOrder(isOrderAuditStatus && storeIdAuditFlagMap.get(orderVO.getStoreId()));

            // 订单状态转换
            OrderStatusEnum.transferOrderStatus(orderVO);
            MerchantStoreAccountResultResp resp = accountMap.get(orderVO.getAccountId());
            orderVO.setContactPhone(Optional.ofNullable(resp).map(MerchantStoreAccountResultResp::getPhone).orElse(null));
            MerchantStoreResultResp merchantStoreResultResp = storeMap.get(orderVO.getStoreId());
            if (Objects.nonNull(merchantStoreResultResp)) {
                orderVO.setStoreName(merchantStoreResultResp.getStoreName());
                orderVO.setStoreType(merchantStoreResultResp.getType());
                orderVO.setStoreStatus(merchantStoreResultResp.getStatus());
                orderVO.setStoreNo(merchantStoreResultResp.getStoreNo());
            }

            List<OrderItemVO> orderItemVOS = orderItemMap.get(orderVO.getOrderId());
            if (CollectionUtil.isEmpty(orderItemVOS)) {
                continue;
            }
            orderVO.setSupplyPrice(orderItemVOS.stream().map(item -> item.getSupplyPrice() != null ? item.getSupplyPrice().multiply(BigDecimal.valueOf(item.getAmount())) : BigDecimal.ZERO).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
            ;
            Integer deliveryStatus = builderDeliveryStatus(orderItemVOS, orderVO.getStatus(), orderVO.getWarehouseType());
            orderVO.setDeliveryStatus(deliveryStatus);

            for (OrderItemVO orderItemVO : orderItemVOS) {
                orderItemVO.setAfterSaleAmount(Optional.ofNullable(afterSaleAmountMap.get(orderItemVO.getId())).orElse(NumberConstants.ZERO));
                orderItemVO.setAfterSaleCount(Optional.ofNullable(afterSaleCountMap.get(orderItemVO.getId())).orElse(NumberConstants.ZERO));
                orderItemVO.setAfterSalePrice(Optional.ofNullable(afterSalePriceMap.get(orderItemVO.getId())).orElse(BigDecimal.ZERO));

                SupplierInfoDTO supplierInfoDTO = supplierInfoDTOMap.get(orderItemVO.getSupplierTenantId());
                orderItemVO.setSupplierName(Objects.isNull(supplierInfoDTO) ? "" : supplierInfoDTO.getSupplierName());
            }
            WarehouseTypeEnum warehouseTypeEnum = WarehouseTypeEnum.getByCode(orderVO.getWarehouseType());
            orderVO.setDeliveryWarehouseName(warehouseTypeEnum == WarehouseTypeEnum.PROPRIETARY ? warehouseStorageMap.get(Integer.valueOf(orderVO.getWarehouseNo())) : warehouseTypeEnum.getDesc());
            orderVO.setOrderItemVOS(orderItemVOS);
            orderVO.setMerchantStoreGroupName(groupMap.get(orderVO.getStoreId()));
            OrderAddressResp orderAddress = orderAddressMap.get(orderVO.getOrderId());
            orderVO.setOrderAddressVO(OrderAddressConvert.INSTANCE.respToVO(orderAddress));
        }

        return orderVOPageInfo;
    }

    @Override
    public PageInfo<OrderVO> list(OrderQueryDTO orderQueryDTO, Long tenantId) {
        // 处理查询条件
        orderQueryDTO.setTenantId(tenantId);
        OrderQueryReq orderPageQueryReq = OrderQueryReq.builder()
                .tenantId(orderQueryDTO.getTenantId())
                .orderId(orderQueryDTO.getOrderId())
                .orderNos(orderQueryDTO.getOrderNos())
                .storeType(orderQueryDTO.getStoreType())
                .storeName(orderQueryDTO.getStoreName())
                .orderNo(orderQueryDTO.getOrderNo())
                .warehouseType(orderQueryDTO.getWarehouseType())
                .phone(orderQueryDTO.getPhone())
                .storeIds(orderQueryDTO.getStoreIds())
                .accountIds(orderQueryDTO.getAccountIds())
                .itemIds(orderQueryDTO.getItemIds())
                .skuId(orderQueryDTO.getSkuId())
                .payType(orderQueryDTO.getPayType())
                .orderItemIds(orderQueryDTO.getOrderItemIds())
                .warehouseNo(orderQueryDTO.getWarehouseNo() == null ? null : orderQueryDTO.getWarehouseNo().toString())
                .supplierTenantIds(orderQueryDTO.getSupplierIds())
                .orderSource(orderQueryDTO.getOrderSource())
                .planOrderNo(orderQueryDTO.getPlanOrderNo())
                .orderType(orderQueryDTO.getOrderType())
                .sortOrderIdAsc(orderQueryDTO.getSortOrderIdAsc())
                .timeQueryType(orderQueryDTO.getTimeQueryType())
                .queryStartTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getStartTime()))
                .queryEndTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getEndTime()))
                .statusList(orderQueryDTO.getOrderStatusList())
                .build();


        orderPageQueryReq.setPageNum(orderQueryDTO.getPageIndex());
        orderPageQueryReq.setPageSize(orderQueryDTO.getPageSize());
        PageInfo<OrderVO> orderVOPageInfo = queryOrderPage(orderPageQueryReq);
        if (orderVOPageInfo == null || CollectionUtils.isEmpty(orderVOPageInfo.getList())) {
            return PageInfo.emptyPageInfo();
        }

        List<OrderVO> orderVOS = orderVOPageInfo.getList();

        // 查询门店信息、查询账号信息
        List<Long> storeIds = orderVOS.stream().map(OrderVO::getStoreId).distinct().collect(Collectors.toList());
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(storeIds);
        Map<Long, MerchantStoreResultResp> storeMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, Function.identity(), (v1, v2) -> v1));
        List<Long> accountIds = orderVOS.stream().map(OrderVO::getAccountId).distinct().collect(Collectors.toList());
        List<MerchantStoreAccountResultResp> merchantStoreAccountList = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(accountIds);
        Map<Long, MerchantStoreAccountResultResp> accountMap = merchantStoreAccountList.stream().collect(Collectors.toMap(MerchantStoreAccountResultResp::getId, Function.identity(), (v1, v2) -> v1));


        // 查询订单项信息
        List<Long> orderIds = orderVOS.stream().map(OrderVO::getOrderId).collect(Collectors.toList());

        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(orderIds);
        orderItemQueryReq.setSupplierIds(orderQueryDTO.getSupplierIds());
        List<OrderItemVO> orderItems = orderItemQueryFacade.queryOrderItemListVO(orderItemQueryReq);
        if (CollectionUtils.isEmpty(orderItems)) {
            return PageInfo.emptyPageInfo();
        }
        // 订单商品项信息
        Map<Long, List<OrderItemVO>> orderItemMap = orderItems.stream().collect(Collectors.groupingBy(OrderItemVO::getOrderId));

        // 查询门店地址
        List<OrderAddressResp> orderAddressResps = orderAddressQueryFacade.queryByOrderIds(tenantId, orderIds);
        Map<Long, OrderAddressResp> orderAddressMap = orderAddressResps.stream().collect(Collectors.toMap(OrderAddressResp::getOrderId, item -> item));

        for (OrderVO orderVO : orderVOS) {
            // 订单状态转换
            OrderStatusEnum.transferOrderStatus(orderVO);
            MerchantStoreAccountResultResp resp = accountMap.get(orderVO.getAccountId());
            orderVO.setContactPhone(Optional.ofNullable(resp).map(MerchantStoreAccountResultResp::getPhone).orElse(null));
            MerchantStoreResultResp merchantStoreResultResp = storeMap.get(orderVO.getStoreId());
            if (Objects.nonNull(merchantStoreResultResp)) {
                orderVO.setStoreName(merchantStoreResultResp.getStoreName());
                orderVO.setStoreType(merchantStoreResultResp.getType());
                orderVO.setStoreStatus(merchantStoreResultResp.getStatus());
                orderVO.setStoreNo(merchantStoreResultResp.getStoreNo());
            }

            List<OrderItemVO> orderItemVOS = orderItemMap.get(orderVO.getOrderId());
            if (CollectionUtil.isEmpty(orderItemVOS)) {
                continue;
            }
            orderVO.setSupplyPrice(orderItemVOS.stream().map(item -> item.getSupplyPrice() != null ? item.getSupplyPrice().multiply(BigDecimal.valueOf(item.getAmount())) : BigDecimal.ZERO).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            Integer deliveryStatus = builderDeliveryStatus(orderItemVOS, orderVO.getStatus(), orderVO.getWarehouseType());
            orderVO.setDeliveryStatus(deliveryStatus);

            orderVO.setOrderItemVOS(orderItemVOS);
            OrderAddressResp orderAddress = orderAddressMap.get(orderVO.getOrderId());
            orderVO.setOrderAddressVO(OrderAddressConvert.INSTANCE.respToVO(orderAddress));
        }
        return orderVOPageInfo;
    }

    @Override
    public PageInfo<OrderVO> queryOrderPage(OrderQueryReq req) {
        PageInfo<OrderResp> orderRespPageInfo = orderQueryFacade.queryOrderPage(req);
        if (orderRespPageInfo == null || CollectionUtils.isEmpty(orderRespPageInfo.getList())) {
            return PageInfo.emptyPageInfo();
        }
        List<OrderVO> orderVOS = OrderConvert.INSTANCE.convertResp2VOs(orderRespPageInfo.getList());
        if (CollectionUtils.isEmpty(orderVOS)) {
            return PageInfo.emptyPageInfo();
        }

        return PageInfoHelper.pageInfoCopy(orderRespPageInfo, orderVOS);
    }


    /**
     * 判断订单的按钮配送状态
     *
     * @param status
     * @return
     */
    private Integer builderDeliveryStatus(List<OrderItemVO> orderItemVOS, Integer status, Integer warehouseType) {
        boolean waitDeliveryNoWarehouse = OrderStatusEnum.isWaitStatusOrder(status) && WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType);
        Integer deliveryStatus = waitDeliveryNoWarehouse ? DeliveryButtonStatusEnum.GO_DELIVERY.getType() : DeliveryButtonStatusEnum.NO_SHOW.getType();
        if (CollectionUtil.isNotEmpty(orderItemVOS) && waitDeliveryNoWarehouse) {
            // 当 deliveryQuantitySum > 0 且 ！= amountSum 就展示继续配送
            int deliveryQuantitySum = orderItemVOS.stream().mapToInt(OrderItemVO::getDeliveryQuantity).sum();
            int amountSum = orderItemVOS.stream().mapToInt(OrderItemVO::getAmount).sum();
            if (deliveryQuantitySum != 0 && amountSum > deliveryQuantitySum) {
                deliveryStatus = DeliveryButtonStatusEnum.CONTINUE_DELIVERY.getType();
            }
        }
        return deliveryStatus;
    }

    /**
     * 批量查询订单已配送清单
     *
     * @param orderNos
     * @return
     */
    private Map<String, List<FulfillmentDeliveryResp>> batchQueryOrderDelivery(List<String> orderNos) {
        List<FulfillmentDeliveryResp> respList = Lists.newArrayList();
        List<List<String>> lists = Lists.partition(orderNos, NumberConstant.FIFTY);
        for (List<String> orderNoList : lists) {
            QueryFulfillmentDeliveryReq queryFulfillmentDeliveryReq = new QueryFulfillmentDeliveryReq();
            queryFulfillmentDeliveryReq.setOrderNoList(orderNoList);
            List<FulfillmentDeliveryResp> fulfillmentDeliveryResps = fulfillmentOrderQueryFacade.queryOrderDelivery(queryFulfillmentDeliveryReq);
            if (CollectionUtil.isEmpty(fulfillmentDeliveryResps)) {
                continue;
            }
            respList.addAll(fulfillmentDeliveryResps);
        }
        return respList.stream().collect(Collectors.groupingBy(FulfillmentDeliveryResp::getOrderNo));
    }

    @Override
    public CommonResult<OrderVO> detail(Long orderId) {
        AssertCheckParams.notNull(orderId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        OrderVO orderVO = queryOrderVOByOrderId(orderId);
        AssertCheckParams.notNull(orderVO, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单不存在");
        OrderTenantIsolateCheckUtil.check(orderVO);
        // 订单改单
        boolean isOrderChangeStatus = OrderStatusEnum.WAIT_AUDIT.getCode().equals(orderVO.getStatus()) || OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderVO.getStatus());
        boolean isOrderChangeWarehouseType = WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(orderVO.getWarehouseType()) || WarehouseTypeEnum.PROPRIETARY.getCode().equals(orderVO.getWarehouseType());
        orderVO.setCanChangeOrder(isOrderChangeStatus && isOrderChangeWarehouseType && isSupportChangeOrder(UserLoginContextUtils.getTenantId()));
        // 订单审核
        boolean isOrderAuditStatus = OrderStatusEnum.WAIT_AUDIT.getCode().equals(orderVO.getStatus());
        orderVO.setCanAuditOrder(isOrderAuditStatus && tenantFlowSchemeService.canAuditByAccountIdAndBizType(UserLoginContextUtils.getAuthUserId(), orderVO.getStoreId(), FlowRuleAuditBizTypeEnum.ORDER_AUDIT));

        // 订单状态转换
        OrderStatusEnum.transferOrderStatus(orderVO);
        // 查询供应商信息
        List<Long> supplierTenantIds = Arrays.asList(orderVO.getSupplierTenantId());
        List<Tenant> tenants = tenantService.querySupplierInfoBySupplierTenantIds(supplierTenantIds);
        orderVO.setSupplierName(tenants.get(0).getTenantName());
        // 获取下单账号信息
        MerchantStoreAccountVO merchantStoreAccountVO = merchantStoreAccountService.queryAccountInfo(orderVO.getAccountId());
        StringBuffer accountName = new StringBuffer(merchantStoreAccountVO.getAccountName())
                .append("(").append(merchantStoreAccountVO.getPhone()).append(")");
        orderVO.setAccountName(accountName.toString());
        // 查询订单地址信息
        OrderAddressResp orderAddressResp = orderAddressQueryFacade.queryByOrderId(orderVO.getTenantId(), orderId);
        orderVO.setOrderAddressVO(OrderAddressConvert.INSTANCE.respToVO(orderAddressResp));

        // 获取订单项信息
        List<OrderItemVO> orderItemVOS = orderItemQueryFacade.queryOrderItemByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderItemVOS)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "订单项不存在");
        }


        List<Long> itemIds = orderItemVOS.stream().map(OrderItemVO::getItemId).collect(Collectors.toList());
        Map<Long, MarketItemDTO> marketItemMap = marketItemService.getMapByItemIds(itemIds);
        // 门店分组
        Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(orderVO.getTenantId(), Arrays.asList(orderVO.getStoreId()));
        orderVO.setMerchantStoreGroupName(groupMap.get(orderVO.getStoreId()));
        List<MerchantDeliveryAddressResultDTO> merchantAddressDTOS = merchantAddressService.selectByStoreIds(orderVO.getTenantId(), Lists.newArrayList(orderVO.getStoreId()));
        if (CollectionUtil.isNotEmpty(merchantAddressDTOS)) {
            orderVO.setStoreContactPhone(merchantAddressDTOS.get(NumberConstant.ZERO).getContactPhone());
        }

        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setTenantId(orderVO.getTenantId());
        req.setOrderIds(Lists.newArrayList(orderId));
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryList(req);
        Map<Long, List<OrderAfterSaleResp>> orderAfterSaleMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderAfterSaleResps)) {
            orderAfterSaleMap = orderAfterSaleResps.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));
        }

        // 售后详情
        Map<Long, Integer> afterSaleAmountMap = new HashMap<>();
        Map<Long, Integer> afterSaleCountMap = new HashMap<>();
        Map<Long, BigDecimal> afterSalePriceMap = new HashMap<>();

        //配送前售后数量
        Map<Long, Integer> preDeliveryAfterSaleAmountMap = new HashMap<>();

        for (Long orderItemId : orderAfterSaleMap.keySet()) {
            List<OrderAfterSaleResp> afterSaleList = orderAfterSaleMap.get(orderItemId);
            afterSaleAmountMap.put(orderItemId, afterSaleList.size());
            afterSaleList = afterSaleList.stream().filter(e -> !AFTERSALE_STATUS_LIST_FOR_CLOSE.contains(e.getStatus())).collect(Collectors.toList());
            afterSaleCountMap.put(orderItemId, afterSaleList.stream().mapToInt(OrderAfterSaleResp::getAmount).sum());
            afterSalePriceMap.put(orderItemId, afterSaleList.stream().map(OrderAfterSaleResp::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));

            Integer preDeliveryAfterSaleAmount = afterSaleList.stream()
                    .filter(e ->
                    OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue().equals(e.getStatus())
                            && OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(e.getAfterSaleType()))
                    .mapToInt(OrderAfterSaleResp::getAmount).sum();
            preDeliveryAfterSaleAmountMap.put(orderItemId, preDeliveryAfterSaleAmount);
        }

        BigDecimal productTotalPrice = BigDecimal.ZERO;
        Map<Long, OrderAfterSaleEnableResp> enableApplyDTOMap = orderAfterSaleQueryFacade.queryEnableApply(orderVO.getTenantId(), orderId, null);

        if (CollectionUtils.isEmpty(enableApplyDTOMap)) {
            log.error("订单没有找到可售后信息：{}", orderId, new Exception());
        }
        for (OrderItemVO orderItemVO : orderItemVOS) {
            productTotalPrice = NumberUtil.add(productTotalPrice, orderItemVO.getTotalPrice());
            MarketItemDTO marketItemDTO = marketItemMap.get(orderItemVO.getItemId());
            orderItemVO.setItemCode(Objects.isNull(marketItemDTO) ? "" : marketItemDTO.getItemCode());
            // 查询已经在售后的数量 去除掉换货以及补发成功的数量
            OrderAfterSaleEnableResp orderAfterSaleEnableApplyDTO = enableApplyDTOMap.get(orderItemVO.getId());
            boolean flag = (null != orderAfterSaleEnableApplyDTO) && (orderAfterSaleEnableApplyDTO.getEnableApplyAmount() > 0
                    || orderAfterSaleEnableApplyDTO.getEnableApplyQuantity() > 0);
            // 判断售后到期时间
//            if (flag && orderItemVO.getAfterSaleExpiryTime() != null) {
//                flag = orderItemVO.getAfterSaleExpiryTime().compareTo(LocalDateTime.now()) > NumberConstant.ZERO;
//            }
            orderItemVO.setEnableApplyAfterSale(flag);
            if (null != orderAfterSaleEnableApplyDTO) {
                orderItemVO.setEnableApplyAmount(orderAfterSaleEnableApplyDTO.getEnableApplyAmount());
                orderItemVO.setEnableApplyQuantity(orderAfterSaleEnableApplyDTO.getEnableApplyQuantity());
            } else {
                log.error("订单项找不到OrderAfterSaleEnableApplyDTO：{}.{}", orderId, orderItemVO.getId(), new Exception());
            }

            // 设置售后信息
            orderItemVO.setAfterSaleAmount(Optional.ofNullable(afterSaleAmountMap.get(orderItemVO.getId())).orElse(NumberConstants.ZERO));
            orderItemVO.setAfterSaleCount(Optional.ofNullable(afterSaleCountMap.get(orderItemVO.getId())).orElse(NumberConstants.ZERO));
            orderItemVO.setAfterSalePrice(Optional.ofNullable(afterSalePriceMap.get(orderItemVO.getId())).orElse(BigDecimal.ZERO));

            // 设置发货数量和发货重量
            orderItemVO.setWeight(Optional.ofNullable(orderItemVO.getWeight()).orElse(BigDecimal.ZERO));
            orderItemVO.setTotalWeight(NumberUtil.mul(orderItemVO.getWeight(), orderItemVO.getAmount()));
            orderItemVO.setDeliveryNum(orderItemVO.getAmount() - Optional.ofNullable(preDeliveryAfterSaleAmountMap.get(orderItemVO.getId())).orElse(0));
            orderItemVO.setDeliveryTotalWeight(NumberUtil.mul(orderItemVO.getWeight(), orderItemVO.getDeliveryNum()));
        }

        // 设置配送的状态信息
        Integer deliveryStatus = builderDeliveryStatus(orderItemVOS, orderVO.getStatus(), orderVO.getWarehouseType());
        orderVO.setDeliveryStatus(deliveryStatus);

        orderVO.setProductTotalPrice(productTotalPrice);
        orderVO.setOrderItemVOS(orderItemVOS);

        // 下单重量设置
        orderVO.setOrderNum(orderItemVOS.stream().mapToInt(OrderItemVO::getAmount).sum());
        orderVO.setOrderTotalWeight(orderItemVOS.stream().map(OrderItemVO::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
        orderVO.setOrderDeliveryNum(orderItemVOS.stream().mapToInt(OrderItemVO::getDeliveryNum).sum());
        orderVO.setOrderDeliveryTotalWeight(orderItemVOS.stream().map(OrderItemVO::getDeliveryTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add));


        // 自提信息处理
        List<OrderSelfLiftingDTO> orderSelfLiftingDTOS = orderSelfLiftingMapper.selectByOrderNo(orderVO.getOrderNo());
        orderVO.setOrderSelfLiftingDTOS(orderSelfLiftingDTOS);
        // 获取订单仓库信息
        WarehouseTypeEnum typeEnum = WarehouseTypeEnum.getByCode(orderVO.getWarehouseType());
        if (Objects.nonNull(orderVO.getWarehouseNo())) {
            TenantCompany tenantCompany = tenantCompanyService.selectSimpleCompanyByTenantId(orderVO.getTenantId());
            List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade
                    .queryWarehouseStorageList(UserLoginContextUtils.getTenantId(), WarehouseSourceEnum.SAAS_WAREHOUSE, Lists.newArrayList(Integer.valueOf(orderVO.getWarehouseNo())));
            Map<Integer, WarehouseStorageResp> warehouseStorageMap = warehouseStorageResps.stream()
                    .collect(Collectors.toMap(WarehouseStorageResp::getWarehouseNo, Function.identity(), (v1, v2) -> v1));
            Pair<String, String> warehousePair = getWarehousePair(warehouseStorageMap, tenantCompany, orderVO.getWarehouseType(), orderVO.getWarehouseNo());
            orderVO.setDeliveryWarehouseName(warehousePair.getKey());
            orderVO.setDeliveryServiceName(warehousePair.getValue());
        } else if (typeEnum == WarehouseTypeEnum.NO_WAREHOUSE || typeEnum == WarehouseTypeEnum.THREE_PARTIES) {
            orderVO.setDeliveryWarehouseName(typeEnum.getDesc());
        }

        if(!StringUtils.isEmpty(orderVO.getPlanOrderNo())){
            PlanOrder planOrder = planOrderDao.getByPlanOrderNo(orderVO.getPlanOrderNo());
            orderVO.setPlanType(Optional.ofNullable(planOrder).map(PlanOrder::getPlanType).orElse(null));
        }

        //组合支付明细组装
        orderVO.setCanNonCashRefund(Boolean.FALSE);
        if (orderVO.getPayType().equals(com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getCode())) {
            List<PaymentCombinedOrderDetailVO> listByOrderIds = paymentCombinedOrderDetailMapper.getListByOrderIds(Lists.newArrayList(orderId));
            listByOrderIds.forEach(e -> e.setTradeTypeDesc(PaymentTradeTypeEnum.getPaymentTradeType(e.getTradeType()) == null ?
                    null : PaymentTradeTypeEnum.getPaymentTradeType(e.getTradeType()).getDetailDesc()));
            orderVO.setPaymentCombinedDetailVOS(listByOrderIds);

            long count = listByOrderIds.stream().filter(e -> Objects.equals(e.getTradeType(), (PaymentTradeTypeEnum.NON_CASH.getDesc()))).count();
            orderVO.setCanNonCashRefund(count > 0);
        } else if (orderVO.getPayType().equals(com.cosfo.ordercenter.client.common.PayTypeEnum.NON_CASH_PAY.getCode())) {
            orderVO.setCanNonCashRefund(Boolean.TRUE);
        }

        buildTmsSignProofList(orderVO);
        buildPaymentInfo(orderVO);
        return CommonResult.ok(orderVO);
    }

    private void buildPaymentInfo(OrderVO orderVO) {
        PaymentItem paymentItem = paymentItemMapper.selectByOrderId (orderVO.getTenantId (), orderVO.getOrderId ());
        if(paymentItem!=null) {
            orderVO.setPaymentReceipt (paymentItem.getPaymentReceipt ());
            orderVO.setFinancialReceipt (paymentItem.getFinancialReceipt ());
        }

        // 总金额去掉非现金金额
        Map<String, BigDecimal> orderTradeTypeAmountMap = paymentCombinedDetailService.querySuccessCombinedTradeTypeAmountByOrderId(orderVO.getTenantId(), orderVO.getOrderId());
        BigDecimal nonCashAmount = orderTradeTypeAmountMap.getOrDefault(PaymentTradeTypeEnum.NON_CASH.getDesc(), BigDecimal.ZERO);
        orderVO.setNonCashAmount(nonCashAmount.negate());
        orderVO.setTotalPrice(NumberUtil.sub(orderVO.getTotalPrice(), nonCashAmount));

        // 非现金变动记录
        String orderNo = orderVO.getOrderNo();
        List<MerchantStoreBalanceChangeRecord> changeRecords = merchantStoreBalanceService.queryByAssociatedOrderNos(Collections.singletonList(orderNo));
        

    }

    /**
     * 订单详情组装tms签收图片
     * @param orderVO
     */
    private void buildTmsSignProofList(OrderVO orderVO){
        if(orderVO == null){
            return;
        }

        try {
            if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderVO.getWarehouseType()) && FulfillmentTypeEnum.CITY_DELIVERY.getValue().equals(orderVO.getFulfillmentType())) {
                OrderDeliveryInfoStandardResp orderDeliveryInfoStandardResp = ofcDeliveryQueryFacade.queryOrderDeliveryInfo(orderVO.getOrderNo());
                if(orderDeliveryInfoStandardResp == null || orderDeliveryInfoStandardResp.getDeliverySite() == null){
                    return;
                }

                OrderTmsDeliverySiteStandardDTO deliverySite = orderDeliveryInfoStandardResp.getDeliverySite();

                // 签收凭证
                List<String> signProofList = Lists.newArrayList();
                if (!StringUtils.isEmpty(deliverySite.getSignInPics())){ // 到达打卡照片
                    signProofList.addAll(Arrays.asList(deliverySite.getSignInPics().split(",")));
                }
                if (!StringUtils.isEmpty(deliverySite.getSignInSignPic())){ // 配送照片签收面单
                    signProofList.addAll(Arrays.asList(deliverySite.getSignInSignPic().split(",")));
                }
                if (!StringUtils.isEmpty(deliverySite.getSignInProductPic())){ // 配送照片货物照片
                    signProofList.addAll(Arrays.asList(deliverySite.getSignInProductPic().split(",")));
                }
                orderVO.setSignProofList(signProofList);
            }
        } catch (Exception e) {
            log.warn("查询tms配送信息报错，orderNo={}，", orderVO.getOrderNo(), e);
        }

    }


    @Override
    public void export(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Map<String, String> queryParamsMap = builderOrderExportParamMap(orderQueryDTO, loginContextInfoDTO);

        orderQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());

        Boolean isSupplierDistributor = authRoleService.isSupplierRole(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getAuthUserId());

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.ORDER.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_ORDERS.getFileName() : ExcelTypeEnum.ORDER.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(orderQueryDTO, e -> writeDownloadCenter(e, recordDTO.getFileName(), isSupplierDistributor, loginContextInfoDTO));
    }


    @Override
    public void exportForJuepei(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Map<String, String> queryParamsMap = builderOrderExportParamMap(orderQueryDTO, loginContextInfoDTO);

        orderQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.ORDER_JUEPEI.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.ORDER_JUEPEI.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(orderQueryDTO, e -> writeDownloadCenterForJuepei(e, recordDTO.getFileName(), loginContextInfoDTO));
    }

    public DownloadCenterOssRespDTO writeDownloadCenterForJuepei(OrderQueryDTO orderQueryDTO, String fileName, LoginContextInfoDTO loginContextInfoDTO) {
        // 1、表格处理
        String filePath = generateOrderJuePeiExportFile(orderQueryDTO, loginContextInfoDTO);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }

    /**
     * 绝配订单导出
     *
     * @param orderQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    private String generateOrderJuePeiExportFile(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        List<OrderExportVO> list = Lists.newArrayList();
        Boolean isSupplierDistributor = authRoleService.isSupplierRole(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getAuthUserId());
        // 处理查询条件
        boolean hasResultFlag = dealQueryConditions(orderQueryDTO, loginContextInfoDTO);
        if (StringUtils.isEmpty(orderQueryDTO.getStartTime()) && StringUtils.isEmpty(orderQueryDTO.getEndTime())) {
            orderQueryDTO.setStartTime(TimeUtils.getThreeMonthBeforeTime());
            orderQueryDTO.setEndTime(TimeUtils.getTodayString(TimeUtils.FORMAT));
        }
        if (!hasResultFlag) {
            return commonService.exportExcel(list, ExcelTypeEnum.ORDER_JUEPEI.getName());
        }
        return generateOrderExportFileLoopJuePei(orderQueryDTO, isSupplierDistributor, loginContextInfoDTO);
    }

    private String generateOrderExportFileLoopJuePei(OrderQueryDTO orderQueryDTO, Boolean isSupplierDistributor, LoginContextInfoDTO loginContextInfoDTO) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.ORDER_JUEPEI.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream)
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        excelWriter.fill(Collections.emptyList(), fillConfig, writeSheet);

        OrderQueryReq orderQueryReq = getOrderExportQueryReq(orderQueryDTO, isSupplierDistributor);

        Integer loopIndex = NumberConstant.ZERO;
        do {
            List<OrderResp> orderResps = orderQueryFacade.queryOrderList(orderQueryReq);
            if (CollectionUtils.isEmpty(orderResps)) {
                break;
            }
            loopIndex++;
            if (loopIndex > NumberConstant.TWO_HUNDRED) {
                log.error("绝配订单导出，查询订单数据过大进行终止,orderQueryReq:{}", JSON.toJSONString(orderQueryReq));
                break;
            }

            orderQueryReq.setMaxId(orderResps.get(orderResps.size() - 1).getId());
            List<JuePeiOrderExportVO> resultOrderExportVOS = new ArrayList<>(orderResps.size());

            List<OrderVO> orderVOS = OrderConvert.INSTANCE.convertResp2VOs(orderResps);
            // 订单状态转换
            for (OrderVO orderVO : orderVOS) {
                OrderStatusEnum.transferOrderStatus(orderVO);
            }

            List<Long> orderIdList = orderVOS.stream().map(e -> e.getOrderId()).collect(Collectors.toList());

            OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
            orderItemQueryReq.setOrderIds(orderIdList);
            if (isSupplierDistributor) {
                orderItemQueryReq.setSupplierIds(orderQueryDTO.getSupplierIds());
            }
            List<OrderItemVO> orderItems = orderItemQueryFacade.queryOrderItemListVO(orderItemQueryReq);
            Map<Long, List<OrderItemVO>> orderItemsMap = orderItems.stream().collect(Collectors.groupingBy(OrderItemVO::getOrderId));

            List<Long> storeIdList = orderVOS.stream().map(e -> e.getStoreId()).distinct().collect(Collectors.toList());

            // 查询门店信息、门店账号信息
            List<MerchantStoreDTO> merchantStoreDTOList = merchantStoreService.batchQuery(storeIdList, orderQueryDTO.getTenantId());
            Map<Long, MerchantStoreDTO> merchantStoreDTOMap = merchantStoreDTOList.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, Function.identity(), (v1, v2) -> v1));

            List<Long> itemIds = orderItems.stream().map(e -> e.getItemId()).distinct().collect(Collectors.toList());
            Map<Long, MarketItemDTO> marketItemDTOMap = marketItemService.getMapByItemIds(itemIds);


            for (OrderVO orderVO : orderVOS) {
                // 查询订单项
                List<OrderItemVO> orderItemVOS = orderItemsMap.get(orderVO.getOrderId());

                // 查询门店信息
                MerchantStoreDTO merchantStoreDTO = merchantStoreDTOMap.get(orderVO.getStoreId());
                List<JuePeiOrderExportVO> orderExportVOS = orderItemVOS.stream().map(orderItemVO -> {
                    // 查询销售商品信息
                    MarketItemDTO marketItemDto = marketItemDTOMap.get(orderItemVO.getItemId());

                    JuePeiOrderExportVO orderExportVO = new JuePeiOrderExportVO();
                    orderExportVO.setOrderNO(orderVO.getOrderNo());
                    orderExportVO.setWarehouseName("广州2仓");
                    orderExportVO.setOrderSaleTypeDesc("销售出库");
                    String itemCode = marketItemDto.getItemCode();
                    // 只导出有商品编码的订单
                    if (StringUtils.isEmpty(itemCode)) {
                        return null;
                    }
                    orderExportVO.setItemCodeDetail(itemCode);

                    orderExportVO.setAmount(orderItemVO.getAmount());
                    orderExportVO.setPrice(orderItemVO.getPrice());
                    orderExportVO.setStoreName(Optional.ofNullable(merchantStoreDTO).map(MerchantStoreDTO::getStoreName).orElse(""));

                    return orderExportVO;
                }).filter(Objects::nonNull).collect(Collectors.toList());

                resultOrderExportVOS.addAll(orderExportVOS);
            }
            excelWriter.fill(resultOrderExportVOS, fillConfig, writeSheet);
        } while (true);

        excelWriter.finish();

        return filePath;
    }

    /**
     * 构建订单导出请求参数
     *
     * @param orderQueryDTO
     * @param isSupplierDistributor
     * @return
     */
    private OrderQueryReq getOrderExportQueryReq(OrderQueryDTO orderQueryDTO, Boolean isSupplierDistributor) {
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        if (orderQueryDTO.getStatus() != null && orderQueryDTO.getStatus() == 10) {
            orderQueryReq.setStatusList(Lists.newArrayList(OrderStatusEnum.WAITING_DELIVERY.getCode(), OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode()));
        } else if (orderQueryDTO.getStatus() != null && orderQueryDTO.getStatus() == 4) {
            orderQueryReq.setStatusList(Lists.newArrayList(OrderStatusEnum.DELIVERING.getCode(), OrderStatusEnum.OUT_OF_STORAGE.getCode()));
        } else {
            orderQueryReq.setStatus(orderQueryDTO.getStatus());
        }

        orderQueryReq.setTenantId(orderQueryDTO.getTenantId());
        orderQueryReq.setOrderId(orderQueryDTO.getOrderId());
        orderQueryReq.setStoreType(orderQueryDTO.getStoreType());
        orderQueryReq.setStoreName(orderQueryDTO.getStoreName());
        orderQueryReq.setOrderNo(orderQueryDTO.getOrderNo());
        orderQueryReq.setWarehouseType(orderQueryDTO.getWarehouseType());
        orderQueryReq.setPhone(orderQueryDTO.getPhone());
//        orderQueryReq.setCreateStartTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getStartTime()));
//        orderQueryReq.setCreateEndTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getEndTime()));
        orderQueryReq.setStoreIds(orderQueryDTO.getStoreIds());
        orderQueryReq.setAccountIds(orderQueryDTO.getAccountIds());
        orderQueryReq.setPayType(orderQueryDTO.getPayType());
        orderQueryReq.setItemIds(orderQueryDTO.getItemIds());
        orderQueryReq.setSkuId(orderQueryDTO.getSkuId());
        orderQueryReq.setOrderItemIds(orderQueryDTO.getOrderItemIds());
        orderQueryReq.setWarehouseNo(orderQueryDTO.getWarehouseNo() == null ? null : orderQueryDTO.getWarehouseNo().toString());
        orderQueryReq.setMaxId(0L);
        orderQueryReq.setSupplierTenantIds(orderQueryDTO.getSupplierIds());
        orderQueryReq.setOrderSource(orderQueryDTO.getOrderSource());
        orderQueryReq.setPlanOrderNo(orderQueryDTO.getPlanOrderNo());
        orderQueryReq.setTimeQueryType(orderQueryDTO.getTimeQueryType());
        orderQueryReq.setQueryStartTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getStartTime()));
        orderQueryReq.setQueryEndTime(com.cosfo.manage.common.util.LocalDateTimeUtil.parseDateTime(orderQueryDTO.getEndTime()));
        return orderQueryReq;
    }

    /**
     * 组装订单导出参数查询的map
     *
     * @param orderQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    private Map<String, String> builderOrderExportParamMap(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Map<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.EIGHT);
        // 订单状态
        if (Objects.nonNull(orderQueryDTO.getStatus())) {
            queryParamsMap.put(Constants.STATUS, OrderStatusEnum.getDesc(orderQueryDTO.getStatus()));
        }
        // 配送仓类型
        if (Objects.nonNull(orderQueryDTO.getWarehouseType())) {
            queryParamsMap.put(Constants.WAREHOUSE_TYPE, orderQueryDTO.getWarehouseType() == 0 ? WarehouseTypeEnum.NO_WAREHOUSE.getDesc() : WarehouseTypeEnum.THREE_PARTIES.getDesc());
        }
        // 门店名称
        if (!CollectionUtils.isEmpty(orderQueryDTO.getStoreIds())) {
            MerchantStoreQueryDTO query = MerchantStoreQueryDTO.builder()
                    .tenantId(orderQueryDTO.getTenantId())
                    .storeIds(orderQueryDTO.getStoreIds())
                    .build();
            List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreService.listByConditionNew(query);

            queryParamsMap.put(Constants.STORE_NAME, merchantStoreDTOS.stream().map(MerchantStoreDTO::getStoreName).collect(Collectors.joining(",")));
        }
        // 订单编号
        if (!com.cosfo.manage.common.util.StringUtils.isBlank(orderQueryDTO.getOrderNo())) {
            queryParamsMap.put(Constants.ORDER_NO, orderQueryDTO.getOrderNo());
        }
        // 门店类型
        if (Objects.nonNull(orderQueryDTO.getStoreType())) {
            queryParamsMap.put(Constants.STORE_TYPE, MerchantStoreEnum.Type.getDesc(orderQueryDTO.getStoreType()));
        }
        // 手机号
        if (!com.cosfo.manage.common.util.StringUtils.isBlank(orderQueryDTO.getPhone())) {
            queryParamsMap.put(Constants.PHONE, orderQueryDTO.getPhone());
        }

        // 查询时间
        if (orderQueryDTO.getTimeQueryType() != null && !com.cosfo.manage.common.util.StringUtils.isBlank(orderQueryDTO.getStartTime()) && !com.cosfo.manage.common.util.StringUtils.isBlank(orderQueryDTO.getEndTime())) {
            String timeStr = orderQueryDTO.getStartTime() + "~" + orderQueryDTO.getEndTime();
            if(orderQueryDTO.getTimeQueryType() == 1){
                queryParamsMap.put("下单时间", timeStr);

            }else if(orderQueryDTO.getTimeQueryType() == 2){
                queryParamsMap.put("支付时间", timeStr);

            }else if(orderQueryDTO.getTimeQueryType() == 3){
                queryParamsMap.put("配送时间", timeStr);

            }else if(orderQueryDTO.getTimeQueryType() == 4){
                queryParamsMap.put("完成时间", timeStr);
            }
        }

        // 支付方式
        if (ObjectUtil.isNotNull(orderQueryDTO.getPayType())) {
            queryParamsMap.put(Constants.PAY_TYPE, OrderPayTypeEnum.getDesc(orderQueryDTO.getPayType()));
        }
        if (ObjectUtil.isNotNull(orderQueryDTO.getItemId())) {
            queryParamsMap.put(Constants.ITEM_ID, orderQueryDTO.getItemId());
        }
        if (ObjectUtil.isNotNull(orderQueryDTO.getSkuId())) {
            queryParamsMap.put(Constants.SKU_ID_UPPER, orderQueryDTO.getSkuId().toString());
        }
        if (ObjectUtil.isNotNull(orderQueryDTO.getTitle())) {
            queryParamsMap.put(Constants.TITLE, orderQueryDTO.getTitle());
        }
        if (!StringUtils.isEmpty(orderQueryDTO.getStoreNo())) {
            queryParamsMap.put(Constants.STORE_NO, orderQueryDTO.getStoreNo());
        }
        if (Objects.nonNull(orderQueryDTO.getSupplierId())) {
            Map<Long, SupplierInfoDTO> supplierInfoMap = supplierService.batchQuerySupplierMap(loginContextInfoDTO.getTenantId(), Arrays.asList(orderQueryDTO.getSupplierId()));
            SupplierInfoDTO supplierInfoDTO = supplierInfoMap.get(orderQueryDTO.getSupplierId());
            queryParamsMap.put(Constants.SUPPLIER_NAME, supplierInfoDTO.getSupplierName());
        }
        if (!StringUtils.isEmpty(orderQueryDTO.getWarehouseNo())) {
            // 处理仓库编号
            WarehouseQueryEnum warehouseQueryEnum = WarehouseQueryEnum.getById(orderQueryDTO.getWarehouseNo());
            orderQueryDTO.setWarehouseType(transferWarehouseType(orderQueryDTO, warehouseQueryEnum));
            if (Objects.nonNull(warehouseQueryEnum)) {
                queryParamsMap.put(Constants.WAREHOUSE_NAME, warehouseQueryEnum.getName());
            } else {
                WarehouseStorageResp warehouseStorageResp = warehouseStorageQueryFacade.queryOneWarehouseStorage(orderQueryDTO.getWarehouseNo());
                queryParamsMap.put(Constants.WAREHOUSE_NAME, Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getWarehouseName).orElse(orderQueryDTO.getWarehouseNo().toString()));
            }
        }

        /**
         * 下单来源
         */
        if (Objects.nonNull(orderQueryDTO.getOrderSource())) {
            queryParamsMap.put(Constants.ORDER_SOURCE, getOrderSourceStr(orderQueryDTO.getOrderSource()));
        }

        /**
         * 计划单编号
         */
        if (!StringUtils.isEmpty(orderQueryDTO.getPlanOrderNo())) {
            queryParamsMap.put(Constants.PLAN_ORDER_NO, orderQueryDTO.getPlanOrderNo());
        }

        // 门店分组
        String groupNames = merchantStoreGroupService.queryGroupNameByIds(loginContextInfoDTO.getTenantId(), orderQueryDTO.getMerchantStoreGroupIds());
        if (!StringUtils.isEmpty(groupNames)) {
            queryParamsMap.put(Constants.STORE_GROUPS, groupNames);
        }
        return queryParamsMap;
    }


    public DownloadCenterOssRespDTO writeDownloadCenter(OrderQueryDTO orderQueryDTO, String fileName, Boolean isSupplierDistributor, LoginContextInfoDTO loginContextInfoDTO) {
        // 1、表格处理
        String filePath = generateOrderExportFile(orderQueryDTO, isSupplierDistributor, loginContextInfoDTO);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }


    private String generateOrderExportFileLoop(OrderQueryDTO orderQueryDTO, Boolean isSupplierDistributor, LoginContextInfoDTO loginContextInfoDTO, TenantFundAccountConfig infoByTenantId) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_ORDERS.getName() : ExcelTypeEnum.ORDER.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream)
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();

        // 查询租户公司名信息
        TenantCompany tenantCompany = tenantCompanyService.selectSimpleCompanyByTenantId(orderQueryDTO.getTenantId());

        OrderQueryReq orderQueryReq = getOrderExportQueryReq(orderQueryDTO, isSupplierDistributor);

        do {
            List<OrderResp> orderResps = orderQueryFacade.queryOrderList(orderQueryReq);
            if (CollectionUtils.isEmpty(orderResps)) {
                break;
            }
            orderQueryReq.setMaxId(orderResps.get(orderResps.size() - 1).getId());

            List<Integer> warehouseNoList = orderResps.stream().filter(order -> order.getWarehouseNo() != null).map(order -> Integer.valueOf(order.getWarehouseNo())).collect(Collectors.toList());
            List<WarehouseStorageResp> warehouseStorageResps = new ArrayList<>();
            Map<Integer, WarehouseStorageResp> warehouseStorageMap;
            if (!CollectionUtils.isEmpty(warehouseNoList)) {
                warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(loginContextInfoDTO.getTenantId(), WarehouseSourceEnum.SAAS_WAREHOUSE, warehouseNoList);
                warehouseStorageMap = warehouseStorageResps.stream().collect(Collectors.toMap(WarehouseStorageResp::getWarehouseNo, Function.identity(), (v1, v2) -> v1));
            } else {
                warehouseStorageMap = new HashMap<>();
            }
            List<OrderExportVO> resultOrderExportVOS = new ArrayList<>(orderResps.size());

            List<OrderVO> orderVOS = OrderConvert.INSTANCE.convertResp2VOs(orderResps);
            // 订单状态转换
            for (OrderVO orderVO : orderVOS) {
                OrderStatusEnum.transferOrderStatus(orderVO);
            }

            Map<String, PlanOrder> planOrderMap = getPlanOrderMap(orderVOS.stream().map(OrderVO::getPlanOrderNo).filter(e -> !StringUtils.isEmpty(e)).distinct().collect(Collectors.toList()));


            List<Long> orderIdList = orderVOS.stream().map(e -> e.getOrderId()).collect(Collectors.toList());

            List<String> orderNos = orderVOS.stream().map(e -> e.getOrderNo()).collect(Collectors.toList());

            OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
            orderItemQueryReq.setOrderIds(orderIdList);
            if (isSupplierDistributor) {
                orderItemQueryReq.setSupplierIds(orderQueryDTO.getSupplierIds());
            }
            List<OrderItemVO> orderItems = orderItemQueryFacade.queryOrderItemListVO(orderItemQueryReq);
            Map<Long, List<OrderItemVO>> orderItemsMap = orderItems.stream().collect(Collectors.groupingBy(OrderItemVO::getOrderId));

            List<Long> storeIdList = orderVOS.stream().map(e -> e.getStoreId()).distinct().collect(Collectors.toList());
            Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(orderQueryDTO.getTenantId(), storeIdList);

            // 查询门店信息、门店账号信息
            List<MerchantStoreDTO> merchantStoreDTOList = merchantStoreService.batchQuery(storeIdList, orderQueryDTO.getTenantId());
            Map<Long, MerchantStoreDTO> merchantStoreDTOMap = merchantStoreDTOList.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, Function.identity(), (v1, v2) -> v1));

            // 查询门店账号信息
            List<Long> accountIdList = orderVOS.stream().map(e -> e.getAccountId()).distinct().collect(Collectors.toList());
            List<MerchantStoreAccountResultResp> accountInfoList = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(accountIdList);
            Map<Long, MerchantStoreAccountResultResp> accountInfoMap = accountInfoList.stream().collect(Collectors.toMap(MerchantStoreAccountResultResp::getId, Function.identity(), (v1, v2) -> v1));

            // 查询门店地址
            List<OrderAddressResp> orderAddressResps = orderAddressQueryFacade.queryByOrderIds(orderQueryDTO.getTenantId(), orderIdList);
            Map<Long, OrderAddressResp> orderAddressMap = orderAddressResps.stream().collect(Collectors.toMap(OrderAddressResp::getOrderId, item -> item));

            // 查询订单号对应配送信息
            Map<String, List<FulfillmentDeliveryResp>> orderNoDeliveryMap = batchQueryOrderDelivery(orderNos);

            List<Long> itemIds = orderItems.stream().map(e -> e.getItemId()).distinct().collect(Collectors.toList());
            Map<Long, MarketItemInfoDTO> marketItemDTOMap = marketItemService.getMapByItemIds(itemIds, orderQueryDTO.getTenantId());

            // 订单售后信息
            Map<Long, List<OrderAfterSaleResp>> orderAfterSaleMap = Collections.emptyMap();
            OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
            req.setTenantId(loginContextInfoDTO.getTenantId());
            req.setOrderIds(orderIdList);
            List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryList(req);
            if (!CollectionUtils.isEmpty(orderAfterSaleResps)) {
                orderAfterSaleMap = orderAfterSaleResps.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));
            }
            Map<Long, Integer> afterSaleCountMap = new HashMap<>();
            Map<Long, BigDecimal> afterSalePriceMap = new HashMap<>();

            //配送前售后数量
            Map<Long, Integer> preDeliveryAfterSaleAmountMap = new HashMap<>();

            for (Long orderItemId : orderAfterSaleMap.keySet()) {
                List<OrderAfterSaleResp> afterSaleList = orderAfterSaleMap.get(orderItemId);
                afterSaleList = afterSaleList.stream().filter(e -> !AFTERSALE_STATUS_LIST_FOR_CLOSE.contains(e.getStatus())).collect(Collectors.toList());
                afterSaleCountMap.put(orderItemId, afterSaleList.stream().mapToInt(OrderAfterSaleResp::getAmount).sum());
                afterSalePriceMap.put(orderItemId, afterSaleList.stream().map(OrderAfterSaleResp::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));

                Integer preDeliveryAfterSaleAmount = afterSaleList.stream()
                        .filter(e ->
                                OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue().equals(e.getStatus())
                                        && OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(e.getAfterSaleType()))
                        .mapToInt(OrderAfterSaleResp::getAmount).sum();
                preDeliveryAfterSaleAmountMap.put(orderItemId, preDeliveryAfterSaleAmount);
            }
            Map<Long, Integer> payTypeMap = Collections.emptyMap ();
            Map<Long, String> offlinePayRemarkeMap = Collections.emptyMap ();
            // 查询支付单
            List<PaymentItem> paymentItems = paymentItemMapper.selectByOrderIds (loginContextInfoDTO.getTenantId(), orderVOS.stream ().map (OrderVO::getOrderId).collect(Collectors.toSet()));
            if(CollectionUtil.isNotEmpty (paymentItems)){
               payTypeMap = paymentItems.stream().filter (a-> a.getOfflinePayType ()!=null).collect(Collectors.toMap(PaymentItem::getOrderId,PaymentItem::getOfflinePayType));
               offlinePayRemarkeMap = paymentItems.stream().filter (a-> !StringUtils.isEmpty(a.getRemark ())).collect(Collectors.toMap(PaymentItem::getOrderId, PaymentItem::getRemark, (v1, v2) -> v2));
            }

            //假如是组合支付 查询支付明细
            Map<Long, List<PaymentCombinedOrderDetailVO>> paymentCombinedDetailMap = new HashMap<>();
            Set<Long> combinedPaymentOrderIds = orderVOS.stream().filter(orderVO -> orderVO.getPayType().equals(com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getCode()))
                    .map(OrderVO::getOrderId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(combinedPaymentOrderIds)) {
                List<PaymentCombinedOrderDetailVO> paymentCombinedDetailVOS = paymentCombinedOrderDetailMapper.getListByOrderIds(combinedPaymentOrderIds);
                paymentCombinedDetailMap = paymentCombinedDetailVOS.stream().collect(Collectors.groupingBy(PaymentCombinedOrderDetailVO::getOrderId));
            }
            for (OrderVO orderVO : orderVOS) {
                Map<Long, BigDecimal> deliveryFeeMap = new HashMap<>(NumberConstant.ONE);

                // 查询订单项
                List<OrderItemVO> orderItemVOS = orderItemsMap.get(orderVO.getOrderId());

                List<PaymentCombinedOrderDetailVO> paymentCombinedDetailVOS = paymentCombinedDetailMap.getOrDefault(orderVO.getOrderId(), Collections.emptyList());

                // 查询门店信息、门店账号信息
                MerchantStoreDTO merchantStoreDTO = merchantStoreDTOMap.get(orderVO.getStoreId());
                if (Objects.nonNull(merchantStoreDTO)) {
                    orderVO.setStoreNo(merchantStoreDTO.getStoreNo());
                    orderVO.setStoreType(merchantStoreDTO.getType());
                    orderVO.setStoreName(merchantStoreDTO.getStoreName());
                }
                MerchantStoreAccountResultResp accountInfo = accountInfoMap.get(orderVO.getAccountId());
                if (Objects.nonNull(accountInfo)) {
                    orderVO.setContactPhone(accountInfo.getPhone());
                }
                // 查询订单地址
                OrderAddressResp orderAddress = orderAddressMap.get(orderVO.getOrderId());

                // 按照创建时间正序
                List<FulfillmentDeliveryResp> fulfillmentDeliveryResps = Optional.ofNullable(orderNoDeliveryMap.get(orderVO.getOrderNo()))
                        .map(respList -> respList.stream().sorted(Comparator.comparing(FulfillmentDeliveryResp::getCreateTime)).collect(Collectors.toList()))
                        .orElse(Lists.newArrayList());

                AtomicInteger index = new AtomicInteger();
                Integer offlinePayType = payTypeMap.getOrDefault (orderVO.getOrderId (),-1);
                String offlinePayRemark = offlinePayRemarkeMap.getOrDefault(orderVO.getOrderId(),"");

                List<OrderExportVO> orderExportVOS = orderItemVOS.stream().map(orderItemVO -> {

                    OrderAddressResp finalOrderAddress = new OrderAddressResp();
                    // 地址
                    StringBuffer address = new StringBuffer();
                    if (orderAddress != null) {
                        finalOrderAddress = orderAddress;
                        address.append(orderAddress.getProvince())
                                .append(orderAddress.getCity())
                                .append(orderAddress.getArea())
                                .append(orderAddress.getAddress());
                    }

                    //配送费
                    BigDecimal deliveryFee = null;
                    //配送费
                    if (!deliveryFeeMap.containsKey(orderVO.getOrderId())) {
                        deliveryFee = orderVO.getDeliveryFee();
                    }
                    deliveryFeeMap.put(orderVO.getOrderId(), orderVO.getDeliveryFee());


                    String payType = OrderPayTypeEnum.getDesc(orderVO.getPayType());
                    if (OrderPayTypeEnum.NON_CASH_PAY.getType().equals(orderVO.getPayType())) {
                        payType = infoByTenantId == null ? OrderPayTypeEnum.NON_CASH_PAY.getDesc() : infoByTenantId.getAccountName();
                    }

                    // 仓库信息
                    Integer warehouseType = orderVO.getWarehouseType();
                    Pair<String, String> warehousePair = getWarehousePair(warehouseStorageMap, tenantCompany, orderVO.getWarehouseType(), orderVO.getWarehouseNo());
                    // 获取订单下配送信息
                    int currentIndex = index.getAndIncrement();
                    FulfillmentDeliveryResp fulfillmentDeliveryResp = CollectionUtil.isNotEmpty(fulfillmentDeliveryResps) && fulfillmentDeliveryResps.size() > currentIndex
                            ? fulfillmentDeliveryResps.get(currentIndex) : null;
                    NoWarehouseDeliveryTypeEnum noWarehouseDeliveryTypeEnum = Optional.ofNullable(fulfillmentDeliveryResp).map(resp -> NoWarehouseDeliveryTypeEnum.getByType(resp.getDeliveryType()))
                            .orElse(null);
                    String deliveryTypeDesc = Optional.ofNullable(noWarehouseDeliveryTypeEnum).map(NoWarehouseDeliveryTypeEnum::getDesc).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
                    String deliveryCompany = Optional.ofNullable(fulfillmentDeliveryResp).map(resp -> resp.getLogisticsCompany()).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
                    String deliveryNo = Optional.ofNullable(fulfillmentDeliveryResp).map(resp -> resp.getLogisticsNo()).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
                    if (NoWarehouseDeliveryTypeEnum.NOT_NEED_DELIVERY == noWarehouseDeliveryTypeEnum) {
                        deliveryCompany = Optional.ofNullable(fulfillmentDeliveryResp).map(FulfillmentDeliveryResp::getRemark).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
                    }
                    // 查询销售商品信息
                    // TODO 批量
                    MarketItemInfoDTO marketItemDto = marketItemDTOMap.get(orderItemVO.getItemId());
                    MarketSpuVO marketSpuVO = marketService.detail(marketItemDto.getMarketId());

                    OrderExportVO orderExportVO = new OrderExportVO();
                    orderExportVO.setOrderNO(orderVO.getOrderNo());
                    orderExportVO.setStoreName(merchantStoreDTO.getStoreName());
                    orderExportVO.setOrderTime(orderVO.getOrderTime());
                    orderExportVO.setPayTime(orderVO.getPayTime());
                    orderExportVO.setPayType(payType);

                    //假如组合支付需要补充明细
                    if (payType.equals(com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getDesc())) {
                        String payTypeDesc = payType + "--";
                        for (PaymentCombinedOrderDetailVO paymentCombinedDetailVO : paymentCombinedDetailVOS) {
                            PaymentTradeTypeEnum paymentTradeType = PaymentTradeTypeEnum.getPaymentTradeType(paymentCombinedDetailVO.getTradeType());
                            if (paymentTradeType == null) {
                                continue;
                            }
                            String tradeTypeDesc = paymentTradeType.getDetailDesc();
                            if (Objects.equals(paymentTradeType.getPayType(), PaymentTradeTypeEnum.NON_CASH.getPayType())) {
                                tradeTypeDesc = infoByTenantId == null ? PaymentTradeTypeEnum.NON_CASH.getDetailDesc() : infoByTenantId.getAccountName();
                            }

                            payTypeDesc = payTypeDesc + tradeTypeDesc + ":" + paymentCombinedDetailVO.getTotalPrice() + "元;";
                        }
                        orderExportVO.setPayType(payTypeDesc);
                    }
                    if (orderAddress != null) {
                        orderExportVO.setContactName(orderAddress.getContactName());
                        orderExportVO.setContactPhone(orderAddress.getContactPhone());
                    }
                    orderExportVO.setAddress(address.toString());
                    orderExportVO.setItemId(orderItemVO.getItemId());
                    orderExportVO.setItemCode(marketItemDto.getItemCode());
                    orderExportVO.setSkuId(orderItemVO.getSkuId());
                    orderExportVO.setTitle(orderItemVO.getTitle());
                    orderExportVO.setGoodsType(GoodsTypeEnum.getShowDescByCodeV2(orderItemVO.getGoodsType()));
                    orderExportVO.setSupplierName(orderItemVO.getSupplierName());
                    orderExportVO.setSupplierTenantId(orderItemVO.getSupplierTenantId());
                    orderExportVO.setSpecification(orderItemVO.getSpecification());
                    orderExportVO.setSpecificationUnit(orderItemVO.getSpecificationUnit());
                    orderExportVO.setPrice(orderItemVO.getPrice());
                    orderExportVO.setAmount(orderItemVO.getAmount());
                    orderExportVO.setItemTotalPrice(orderItemVO.getTotalPrice());
                    orderExportVO.setAfterSaleCount(Optional.ofNullable(afterSaleCountMap.get(orderItemVO.getId())).orElse(NumberConstants.ZERO));
                    orderExportVO.setAfterSalePrice(Optional.ofNullable(afterSalePriceMap.get(orderItemVO.getId())).orElse(BigDecimal.ZERO));
                    orderExportVO.setStoreTypeDesc(StoreTypeEnum.getDesc(merchantStoreDTO.getType()));
                    orderExportVO.setStatusDesc(OrderStatusEnum.getDesc(orderVO.getStatus()));
                    orderExportVO.setFirstCategoryName(marketSpuVO.getFirstCategory());
                    orderExportVO.setSecondCategoryName(marketSpuVO.getSecondCategory());
                    orderExportVO.setThirdCategoryName(marketSpuVO.getThirdCategory());
                    orderExportVO.setFirstClassificationName(marketSpuVO.getFirstClassificationName());
                    orderExportVO.setSecondClassificationName(marketSpuVO.getSecondClassificationName());
                    orderExportVO.setDeliveryFee(deliveryFee);
                    orderExportVO.setDeliveryTime(orderVO.getDeliveryTime());
                    orderExportVO.setFinishedTime(orderVO.getFinishedTime());
                    orderExportVO.setRemark(orderVO.getRemark());
                    orderExportVO.setGroupName(groupMap.get(orderVO.getStoreId()));
                    orderExportVO.setStoreNo(merchantStoreDTO.getStoreNo());
                    orderExportVO.setDeliveryTypeDesc(deliveryTypeDesc);
                    orderExportVO.setDeliveryCompany(deliveryCompany);
                    orderExportVO.setDeliveryNo(deliveryNo);
                    orderExportVO.setWarehouseType(WarehouseTypeEnum.getByCode(orderVO.getWarehouseType()).getDesc());
                    orderExportVO.setWarehouseName(warehousePair.getKey());
                    orderExportVO.setWarehouseProvider(warehousePair.getValue());
                    orderExportVO.setSupplyPrice(orderItemVO.getSupplyPrice());
                    orderExportVO.setTotalPrice(orderItemVO.getPrice().multiply(BigDecimal.valueOf(orderItemVO.getAmount())));
                    orderExportVO.setOrderSource(orderVO.getOrderSource());
                    orderExportVO.setOrderSourceStr(getOrderSourceStr(orderVO.getOrderSource()));
                    orderExportVO.setOfflinePayType (offlinePayType);
                    orderExportVO.setOfflinePayTypeStr (OfflinePayTypeEnum.getDescByCode (offlinePayType));
                    orderExportVO.setOfflinePayRemark(offlinePayRemark);
                    if (!StringUtils.isEmpty(orderVO.getPlanOrderNo())) {
                        orderExportVO.setPlanOrderNo(orderVO.getPlanOrderNo());
                        orderExportVO.setPlanOrderCreateTime(Optional.ofNullable(planOrderMap.get(orderVO.getPlanOrderNo())).map(e -> e.getCreateTime()).orElse(null));
                        orderExportVO.setRecommendReason(Optional.ofNullable(planOrderMap.get(orderVO.getPlanOrderNo())).map(e -> e.getRecommendReason()).orElse(null));
                    }

                    // 设置发货数量和发货重量
                    orderItemVO.setWeight(Optional.ofNullable(orderItemVO.getWeight()).orElse(BigDecimal.ZERO));
                    orderExportVO.setWeight(Optional.ofNullable(orderItemVO.getWeight()).orElse(BigDecimal.ZERO));
                    orderExportVO.setTotalWeight(NumberUtil.mul(orderItemVO.getWeight(), orderItemVO.getAmount()));
                    orderExportVO.setDeliveryNum(orderItemVO.getAmount() - Optional.ofNullable(preDeliveryAfterSaleAmountMap.get(orderItemVO.getId())).orElse(0));
                    orderExportVO.setDeliveryTotalWeight(NumberUtil.mul(orderItemVO.getWeight(), orderExportVO.getDeliveryNum()));

                    return orderExportVO;
                }).collect(Collectors.toList());

                resultOrderExportVOS.addAll(orderExportVOS);
            }

            excelWriter.fill(resultOrderExportVOS, fillConfig, writeSheet);

        } while (true);

        excelWriter.finish();

        return filePath;
//        // 上传数据到七牛云
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_ORDERS : ExcelTypeEnum.ORDER, fileDownloadRecordId);
    }

    private Map<String, PlanOrder> getPlanOrderMap(List<String> planOrderNos) {
        List<PlanOrder> list = planOrderDao.queryByPlanOrderNos(planOrderNos);
        return list.stream().collect(Collectors.toMap(PlanOrder::getPlanOrderNo, Function.identity()));
    }
    private String generateOrderExportFile(OrderQueryDTO orderQueryDTO, Boolean isSupplierDistributor, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = loginContextInfoDTO.getTenantId();
        TenantFundAccountConfig infoByTenantId = tenantFundAccountConfigMapper.getInfoByTenantId(tenantId);

        List<OrderExportVO> list = new ArrayList<>();
        if (orderQueryDTO.getOrderId() != null) {
            Map<Long, BigDecimal> deliveryFeeMap = new HashMap<>(NumberConstant.FIVE);
            OrderResp orderResp = orderQueryFacade.queryById(orderQueryDTO.getOrderId());
            List<WarehouseStorageResp> warehouseStorageResps = new ArrayList<>();
            Map<Integer, WarehouseStorageResp> warehouseStorageMap = new HashMap<>();
            if (orderResp.getWarehouseNo() != null) {
                warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(tenantId, WarehouseSourceEnum.SAAS_WAREHOUSE, Lists.newArrayList(Integer.valueOf(orderResp.getWarehouseNo())));
                warehouseStorageMap = warehouseStorageResps.stream().collect(Collectors.toMap(WarehouseStorageResp::getWarehouseNo, Function.identity(), (v1, v2) -> v1));
            }

            Map<String, PlanOrder> planOrderMap = Collections.emptyMap();
            if (!StringUtils.isEmpty(orderResp.getPlanOrderNo())) {
                planOrderMap = getPlanOrderMap(Collections.singletonList(orderResp.getPlanOrderNo()));
            }

            TenantCompany tenantCompany = tenantCompanyService.selectSimpleCompanyByTenantId(orderResp.getTenantId());
            // 订单状态转换
            OrderStatusEnum.transferOrderStatus(orderResp);
            Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(orderQueryDTO.getTenantId(), Arrays.asList(orderResp.getStoreId()));
            // 查询门店信息
            MerchantStoreDTO merchantStoreDTO = merchantStoreService.queryStore(orderResp.getStoreId(), orderQueryDTO.getTenantId());
            // 查询订单项
            List<OrderItemVO> orderItemVOS = orderItemQueryFacade.queryOrderItemByOrderId(orderResp.getId());
            // 查询订单地址
            OrderAddressResp orderAddressResp = orderAddressQueryFacade.queryByOrderId(orderResp.getTenantId(), orderResp.getId());
            // 查询订单号对应配送信息
            Map<String, List<FulfillmentDeliveryResp>> orderNoDeliveryMap = batchQueryOrderDelivery(Lists.newArrayList(orderResp.getOrderNo()));
            // 查询支付单
            PaymentItem paymentItem = paymentItemMapper.selectByOrderId (tenantId,orderResp.getId());
            // 按照创建时间正序
            List<FulfillmentDeliveryResp> fulfillmentDeliveryResps = Optional.ofNullable(orderNoDeliveryMap.get(orderResp.getOrderNo()))
                    .map(respList -> respList.stream().sorted(Comparator.comparing(FulfillmentDeliveryResp::getCreateTime)).collect(Collectors.toList())).orElse(Lists.newArrayList());

            //查询组合支付的支付明细
            List<PaymentCombinedOrderDetailVO> paymentCombinedDetailVOS = null;
            if (orderResp.getPayType().equals(com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getCode())) {
                paymentCombinedDetailVOS = paymentCombinedOrderDetailMapper.getListByOrderIds(Lists.newArrayList(orderResp.getId()));
            }

            // 订单售后信息
            Map<Long, List<OrderAfterSaleResp>> orderAfterSaleMap = Collections.emptyMap();
            OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
            req.setTenantId(loginContextInfoDTO.getTenantId());
            req.setOrderIds(Collections.singletonList(orderQueryDTO.getOrderId()));
            List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryList(req);
            if (!CollectionUtils.isEmpty(orderAfterSaleResps)) {
                orderAfterSaleMap = orderAfterSaleResps.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));
            }
            Map<Long, Integer> afterSaleCountMap = new HashMap<>();
            Map<Long, BigDecimal> afterSalePriceMap = new HashMap<>();
            //配送前售后数量
            Map<Long, Integer> preDeliveryAfterSaleAmountMap = new HashMap<>();

            for (Long orderItemId : orderAfterSaleMap.keySet()) {
                List<OrderAfterSaleResp> afterSaleList = orderAfterSaleMap.get(orderItemId);
                afterSaleList = afterSaleList.stream().filter(e -> !AFTERSALE_STATUS_LIST_FOR_CLOSE.contains(e.getStatus())).collect(Collectors.toList());
                afterSaleCountMap.put(orderItemId, afterSaleList.stream().mapToInt(OrderAfterSaleResp::getAmount).sum());
                afterSalePriceMap.put(orderItemId, afterSaleList.stream().map(OrderAfterSaleResp::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));

                Integer preDeliveryAfterSaleAmount = afterSaleList.stream()
                        .filter(e ->
                                OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue().equals(e.getStatus())
                                        && OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(e.getAfterSaleType()))
                        .mapToInt(OrderAfterSaleResp::getAmount).sum();
                preDeliveryAfterSaleAmountMap.put(orderItemId, preDeliveryAfterSaleAmount);
            }

            AtomicInteger index = new AtomicInteger();
            Map<Integer, WarehouseStorageResp> finalWarehouseStorageMap = warehouseStorageMap;
            Map<String, PlanOrder> finalPlanOrderMap = planOrderMap;
            List<PaymentCombinedOrderDetailVO> finalPaymentCombinedDetailVOS = paymentCombinedDetailVOS;
            List<OrderExportVO> orderExportVOS = orderItemVOS.stream().map(orderItemVO -> {
                BigDecimal deliveryFee = null;

                OrderAddressResp finalOrderAddressDTO = new OrderAddressResp();
                // 地址
                StringBuffer address = new StringBuffer();
                if (orderAddressResp != null) {
                    finalOrderAddressDTO = orderAddressResp;
                    address.append(orderAddressResp.getProvince())
                            .append(orderAddressResp.getCity())
                            .append(orderAddressResp.getArea())
                            .append(orderAddressResp.getAddress());
                }


                // 多个订单商品项，运费算在第一个商品
                if (!deliveryFeeMap.containsKey(orderResp.getId())) {
                    deliveryFee = orderResp.getDeliveryFee();
                }
                deliveryFeeMap.put(orderResp.getId(), orderResp.getDeliveryFee());

                String payType = OrderPayTypeEnum.getDesc(orderResp.getPayType());
                if (Objects.equals(orderResp.getPayType(), OrderPayTypeEnum.NON_CASH_PAY.getType())) {
                    payType = infoByTenantId == null ? OrderPayTypeEnum.NON_CASH_PAY.getDesc() : infoByTenantId.getAccountName();
                }

                // 获取订单下配送信息
                int currentIndex = index.getAndIncrement();
                FulfillmentDeliveryResp fulfillmentDeliveryResp = CollectionUtil.isNotEmpty(fulfillmentDeliveryResps) && fulfillmentDeliveryResps.size() > currentIndex
                        ? fulfillmentDeliveryResps.get(currentIndex) : null;
                NoWarehouseDeliveryTypeEnum noWarehouseDeliveryTypeEnum = Optional.ofNullable(fulfillmentDeliveryResp).map(resp -> NoWarehouseDeliveryTypeEnum.getByType(resp.getDeliveryType()))
                        .orElse(null);
                String deliveryTypeDesc = Optional.ofNullable(noWarehouseDeliveryTypeEnum).map(NoWarehouseDeliveryTypeEnum::getDesc).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
                String deliveryCompany = Optional.ofNullable(fulfillmentDeliveryResp).map(resp -> resp.getLogisticsCompany()).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
                String deliveryNo = Optional.ofNullable(fulfillmentDeliveryResp).map(resp -> resp.getLogisticsNo()).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
                if (NoWarehouseDeliveryTypeEnum.NOT_NEED_DELIVERY == noWarehouseDeliveryTypeEnum) {
                    deliveryCompany = Optional.ofNullable(fulfillmentDeliveryResp).map(FulfillmentDeliveryResp::getRemark).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
                }
                // 仓库信息
                Integer warehouseType = orderResp.getWarehouseType();
                Pair<String, String> warehousePair = getWarehousePair(finalWarehouseStorageMap, tenantCompany, orderResp.getWarehouseType(), orderResp.getWarehouseNo());
                // 查询销售商品信息
                MarketItemDTO marketItemDto = marketItemService.detail(tenantId, orderItemVO.getItemId());
                MarketSpuVO marketSpuVO = marketService.detail(marketItemDto.getMarketId());
                OrderExportVO orderExportVO = new OrderExportVO();
                orderExportVO.setOrderNO(orderResp.getOrderNo());
                orderExportVO.setStoreName(merchantStoreDTO.getStoreName());
                orderExportVO.setOrderTime(orderResp.getCreateTime());
                orderExportVO.setPayTime(orderResp.getPayTime());
                orderExportVO.setPayType(payType);

                //假如组合支付需要补充明细
                if (payType.equals(com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getDesc())) {
                    String payTypeDesc = payType + "--";
                    for (PaymentCombinedOrderDetailVO paymentCombinedDetailVO : finalPaymentCombinedDetailVOS) {
                        PaymentTradeTypeEnum paymentTradeType = PaymentTradeTypeEnum.getPaymentTradeType(paymentCombinedDetailVO.getTradeType());
                        if (paymentTradeType == null) {
                            continue;
                        }
                        String tradeTypeDesc = paymentTradeType.getDetailDesc();
                        if (Objects.equals(paymentTradeType.getPayType(), PaymentTradeTypeEnum.NON_CASH.getPayType())) {
                            tradeTypeDesc = infoByTenantId == null ? PaymentTradeTypeEnum.NON_CASH.getDetailDesc() : infoByTenantId.getAccountName();
                        }

                        payTypeDesc = payTypeDesc + tradeTypeDesc + ":" + paymentCombinedDetailVO.getTotalPrice() + "元;";
                    }
                    orderExportVO.setPayType(payTypeDesc);
                }
                orderExportVO.setContactName(finalOrderAddressDTO.getContactName());
                orderExportVO.setContactPhone(finalOrderAddressDTO.getContactPhone());
                orderExportVO.setAddress(address.toString());
                orderExportVO.setItemId(orderItemVO.getItemId());
                orderExportVO.setItemCode(marketItemDto.getItemCode());
                orderExportVO.setSkuId(orderItemVO.getSkuId());
                orderExportVO.setSupplierTenantId(orderItemVO.getSupplierTenantId());
                orderExportVO.setTitle(orderItemVO.getTitle());
                orderExportVO.setGoodsType(GoodsTypeEnum.getShowDescByCodeV2(orderItemVO.getGoodsType()));
                orderExportVO.setSupplierName(orderItemVO.getSupplierName());
                orderExportVO.setSpecification(orderItemVO.getSpecification());
                orderExportVO.setSpecificationUnit(orderItemVO.getSpecificationUnit());
                orderExportVO.setPrice(orderItemVO.getPrice());
                orderExportVO.setAmount(orderItemVO.getAmount());
                orderExportVO.setItemTotalPrice(orderItemVO.getTotalPrice());
                orderExportVO.setAfterSaleCount(Optional.ofNullable(afterSaleCountMap.get(orderItemVO.getId())).orElse(NumberConstants.ZERO));
                orderExportVO.setAfterSalePrice(Optional.ofNullable(afterSalePriceMap.get(orderItemVO.getId())).orElse(BigDecimal.ZERO));
                orderExportVO.setStoreTypeDesc(StoreTypeEnum.getDesc(merchantStoreDTO.getType()));
                orderExportVO.setStatusDesc(OrderStatusEnum.getDesc(orderResp.getStatus()));
                orderExportVO.setFirstCategoryName(marketSpuVO.getFirstCategory());
                orderExportVO.setSecondCategoryName(marketSpuVO.getSecondCategory());
                orderExportVO.setThirdCategoryName(marketSpuVO.getThirdCategory());
                orderExportVO.setFirstClassificationName(marketSpuVO.getFirstClassificationName());
                orderExportVO.setSecondClassificationName(marketSpuVO.getSecondClassificationName());
                orderExportVO.setDeliveryFee(deliveryFee);
                orderExportVO.setDeliveryTime(orderResp.getDeliveryTime());
                orderExportVO.setFinishedTime(orderResp.getFinishedTime());
                orderExportVO.setRemark(orderResp.getRemark());
                orderExportVO.setGroupName(groupMap.get(orderResp.getStoreId()));
                orderExportVO.setStoreNo(merchantStoreDTO.getStoreNo());
                orderExportVO.setDeliveryTypeDesc(deliveryTypeDesc);
                orderExportVO.setDeliveryCompany(deliveryCompany);
                orderExportVO.setDeliveryNo(deliveryNo);
                orderExportVO.setWarehouseType(WarehouseTypeEnum.getByCode(warehouseType).getDesc());
                orderExportVO.setWarehouseName(warehousePair.getKey());
                orderExportVO.setWarehouseProvider(warehousePair.getValue());
                orderExportVO.setOrderSource(orderResp.getOrderSource());
                orderExportVO.setOrderSourceStr(getOrderSourceStr(orderResp.getOrderSource()));

                // 设置发货数量和发货重量
                orderItemVO.setWeight(Optional.ofNullable(orderItemVO.getWeight()).orElse(BigDecimal.ZERO));
                orderExportVO.setWeight(Optional.ofNullable(orderItemVO.getWeight()).orElse(BigDecimal.ZERO));
                orderExportVO.setTotalWeight(NumberUtil.mul(orderItemVO.getWeight(), orderItemVO.getAmount()));
                orderExportVO.setDeliveryNum(orderItemVO.getAmount() - Optional.ofNullable(preDeliveryAfterSaleAmountMap.get(orderItemVO.getId())).orElse(0));
                orderExportVO.setDeliveryTotalWeight(NumberUtil.mul(orderItemVO.getWeight(), orderExportVO.getDeliveryNum()));

                if(paymentItem!=null) {
                    orderExportVO.setOfflinePayType (paymentItem.getOfflinePayType ());
                    orderExportVO.setOfflinePayTypeStr (OfflinePayTypeEnum.getDescByCode (paymentItem.getOfflinePayType ()));
                    orderExportVO.setOfflinePayRemark(paymentItem.getRemark());
                }
                if (!StringUtils.isEmpty(orderResp.getPlanOrderNo())) {
                    orderExportVO.setPlanOrderNo(orderResp.getPlanOrderNo());
                    orderExportVO.setPlanOrderCreateTime(Optional.ofNullable(finalPlanOrderMap.get(orderResp.getPlanOrderNo())).map(e -> e.getCreateTime()).orElse(null));
                    orderExportVO.setRecommendReason(Optional.ofNullable(finalPlanOrderMap.get(orderResp.getPlanOrderNo())).map(e -> e.getRecommendReason()).orElse(null));
                }
                return orderExportVO;
            }).collect(Collectors.toList());
            list.addAll(orderExportVOS);
            return commonService.exportExcel(list, isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_ORDERS.getName() : ExcelTypeEnum.ORDER.getName());

        } else {
            // 处理查询条件
            boolean hasResultFlag = dealQueryConditions(orderQueryDTO, loginContextInfoDTO);
            if (StringUtils.isEmpty(orderQueryDTO.getStartTime()) && StringUtils.isEmpty(orderQueryDTO.getEndTime())) {
                orderQueryDTO.setStartTime(TimeUtils.getThreeMonthBeforeTime());
                orderQueryDTO.setEndTime(TimeUtils.getTodayString(TimeUtils.FORMAT));
            }
            if (hasResultFlag) {
                return generateOrderExportFileLoop(orderQueryDTO, isSupplierDistributor, loginContextInfoDTO, infoByTenantId);
            }
        }
        return commonService.exportExcel(list, isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_ORDERS.getName() : ExcelTypeEnum.ORDER.getName());
    }

    private String getOrderSourceStr(Integer orderSource) {
        if (OrderSourceEnum.INNER_SYSTEM.getValue().equals(orderSource)) {
            return "门店下单";
        }

        if (OrderSourceEnum.AGENT_ORDER.getValue().equals(orderSource)) {
            return "总部代下计划单";
        }

        if (OrderSourceEnum.OPENAPI.getValue().equals(orderSource)) {
            return "开放平台下单";
        }

        return null;
    }

    /**
     * 获取订单仓库信息
     *
     * @param warehouseStorageMap
     * @param warehouseType
     * @param warehouseNo
     * @return 仓库名称、仓库服务商
     */
    private Pair<String, String> getWarehousePair(Map<Integer, WarehouseStorageResp> warehouseStorageMap, TenantCompany tenantCompany, Integer warehouseType, String warehouseNo) {
        String warehouseServiceName = org.apache.commons.lang3.StringUtils.EMPTY;
        if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
            warehouseServiceName = Optional.ofNullable(tenantCompany).map(TenantCompany::getCompanyName).orElse(warehouseServiceName);
        }

        // 编号为空，返回无仓、三方仓名称
        if (Objects.isNull(warehouseNo)) {
            WarehouseTypeEnum warehouseTypeEnum = WarehouseTypeEnum.getByCode(warehouseType);
            return Pair.create(warehouseTypeEnum.getDesc(), warehouseServiceName);
        }

        String warehouseName = Optional.ofNullable(warehouseStorageMap.get(Integer.valueOf(warehouseNo))).map(WarehouseStorageResp::getWarehouseName)
                .orElse(org.apache.commons.lang3.StringUtils.EMPTY);
        return Pair.create(warehouseName, warehouseServiceName);
    }

    /**
     * 处理查询条件
     * v
     *
     * @param orderQueryDTO
     */
    private boolean dealQueryConditions(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 填充供应商查询条件
        populateSupplierQueryCondition(orderQueryDTO, loginContextInfoDTO);
        orderQueryDTO.setOrderItemIds(Lists.newArrayList());
//        if (!CollectionUtils.isEmpty(orderQueryDTO.getSupplierIds())) {
//
//            OrderItemSnapshotQueryReq req = new OrderItemSnapshotQueryReq();
//            req.setSupplierIds(orderQueryDTO.getSupplierIds());
//            req.setTenantId(orderQueryDTO.getTenantId());
//            DubboResponse<List<com.cosfo.ordercenter.client.resp.OrderItemSnapshotDTO>> response = orderItemSnapshotQueryService.queryList(req);
//            if (!response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
//                return false;
//            }
//
//            orderQueryDTO.setOrderItemIds(response.getData().stream().map(com.cosfo.ordercenter.client.resp.OrderItemSnapshotDTO::getOrderItemId).collect(Collectors.toList()));
//        }
        List<Long> storeIds = new ArrayList<>();
        // 门店类型和门店账号
        if (!CollectionUtils.isEmpty(orderQueryDTO.getMerchantStoreGroupIds())) {
            List<MerchantStoreGroupResultResp> mappings = merchantStoreGroupMappingService.selectByGroupId(orderQueryDTO.getMerchantStoreGroupIds(), orderQueryDTO.getTenantId());
            if (CollectionUtils.isEmpty(mappings)) {
                return false;
            }
            storeIds = mappings.stream().map(MerchantStoreGroupResultResp::getStoreId).collect(Collectors.toList());
        }

        if (orderQueryDTO.getStoreType() != null || !StringUtils.isEmpty(orderQueryDTO.getStoreName()) || Objects.nonNull(orderQueryDTO.getStoreNo())) {
            // 查询门店信息
            MerchantStoreQueryDTO query = MerchantStoreQueryDTO.builder()
                    .tenantId(orderQueryDTO.getTenantId())
                    .type(orderQueryDTO.getStoreType())
                    .storeName(orderQueryDTO.getStoreName())
                    .storeIds(storeIds)
                    .storeNo(orderQueryDTO.getStoreNo())
                    .build();
            List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreService.listByConditionNew(query);
            if (CollectionUtils.isEmpty(merchantStoreDTOS)) {
                return false;
            }
            storeIds = merchantStoreDTOS.stream().map(MerchantStoreDTO::getId).collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(orderQueryDTO.getStoreIds())) {
            if (CollectionUtils.isEmpty(storeIds)) {
                orderQueryDTO.setStoreIds(orderQueryDTO.getStoreIds());
            } else {
                storeIds = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(storeIds, orderQueryDTO.getStoreIds());
                if (CollectionUtils.isEmpty(storeIds)) {
                    return false;
                }
                orderQueryDTO.setStoreIds(storeIds);
            }
        } else {
            orderQueryDTO.setStoreIds(storeIds);
        }
        List<Long> accountIds = new ArrayList<>();
        // 注册手机号
        if (!StringUtils.isEmpty(orderQueryDTO.getPhone())) {
            MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
            merchantStoreAccountQueryReq.setTenantId(orderQueryDTO.getTenantId());
            merchantStoreAccountQueryReq.setPhone(orderQueryDTO.getPhone());
            List<MerchantStoreAccountResultResp> merchantStoreAccounts = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountsWithFuzzy(merchantStoreAccountQueryReq);
            if (CollectionUtils.isEmpty(merchantStoreAccounts)) {
                return false;
            }
            accountIds = merchantStoreAccounts.stream().map(MerchantStoreAccountResultResp::getId).collect(Collectors.toList());
        }

        orderQueryDTO.setAccountIds(accountIds);

        if (Objects.nonNull(orderQueryDTO.getCode())) {
            orderQueryDTO.setOnlinePayChannel(BillPayEnum.getPayChannel(orderQueryDTO.getCode()));
            orderQueryDTO.setPayType(orderQueryDTO.getPayType());
        }

        // 输入的商品编码非long类型，则是错误数据
        if (Objects.nonNull(orderQueryDTO.getItemId()) && !NumberUtil.isLong(orderQueryDTO.getItemId())) {
            return false;
        }
        // 商品编码以及商品名称条件
        if (Objects.nonNull(orderQueryDTO.getItemId()) || Objects.nonNull(orderQueryDTO.getTitle())) {
            MarketItemQueryDTO marketItemQueryDTO = new MarketItemQueryDTO();
            marketItemQueryDTO.setTitle(orderQueryDTO.getTitle());
            if (Objects.nonNull(orderQueryDTO.getItemId())) {
                marketItemQueryDTO.setId(Long.valueOf(orderQueryDTO.getItemId()));
            }
            marketItemQueryDTO.setTenantId(orderQueryDTO.getTenantId());
            List<MarketItem> marketItems = marketItemService.listAll(marketItemQueryDTO);
            if (CollectionUtils.isEmpty(marketItems)) {
                return false;
            }
            List<Long> itemIds = marketItems.stream().map(MarketItem::getId).collect(Collectors.toList());
            orderQueryDTO.setItemIds(itemIds);
        } else {
            orderQueryDTO.setItemIds(CollectionUtils.isEmpty(orderQueryDTO.getItemIds()) ? Collections.emptyList() : orderQueryDTO.getItemIds());
        }

        // 处理仓库编号
        WarehouseQueryEnum warehouseQueryEnum = WarehouseQueryEnum.getById(orderQueryDTO.getWarehouseNo());
        orderQueryDTO.setWarehouseType(transferWarehouseType(orderQueryDTO, warehouseQueryEnum));
        if (Objects.nonNull(warehouseQueryEnum)) {
            orderQueryDTO.setWarehouseNo(null);
        }
        return true;
    }

    private void populateSupplierQueryCondition(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        List<Long> supplierIds = Lists.newArrayList();
        if (orderQueryDTO.getSupplierId() != null) {
            supplierIds.add(orderQueryDTO.getSupplierId());
            orderQueryDTO.setSupplierIds(supplierIds);
            return;
        }
        TenantAccount tenantAccount = tenantAccountService.selectByAuthUserId(loginContextInfoDTO.getAuthUserId());
        if (Objects.nonNull(tenantAccount)) {
            supplierIds = tenantAccountSupplierMappingService.queryByAccountId(tenantAccount.getId());
        }
        orderQueryDTO.setSupplierIds(supplierIds);
    }

    private Integer transferWarehouseType(OrderQueryDTO orderQueryDTO, WarehouseQueryEnum warehouseQueryEnum) {
        if (Objects.isNull(orderQueryDTO.getWarehouseNo())) {
            return null;
        }
        // 获取查询的仓库类型
        Integer warehouseType = WarehouseTypeEnum.PROPRIETARY.getCode();
        if (Objects.nonNull(warehouseQueryEnum)) {
            warehouseType = warehouseQueryEnum == WarehouseQueryEnum.NO_WAREHOUSE ? WarehouseTypeEnum.NO_WAREHOUSE.getCode() : warehouseType;
            warehouseType = warehouseQueryEnum == WarehouseQueryEnum.THIRD_WAREHOUSE ? WarehouseTypeEnum.THREE_PARTIES.getCode() : warehouseType;
        }
        return warehouseType;
    }

    @Override
    public PendingMatterVO pendingMatter(LoginContextInfoDTO loginContextInfoDTO) {
        PendingMatterVO pendingMatterVO = new PendingMatterVO();
        Integer waitDeliveryNum = orderStatisticsQueryFacade.getWaitDeliveryQuantity(loginContextInfoDTO.getTenantId());
        pendingMatterVO.setWaitDeliveryNum(waitDeliveryNum);
        Integer waitAuditNum = getWaitAuditNum(loginContextInfoDTO.getTenantId());
        pendingMatterVO.setWaitAuditOrderNum(waitAuditNum);
        OrderAfterSaleCountReq req = new OrderAfterSaleCountReq();
        req.setTenantId(loginContextInfoDTO.getTenantId());
        req.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.UNAUDITED.getValue()));
        Integer waitDealOrderAfterSaleNum = orderAfterSaleQueryFacade.countOrderAfterSale(req);
        pendingMatterVO.setWaitAfterSaleNum(waitDealOrderAfterSaleNum);
        Integer waitAuditStoreNum = merchantStoreService.waitAuditStoreNum(loginContextInfoDTO);
        pendingMatterVO.setWaitAuditStoreNum(waitAuditStoreNum);
        Integer placeOrderDeadlineNum = merchantStoreService.placeOrderDeadlineNum(loginContextInfoDTO);
        pendingMatterVO.setPlaceOrderDeadlineNum (placeOrderDeadlineNum);
        return pendingMatterVO;
    }

    private Integer getWaitAuditNum(Long tenantId) {
        return orderStatisticsQueryFacade.getWaitAuditQuantity(tenantId);
    }

    @Override
    @Deprecated
    public ResultDTO confirmDelivery(Long orderId, LoginContextInfoDTO loginContextInfoDTO) {
        // 查询订单
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        OrderVO orderVO = OrderConvert.INSTANCE.convertResp2VO(orderResp);

        AssertCheckParams.notNull(orderVO, ResultDTOEnum.SERVER_ERROR.getCode(), "订单不存在");
        if (!orderVO.getWarehouseType().equals(WarehouseTypeEnum.NO_WAREHOUSE.getCode())) {
            throw new BizException("该订单不是自营仓订单，不能同步配送单状态");
        }

        OrderStatusUpdateReq req = new OrderStatusUpdateReq();
        req.setStatus(com.cosfo.ordercenter.client.common.OrderStatusEnum.DELIVERING.getCode());
        req.setOriginStatus(orderVO.getStatus());
        req.setTenantId(loginContextInfoDTO.getTenantId());
        req.setOrderId(orderId);
        DubboResponse<Boolean> response = orderCommandProvider.updateStatus(req);
        if (!response.isSuccess()) {
            log.error("订单更新状态异常，orderId={}, error={}", orderId, response.getMsg());
            return ResultDTO.fail("更新失败");
        }
        return ResultDTO.success();
    }

    @Override
    public ResultDTO querySelfLifting(String orderNo, Long tenantId) {
        OrderResp orderResp = orderQueryFacade.queryByNo(orderNo);

        if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(orderResp.getWarehouseType())) {
            throw new BizException("无仓订单不支持自提");
        }

        // 自营仓处理
        if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(orderResp.getWarehouseType())) {
            return builderProprietarySelfLifting(orderResp);
        }

        // 三方仓订单处理
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotResps = orderItemQueryFacade.queryByOrderId(orderResp.getId());
        if (CollectionUtils.isEmpty(orderItemAndSnapshotResps)) {
            throw new BizException("订单项信息不存在");
        }

        List<OrderSelfLiftingDTO> orderSelfLiftingDTOS = new ArrayList<>();

        // 订单项按照skuId分组
        Map<Long, List<OrderItemAndSnapshotResp>> skuIdMap = orderItemAndSnapshotResps.stream().collect(Collectors.groupingBy(OrderItemAndSnapshotResp::getSkuId));


        // 查询orderNo指定订单的货品sku和库存仓号
        List<GoodsSupplyOrderDetailQueryResp> supplyOrderDetailQueryResps = ofcFacade.queryGoodsSupplyOrderDetail(Collections.singletonList(orderNo));

        // 单个订单，sku按照仓分组
        Map<Long, Set<String>> warehouseNo2skuCodeMap = supplyOrderDetailQueryResps.stream().collect(Collectors.groupingBy(GoodsSupplyOrderDetailQueryResp::getWarehouseNo, Collectors.mapping(GoodsSupplyOrderDetailQueryResp::getSkuCode, Collectors.toSet())));
        // 三方仓订单鲜沐直供和代仓商品都是鲜沐仓库出库，所以只用查鲜沐仓库 tenantId=1
        List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(XianmuSupplyTenant.TENANT_ID, WarehouseSourceEnum.SUMMERFARM_WAREHOUSE, warehouseNo2skuCodeMap.keySet().stream().map(Long::intValue).collect(Collectors.toList()));
        Map<Integer, WarehouseStorageResp> warehouseStorageMap = warehouseStorageResps.stream().collect(Collectors.toMap(WarehouseStorageResp::getWarehouseNo, Function.identity(), (v1, v2) -> v1));

        List<String> skuCodes = supplyOrderDetailQueryResps.stream().map(GoodsSupplyOrderDetailQueryResp::getSkuCode).distinct().collect(Collectors.toList());
        List<ProductsMappingResp> productAgentSkuMappingList = productFacade.queryBySkuCodes(skuCodes);
        Map<String, Long> skucode2skuIdMap = productAgentSkuMappingList.stream().collect(Collectors.toMap(ProductsMappingResp::getSku, ProductsMappingResp::getSkuId, (v1, v2) -> v1));

        for (Map.Entry<Long, Set<String>> entry : warehouseNo2skuCodeMap.entrySet()) {
            Integer warehouseNo = entry.getKey().intValue();
            Set<String> skuCodeSet = entry.getValue();
            List<Long> skuIdList = skuCodeSet.stream().map(e -> skucode2skuIdMap.get(e)).collect(Collectors.toList());


            OrderSelfLiftingDTO dto = new OrderSelfLiftingDTO();
            dto.setWarehouseNo(warehouseNo);
            WarehouseStorageResp warehouseStorageResp = warehouseStorageMap.get(warehouseNo);
            dto.setPersonContact(Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getPersonContact).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
            dto.setPhone(Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getPhone).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
            // 使用仓库列表返回的地址，之前的地址包含了联系人信息
            String address = Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getAddress).orElse(dto.getAddress());
            dto.setAddress(address);

            List<OrderItemDTO> orderItemDTOList = skuIdList.stream().flatMap(skuId -> skuIdMap.get(skuId).stream().map(snapshot -> {
                OrderItemDTO itemDTO = new OrderItemDTO();
                itemDTO.setMainPicture(snapshot.getMainPicture());
                itemDTO.setItemId(snapshot.getItemId());
                itemDTO.setTitle(snapshot.getTitle());
                itemDTO.setSpecification(snapshot.getSpecification());
                itemDTO.setAmount(snapshot.getAmount());
                itemDTO.setSkuId(snapshot.getSkuId());
                return itemDTO;
            })).collect(Collectors.toList());

            dto.setOrderItemDTOList(orderItemDTOList);
            orderSelfLiftingDTOS.add(dto);
        }

        if (CollectionUtils.isEmpty(orderSelfLiftingDTOS)) {
            return ResultDTO.fail(ResultDTOEnum.ORDER_SELF_LIFTING_ERROR);
        }


        return ResultDTO.success(orderSelfLiftingDTOS);
    }

    /**
     * 组装自营仓的自提商品信息
     *
     * @param order
     * @return
     */
    private ResultDTO builderProprietarySelfLifting(OrderResp order) {
        OrderSelfLiftingDTO orderSelfLiftingDTO = new OrderSelfLiftingDTO();
        orderSelfLiftingDTO.setOrderNo(order.getOrderNo());
        orderSelfLiftingDTO.setWarehouseNo(Integer.valueOf(order.getWarehouseNo()));
        if (Objects.nonNull(order.getWarehouseNo())) {
            WarehouseStorageResp warehouseStorageResp = warehouseStorageQueryFacade.queryOneWarehouseStorage(Integer.valueOf(order.getWarehouseNo()));
            orderSelfLiftingDTO.setPersonContact(Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getPersonContact).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
            orderSelfLiftingDTO.setPhone(Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getPhone).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
            orderSelfLiftingDTO.setAddress(Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getAddress).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
        }
        List<OrderItemVO> orderItemVOS = orderItemQueryFacade.queryOrderItemByOrderId(order.getId());
        List<OrderItemDTO> orderItemDTOList = new ArrayList<>();
        for (OrderItemVO snapshot : orderItemVOS) {
            OrderItemDTO itemDTO = new OrderItemDTO();
            itemDTO.setMainPicture(snapshot.getMainPicture());
            itemDTO.setItemId(snapshot.getItemId());
            itemDTO.setTitle(snapshot.getTitle());
            itemDTO.setSpecification(snapshot.getSpecification());
            itemDTO.setAmount(snapshot.getAmount());
            itemDTO.setSkuId(snapshot.getSkuId());
            orderItemDTOList.add(itemDTO);
        }
        orderSelfLiftingDTO.setOrderItemDTOList(orderItemDTOList);
        return ResultDTO.success(Lists.newArrayList(orderSelfLiftingDTO));
    }

    @Override
    public ResultDTO selfLifting(OrderDTO orderDTO, Boolean selfCommit) {
        String orderNo = orderDTO.getOrderNo();
        Long tenantId = orderDTO.getTenantId();
        OrderResp orderResp = orderQueryFacade.queryByNo(orderNo);
        if (orderResp == null) {
            return ResultDTO.fail("订单不存在");
        }
        if (Objects.equals(orderResp.getWarehouseType(), WarehouseTypeEnum.NO_WAREHOUSE.getCode())) {
            return ResultDTO.fail(ResultDTOEnum.NO_WAREHOUSE_ORDER_ERROR);
        }
        if (!Objects.equals(orderResp.getStatus(), OrderStatusEnum.WAITING_DELIVERY.getCode())) {
            return ResultDTO.fail(ResultDTOEnum.ORDER_STATUS_ERROR);
        }

        List<OrderSelfLiftingDTO> orderSelfLiftingList = orderDTO.getOrderSelfLiftingDTOS();
        if (CollectionUtils.isEmpty(orderSelfLiftingList)) {
            return ResultDTO.fail(ResultDTOEnum.ORDER_SELF_LIFTING_DETAIL_ERROR);
        }

        // 调用OFC自提接口
        try {
            if (selfCommit) {
                TenantDTO tenantDTO = tenantService.queryTenantById(orderResp.getTenantId());
                ofcFacade.saasOrderSelfPickup(orderNo, orderResp.getTenantId(), Objects.isNull(tenantDTO) ? "" : tenantDTO.getTenantName(), orderSelfLiftingList);
            }
        } catch (ProviderException e) {
            log.error("发起OFC自提失败,orderno={}", orderNo, e);
            return ResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("发起OFC自提失败,orderno={}", orderNo, e);
            return ResultDTO.fail("发起自提失败，请稍后再试");
        }

        boolean executeResult = transactionTemplate.execute(status -> {
            try {
                // 插入自提信息
                for (OrderSelfLiftingDTO orderSelfLiftingDTO : orderSelfLiftingList) {
                    OrderSelfLifting orderSelfLifting = new OrderSelfLifting();
                    BeanUtils.copyProperties(orderSelfLiftingDTO, orderSelfLifting);
                    orderSelfLifting.setOrderNo(orderNo);
                    try {
                        orderSelfLiftingMapper.insert(orderSelfLifting);
                    } catch (DuplicateKeyException e) {
                        log.warn("自提信息重复插入异常，orderSelfLifting={}", orderSelfLifting, e);
                    }
                }
                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                return false;
            }
        });

        if (!executeResult) {
            return ResultDTO.fail("发起自提失败，请稍后再试");
        }

        // 变更订单状态为已完成 & 更新可申请售后时间
        OrderSelfLiftReq req = new OrderSelfLiftReq();
        req.setOrderId(orderResp.getId());
        DubboResponse<Boolean> response = orderCommandProvider.selfLifting(req);
        if (!response.isSuccess()) {
            return ResultDTO.fail("发起自提失败，请稍后再试");
        }
        return ResultDTO.success();
    }

    @Override
    public void receiveActualSelfLiftingTime(SummerfarmOrderOutDTO summerfarmOrderOutDTO) {
        String orderNo = summerfarmOrderOutDTO.getOrderNo();
        OrderResp orderResp = orderQueryFacade.queryByNo(orderNo);
        if (Objects.isNull(orderResp)) {
            return;
        }

        OrderSelfLifting orderSelfLifting = new OrderSelfLifting();
        orderSelfLifting.setOrderNo(orderNo);
        orderSelfLifting.setWarehouseNo(summerfarmOrderOutDTO.getWarehouseNo());
        orderSelfLifting.setActualTime(summerfarmOrderOutDTO.getOutTime());
        orderSelfLiftingMapper.updateActualTime(orderSelfLifting);
        log.info("订单：{}更新了仓库：{}的实际自提时间为：{}", orderNo, orderSelfLifting.getWarehouseNo(), orderSelfLifting.getActualTime());
    }

    @Override
    public List<OrderDTO> batchQuery(List<Long> orderIds, Long tenantId) {
        AssertCheckParams.isTrue(!CollectionUtils.isEmpty(orderIds), ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        List<OrderResp> orderResps = orderQueryFacade.queryByIds(orderIds);
        if (CollectionUtils.isEmpty(orderResps)) {
            return Collections.emptyList();
        }
        List<OrderDTO> orderDTOs = orderResps.stream().map(order -> {
            OrderDTO orderDTO = new OrderDTO();
            BeanUtils.copyProperties(order, orderDTO);
            return orderDTO;
        }).collect(Collectors.toList());
        return orderDTOs;
    }

    @Override
    public BillOrderInfoDTO queryBillInfo(List<Long> orderIds, Long tenantId) {
        AssertCheckParams.isTrue(!CollectionUtils.isEmpty(orderIds), ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        OrderItemSaleResp orderItemSaleResp = orderStatisticsQueryFacade.querySkuSaleQuantity(orderIds, tenantId);
        if (orderItemSaleResp == null) {
            throw new BizException("获取sku销售信息失败");
        }
        BillOrderInfoDTO billOrderInfoDTO = new BillOrderInfoDTO();
        billOrderInfoDTO.setSkuNum(orderItemSaleResp.getSkuQuantity());
        billOrderInfoDTO.setSkuAmount(orderItemSaleResp.getSaleQuantity());
        return billOrderInfoDTO;
    }

    @Override
    public List<BillOrderVO> queryBillOrderInfo(List<Long> orderIds, Long tenantId) {
        AssertCheckParams.isTrue(!CollectionUtils.isEmpty(orderIds), ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        // 查询订单项信息
        List<BillOrderDTO> billOrderDTOS = orderStatisticsQueryFacade.queryOrderDetailForBill(tenantId, orderIds);

        // 查询订单地址信息
        List<OrderAddressResp> orderAddressResps = orderAddressQueryFacade.queryByOrderIds(tenantId, orderIds);
        Map<Long, OrderAddressResp> orderAddressMap = orderAddressResps.stream().collect(Collectors.toMap(OrderAddressResp::getOrderId, item -> item));
        // 查询门店信息
        List<Long> storeIds = billOrderDTOS.stream().map(BillOrderDTO::getStoreId).collect(Collectors.toList());
        List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreService.batchQuery(storeIds, tenantId);
        Map<Long, MerchantStoreDTO> merchantStoreDTOMap = merchantStoreDTOS.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));
        // 查询门店分组信息
        Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(tenantId, storeIds);
        // 订单售后信息
        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        req.setTenantId(tenantId);
        req.setOrderIds(orderIds);
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryList(req);
        Map<Long, List<OrderAfterSaleResp>> orderAfterSaleMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderAfterSaleResps)) {
            orderAfterSaleMap = orderAfterSaleResps.stream().collect(Collectors.groupingBy(OrderAfterSaleResp::getOrderItemId));
        }

        List<Long> marketItemIds = billOrderDTOS.stream().map(BillOrderDTO::getItemId).collect(Collectors.toList());
        Map<Long, MarketItemDTO> marketItemMap = marketItemService.getMapByItemIds(marketItemIds);

        Map<Long, List<OrderAfterSaleResp>> finalOrderAfterSaleMap = orderAfterSaleMap;
        List<BillOrderVO> billOrderVOS = billOrderDTOS.stream().map(item -> {
            BillOrderVO billOrderVO = new BillOrderVO();
            BeanUtils.copyProperties(item, billOrderVO);
            billOrderVO.setPricingTypeDesc(AreaItemTypeEnum.getDescByCode(item.getPricingType()));
            billOrderVO.setSkuTotalPrice(NumberUtil.mul(item.getSkuPrice(), item.getAmount()));
            billOrderVO.setPayTypeDesc(PayTypeEnum.getPayType(item.getPayType()).getDesc());
            billOrderVO.setSupplyPrice(item.getSupplyPrice());
            // 地址信息
            OrderAddressResp orderAddress = orderAddressMap.get(item.getOrderId());
            StringBuffer stringBuffer = new StringBuffer(orderAddress.getProvince()).append(orderAddress.getCity()).append(orderAddress.getArea()).append(orderAddress.getAddress());
            billOrderVO.setAddress(stringBuffer.toString());
            billOrderVO.setPhone(orderAddress.getContactPhone());
            billOrderVO.setProvince(orderAddress.getProvince());
            billOrderVO.setCity(orderAddress.getCity());
            billOrderVO.setArea(orderAddress.getArea());
            // 门店信息
            MerchantStoreDTO merchantStoreDTO = merchantStoreDTOMap.get(item.getStoreId());
            billOrderVO.setStoreName(merchantStoreDTO.getStoreName());
            billOrderVO.setStoreId(item.getStoreId());
            billOrderVO.setStoreTypeDesc(MerchantStoreEnum.Type.getDesc(merchantStoreDTO.getType()));
            billOrderVO.setGroupName(groupMap.get(item.getStoreId()));
            // 售后单信息
            List<OrderAfterSaleResp> afterSaleDTOS = finalOrderAfterSaleMap.get(item.getOrderItemId());
            BigDecimal orderAfterSalePrice = BigDecimal.ZERO, orderAfterSaleDeliveryFee = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(afterSaleDTOS)) {
                for (OrderAfterSaleResp orderAfterSaleDTO : afterSaleDTOS) {
                    orderAfterSalePrice = NumberUtil.add(orderAfterSalePrice, orderAfterSaleDTO.getTotalPrice());
                    orderAfterSaleDeliveryFee = NumberUtil.add(orderAfterSaleDeliveryFee, orderAfterSaleDTO.getDeliveryFee());
                }
            }

            billOrderVO.setOrderAfterSalePrice(orderAfterSalePrice);
            billOrderVO.setOrderAfterSaleDeliveryFee(orderAfterSaleDeliveryFee);
            MarketItemDTO marketItemDTO = marketItemMap.get(item.getItemId());
            billOrderVO.setItemCode(Objects.isNull(marketItemDTO) ? "" : marketItemDTO.getItemCode());
            // TODO 前端暂时不改造 item-> skuId、 skuId -> supplierSkuId
            billOrderVO.setItemId(item.getItemId());
            billOrderVO.setSkuId(item.getItemId());
            billOrderVO.setSupplySkuId(item.getSkuId());
            if (Objects.nonNull(marketItemDTO)) {
                MarketClassificationDTO marketClassificationDTO = marketClassificationService.selectWholeClassification(tenantId, marketItemDTO.getMarketId());
                if (Objects.nonNull(marketClassificationDTO)) {
                    billOrderVO.setFirstClassificationName(marketClassificationDTO.getFirstClassificationName());
                    billOrderVO.setSecondClassificationName(marketClassificationDTO.getSecondClassificationName());
                }
            }
            return billOrderVO;
        }).collect(Collectors.toList());
        return billOrderVOS;
    }

    @Override
    public List<OrderDTO> queryBillOrderByStartTimeAndEndTime(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        OrderQueryReq req = OrderQueryReq.builder()
                .tenantId(tenantId)
                .startTime(startTime)
                .endTime(endTime)
                .statusList(Lists.newArrayList(OrderStatusEnum.FINISHED.getCode(), OrderStatusEnum.REFUNDED.getCode(), OrderStatusEnum.CLOSED.getCode()))
                .payType(2)
                .build();
        List<OrderResp> orderResps = orderQueryFacade.queryAllOrderListByCondition(req);
        List<OrderDTO> orderDTOs = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderResps)) {
            orderDTOs = orderResps.stream().map(order -> {
                OrderDTO orderDTO = new OrderDTO();
                BeanUtils.copyProperties(order, orderDTO);
                return orderDTO;
            }).collect(Collectors.toList());
        }
        return orderDTOs;
    }

    @Override
    @BizLogRecord(operationName = "关闭订单", bizKey = "#content['orderNo']", bizKeyTenantId = "#content['tenantId']", content = "#content['logContent']")
    public void closeOrder(Long orderId, LoginContextInfoDTO loginContextInfoDTO) {
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        if (orderResp == null) {
            throw new BizException("订单不存在");
        }

        OrderCloseReq req = new OrderCloseReq();
        req.setOrderId(orderId);
        req.setTenantId(loginContextInfoDTO.getTenantId());
        if (Objects.equals(orderResp.getPayType(), com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getCode())) {
            List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.querySuccessCombinedByOrderId(orderResp.getTenantId(), orderResp.getId());
            List<Integer> combinedPayTypes = combinedDetails.stream().map(detail -> {
                return PaymentTradeTypeEnum.getPayTypeByTradeType(detail.getTradeType());
            }).collect(Collectors.toList());
            req.setCombinedPayTypes(combinedPayTypes);
        }
        DubboResponse<Boolean> response = orderCommandProvider.close(req);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        if (!response.getData()) {
            throw new BizException("关闭订单失败");
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderNo", orderResp.getOrderNo());
        paramMap.put("tenantId", orderResp.getTenantId());
        paramMap.put("logContent", "");
        // 记录日志
        BizLogRecordContext.put("content", paramMap);
    }

    @Override
    @BizLogRecord(operationName = "订单审核通过", bizKey = "#content['orderNo']", bizKeyTenantId = "#content['tenantId']", content = "#content['logContent']")
    public void auditOrder(AuditOrderDTO auditOrderDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long orderId = auditOrderDTO.getOrderId ();
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        if (orderResp == null) {
            throw new BizException("订单不存在");
        }

        boolean isOrderAuditStatus = OrderStatusEnum.WAIT_AUDIT.getCode().equals(orderResp.getStatus());
        if (!isOrderAuditStatus) {
            throw new BizException("订单状态不支持审核");
        }
        List<PaymentItemDTO> paymentItemDTOS = paymentMapper.querySuccessPaymentByOrderIds (Collections.singletonList (orderId), loginContextInfoDTO.getTenantId ());
        chargeAudit(orderResp.getStoreId(),paymentItemDTOS,loginContextInfoDTO);

        if(CollectionUtil.isNotEmpty (paymentItemDTOS)&&paymentItemDTOS.get (0).getTradeType ().equals (PaymentTradeTypeEnum.OFFLINE_PAY.getDesc ())){
            PaymentItemDTO paymentItemDTO = auditOrderDTO.getPaymentItemDTO ();
            if(paymentItemDTO == null){
                throw new BizException ("线下支付订单审核支付相关信息不能为空");
            }
        }

        OrderAuditReq req = new OrderAuditReq();
        req.setOrderId(orderId);
        req.setTenantId(loginContextInfoDTO.getTenantId());
        Boolean flag = RpcResultUtil.handle(orderCommandProvider.auditSuccess(req));
        if (!flag) {
            throw new BizException("审核订单失败");
        }
//        更新 支付单
        updatePayment(auditOrderDTO.getPaymentItemDTO ());

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderNo", orderResp.getOrderNo());
        paramMap.put("tenantId", orderResp.getTenantId());
        paramMap.put("logContent", "");
        // 记录审核日志
        BizLogRecordContext.put("content", paramMap);
    }

    private void updatePayment(PaymentItemDTO paymentItemDTO) {
        if(paymentItemDTO == null){
            return;
        }
        PaymentItem paymentItem = paymentItemMapper.selectPaySuccessByOrderId (paymentItemDTO.getTenantId (),paymentItemDTO.getOrderId ());
        if(paymentItem == null){
            return;
        }
        paymentItem.setPaymentReceipt(paymentItemDTO.getPaymentReceipt ());
        paymentItem.setFinancialReceipt(paymentItemDTO.getFinancialReceipt ());
        paymentItem.setReceiptDate(paymentItemDTO.getReceiptDate ());
        paymentItem.setRemark(paymentItemDTO.getRemark ());
        paymentItem.setOfflinePayType(paymentItemDTO.getOfflinePayType ());
        paymentItem.setOrderId (paymentItem.getOrderId ());
        paymentItemMapper.updateByPrimaryKey (paymentItem);
    }

    private void chargeAudit(Long storeId,List<PaymentItemDTO> paymentItemDTOS, LoginContextInfoDTO loginContextInfoDTO){
        boolean canAudit = tenantFlowSchemeService.canAuditByAccountIdAndBizType(loginContextInfoDTO.getAuthUserId(), storeId, FlowRuleAuditBizTypeEnum.ORDER_AUDIT);
        if (!canAudit) {
            if(CollectionUtil.isNotEmpty (paymentItemDTOS)&&(!paymentItemDTOS.get (0).getTradeType ().equals (PaymentTradeTypeEnum.OFFLINE_PAY.getDesc ()))) {
                throw new BizException ("订单审核没有权限");
            }
        }
    }
    @Override
    @BizLogRecord(operationName = "修改订单", bizKey = "#content['orderNo']", bizKeyTenantId = "#content['tenantId']", content = "#content['logContent']")
    public void changeOrder(ChangeOrderDTO changeOrderDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (!isSupportChangeOrder(loginContextInfoDTO.getTenantId())) {
            throw new BizException("不支持改单");
        }
        OrderResp orderResp = orderQueryFacade.queryById(changeOrderDTO.getOrderId());
        if (orderResp == null) {
            throw new BizException("订单不存在");
        }

        boolean isOrderChangeStatus = OrderStatusEnum.WAIT_AUDIT.getCode().equals(orderResp.getStatus()) || OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderResp.getStatus());
        if (!isOrderChangeStatus) {
            throw new BizException("订单状态不支持改单");
        }

        if (OrderTypeEnum.COMBINE_ORDER.getValue().equals(orderResp.getOrderType())) {
            throw new BizException("组合包不支持改单");
        }

        if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderResp.getWarehouseType())) {
            throw new BizException("三方优选仓订单不支持改单");
        }

        // 订单未退款的剩余金额
        BigDecimal orderPrice = getOrderUnRefundTotalPrice(orderResp);

        Map<Long, OrderAfterSaleEnableResp> enableApplyDTOMap = orderAfterSaleQueryFacade.queryEnableApply(orderResp.getTenantId(), orderResp.getId(), null);

        List<Long> orderItemIdList = changeOrderDTO.getChangeOrderItemDTOList().stream().map(ChangeOrderDTO.ChangeOrderItemDTO::getOrderItemId).distinct().collect(Collectors.toList());
        List<OrderItemResp> orderItemResps = orderItemQueryFacade.queryByIds(orderItemIdList);
        Map<Long, OrderItemResp> orderItemMap = orderItemResps.stream().collect(Collectors.toMap(OrderItemResp::getId, Function.identity(), (v1, v2) -> v1));
        List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryByOrderItemIds(orderItemIdList);
        Map<Long, OrderItemSnapshotResp> orderItemSnapshotDTOMap = orderItemSnapshotResps.stream().collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, Function.identity(), (v1, v2) -> v1));

        // 如果是组合支付 查询组合明细 后续确认退款服务类型
        List<Integer> combinedPayTypes;
        if (Objects.equals(orderResp.getPayType(), com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getCode())) {
            List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.querySuccessCombinedByOrderId(orderResp.getTenantId(), orderResp.getId());
            combinedPayTypes = combinedDetails.stream().map(detail -> {
                return PaymentTradeTypeEnum.getPayTypeByTradeType(detail.getTradeType());
            }).collect(Collectors.toList());
        } else {
            combinedPayTypes = Lists.newArrayList();
        }

        StringBuilder stringBuilder = new StringBuilder();

        List<OrderAfterSaleAddReq> orderAfterSaleDTOS = changeOrderDTO.getChangeOrderItemDTOList().stream().map(e -> {

            OrderItemResp orderItemDTO = orderItemMap.get(e.getOrderItemId());

            // 校验改单参数
            checkChangeOrderItem(e, enableApplyDTOMap.get(e.getOrderItemId()), orderItemDTO);

            BigDecimal applyPrice = NumberUtil.mul(orderItemDTO.getPayablePrice(), e.getReduceQuantity());

            OrderItemSnapshotResp orderItemSnapshotDTO = orderItemSnapshotDTOMap.get(e.getOrderItemId());

            stringBuilder.append(String.format("%s/%s/%s/%s，改单前数量：%s，改单后数量：%s",
                    orderItemSnapshotDTO.getTitle(),
                    orderItemSnapshotDTO.getSpecification(),
                    orderItemSnapshotDTO.getSpecificationUnit(),
                    orderItemDTO.getItemId(),
                    e.getEnableApplyAmount(),
                    e.getRemainQuantity())).append("\n");

            Integer serviceType = OrderAfterSaleServiceTypeEnum.getServiceTypeByPayType(orderResp.getPayType(), OrderAfterSaleTypeEnum.NOT_SEND.getType());

            OrderAfterSaleAddReq orderAfterSaleDTO = new OrderAfterSaleAddReq();
            orderAfterSaleDTO.setOrderId(orderResp.getId());
            orderAfterSaleDTO.setOrderItemId(e.getOrderItemId());
            orderAfterSaleDTO.setAmount(e.getReduceQuantity());
            orderAfterSaleDTO.setAfterSaleType(OrderAfterSaleTypeEnum.NOT_SEND.getType());
            orderAfterSaleDTO.setServiceType(serviceType);
            orderAfterSaleDTO.setApplyPrice(applyPrice);
//        orderAfterSaleDTO.setTotalPrice(new BigDecimal("0.1"));
            orderAfterSaleDTO.setReason("订单改单");
            orderAfterSaleDTO.setWarehouseType(orderResp.getWarehouseType());
            // 售后请求来源，改单
            orderAfterSaleDTO.setReqSource(OrderAfterSaleReqSourceEnum.CHANGE_ORDER.getReqSource());
            orderAfterSaleDTO.setCombinedPayTypes(combinedPayTypes);
            return orderAfterSaleDTO;
        }).collect(Collectors.toList());

        RpcResultUtil.handle(orderAfterSaleCommandProvider.createPreDeliveryAfterSale(orderAfterSaleDTOS));

        // 累计退款金额
        BigDecimal currRefundPrice = orderAfterSaleDTOS.stream().map(OrderAfterSaleAddReq::getApplyPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        stringBuilder.append(String.format("改单退款(￥%s)=改单前结算金额(￥%s) - 改单后结算金额(￥%s)", currRefundPrice, orderPrice, orderPrice.subtract(currRefundPrice)));

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderNo", orderResp.getOrderNo());
        paramMap.put("tenantId", orderResp.getTenantId());
        paramMap.put("logContent", stringBuilder.toString());
        // 记录日志
        BizLogRecordContext.put("content", paramMap);
    }

    /**
     * 查询订单未退款的剩余的金额
     *
     * @param order
     * @return
     */
    private BigDecimal getOrderUnRefundTotalPrice(OrderResp order) {

        // 订单售后信息
        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setTenantId(order.getTenantId());
        req.setOrderIds(Lists.newArrayList(order.getId()));
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryList(req);
        if (CollectionUtils.isEmpty(orderAfterSaleResps)) {
            return order.getTotalPrice();
        }

        // 查询退款的金额
        BigDecimal refundPrice = orderAfterSaleResps.stream()
                .filter(e -> !AFTERSALE_STATUS_LIST_FOR_CLOSE.contains(e.getStatus()))
                .map(OrderAfterSaleResp::getTotalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return order.getTotalPrice().subtract(refundPrice);
    }

    private void checkChangeOrderItem(ChangeOrderDTO.ChangeOrderItemDTO changeOrderItemDTO, OrderAfterSaleEnableResp orderAfterSaleEnableApplyDTO, OrderItemResp orderItemDTO) {
        if (orderAfterSaleEnableApplyDTO == null || changeOrderItemDTO.getReduceQuantity() > orderAfterSaleEnableApplyDTO.getEnableApplyAmount()) {
            throw new BizException(String.format("商品%s可改单数量不足", orderItemDTO.getItemId()));
        }

        if (orderAfterSaleEnableApplyDTO.getEnableApplyAmount() != changeOrderItemDTO.getEnableApplyAmount()) {
            throw new BizException(String.format("商品%s可改单数量变化，请刷新后重试", orderItemDTO.getItemId()));
        }
    }

    private boolean isSupportChangeOrder(Long tenantId) {
        TenantCommonConfigVO changeOrderConfig = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfigEnum.TenantConfig.SUPPORT_CHANGE_ORDER_RULE.getConfigKey());
        return CommonCodeEnum.YES.getCode().toString().equals(changeOrderConfig.getConfigValue());
    }

    @Override
    public List<OrderBizLogVO> listOrderBizLog(Long orderId, LoginContextInfoDTO loginContextInfoDTO) {
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        if (orderResp == null) {
            throw new BizException("订单不存在");
        }

        BizLogQueryDTO queryDTO = new BizLogQueryDTO();
        queryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        queryDTO.setBizDomain("订单");
        queryDTO.setEntityType("订单操作记录");
        queryDTO.setBizKey(orderResp.getOrderNo());
        queryDTO.setPageIndex(BizLogConstants.DEFAULT_PAGE_INDEX);
        queryDTO.setPageSize(BizLogConstants.DEFAULT_PAGE_SIZE);
        PageInfo<BizLogListVO> bizLogListVOPageInfo = bizLogFacade.listBizLog(queryDTO);
        List<BizLogListVO> bizLogListVOList = bizLogListVOPageInfo.getList();
        if (CollectionUtils.isEmpty(bizLogListVOList)) {
            return Collections.emptyList();
        }

        return bizLogListVOList.stream().map(BizLogConvert::convert2OrderBizLogVO).collect(Collectors.toList());
    }

    @Override
    public CommonResult<Boolean> queryHavingAuditOrderAfterSale(CloseOrderDTO closeOrderDTO, LoginContextInfoDTO loginContextInfoDTO) {
        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.UNAUDITED.getValue()));
        req.setTenantId(loginContextInfoDTO.getTenantId());
        req.setOrderIds(Lists.newArrayList(closeOrderDTO.getOrderId()));
        // 查询订单是否有未处理售后单
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryList(req);

        return CommonResult.ok(CollectionUtils.isEmpty(orderAfterSaleResps));
    }

    @Override
    public List<OrderSkuSaleDTO> querySkuSaleAmount(List<Long> skuIds, Long tenantId) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        return querySkuSaleAmountByDays(skuIds, tenantId, 1);
    }

    @Override
    public List<OrderDTO> queryByOrderNos(List<String> orderNos, Long tenantId) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }

        List<OrderResp> orderResps = orderQueryFacade.queryByNos(orderNos);
        if (CollectionUtils.isEmpty(orderResps)) {
            return Collections.emptyList();
        }

        List<OrderDTO> orderDTOS = orderResps.stream().map(item -> {
            OrderDTO orderDTO = new OrderDTO();
            BeanUtils.copyProperties(item, orderDTO);
            return orderDTO;
        }).collect(Collectors.toList());
        return orderDTOS;
    }

    // TODO 外部接口调用直接走订单中心
    @Override
    public CommonResult<OrderVO> queryOrderInfoToOtherService(OrderQueryDTO orderDTO) {
        if (Objects.nonNull(orderDTO.getOrderNo())) {
            OrderResp orderResp = orderQueryFacade.queryByNo(orderDTO.getOrderNo());
            orderDTO.setOrderId(orderResp.getId());
        }

        return detail(orderDTO.getOrderId());
    }

    @Override
    public List<OrderSkuSaleDTO> querySkuSaleAmountByDays(List<Long> skuIds, Long tenantId, Integer days) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        // 获取当前时间
        String startTime = TimeUtils.getBeforeTimeString(new Date(), days);
        startTime = startTime + StringConstants.CUT_OFF_TIME;

        OrderSkuSaleReq orderSkuSaleReq = new OrderSkuSaleReq();
        orderSkuSaleReq.setTenantId(tenantId);
        orderSkuSaleReq.setSkuIds(skuIds);
        orderSkuSaleReq.setStartTime(LocalDateTimeUtil.parse(startTime, DatePattern.NORM_DATETIME_PATTERN));
        orderSkuSaleReq.setEndTime(LocalDateTime.now());

        List<OrderSkuQuantityResp> orderSkuQuantityResps = orderStatisticsQueryFacade.querySkuSaleQuantity(orderSkuSaleReq);
        if (CollectionUtils.isEmpty(orderSkuQuantityResps)) {
            return Collections.emptyList();
        }
        List<OrderSkuSaleDTO> orderSkuSaleDTOS = orderSkuQuantityResps.stream().map(e -> {
            OrderSkuSaleDTO orderSkuSaleDTO = new OrderSkuSaleDTO();
            orderSkuSaleDTO.setSkuId(e.getSkuId());
            orderSkuSaleDTO.setSaleAmount(e.getSaleQuantity());
            orderSkuSaleDTO.setWarehouseId(e.getWarehouseId());
            orderSkuSaleDTO.setCity(e.getCity());
            return orderSkuSaleDTO;
        }).collect(Collectors.toList());
        return orderSkuSaleDTOS;
    }

    @Override
    public List<OrderSkuSaleDTO> querySkuSaleAmountByDaysWithStoreNo(List<Long> skuIds, Long tenantId, Integer days) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }
        // 获取当前时间
        String startTime = TimeUtils.getBeforeTimeString(new Date(), days);
        startTime = startTime + StringConstants.CUT_OFF_TIME;

        OrderSkuSaleReq orderSkuSaleReq = new OrderSkuSaleReq();
        orderSkuSaleReq.setTenantId(tenantId);
        orderSkuSaleReq.setSkuIds(skuIds);
        orderSkuSaleReq.setStartTime(LocalDateTimeUtil.parse(startTime, DatePattern.NORM_DATETIME_PATTERN));
        orderSkuSaleReq.setEndTime(LocalDateTime.now());

        // 查询sku，仓库维度销量
        List<OrderSkuQuantityResp> orderSkuQuantityResps = orderStatisticsQueryFacade.querySkuSaleWithStoreNoQuantity(orderSkuSaleReq);

        if (CollectionUtils.isEmpty(orderSkuQuantityResps)) {
            return Collections.emptyList();
        }
        List<OrderSkuSaleDTO> orderSkuSaleDTOS = orderSkuQuantityResps.stream().map(e -> {
            OrderSkuSaleDTO orderSkuSaleDTO = new OrderSkuSaleDTO();
            orderSkuSaleDTO.setSkuId(e.getSkuId());
            orderSkuSaleDTO.setSaleAmount(e.getSaleQuantity());
            orderSkuSaleDTO.setWarehouseId(e.getWarehouseId());
            orderSkuSaleDTO.setCity(e.getCity());
            return orderSkuSaleDTO;
        }).collect(Collectors.toList());
        return orderSkuSaleDTOS;
    }

    @Override
    public List<OrderSkuSaleDTO> querySkuSaleAmountByCity(List<Long> skuIds, Long tenantId, Integer days) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }
        // 获取当前时间
        String startTime = TimeUtils.getBeforeTimeString(new Date(), days);
        startTime = startTime + StringConstants.CUT_OFF_TIME;

        OrderSkuSaleReq orderSkuSaleReq = new OrderSkuSaleReq();
        orderSkuSaleReq.setTenantId(tenantId);
        orderSkuSaleReq.setSkuIds(skuIds);
        orderSkuSaleReq.setStartTime(LocalDateTimeUtil.parse(startTime, DatePattern.NORM_DATETIME_PATTERN));
        orderSkuSaleReq.setEndTime(LocalDateTime.now());

        // 查询sku、城市维度销量
        List<OrderSkuQuantityResp> orderSkuQuantityResps = orderStatisticsQueryFacade.querySkuSaleWithCityQuantity(orderSkuSaleReq);
        if (CollectionUtils.isEmpty(orderSkuQuantityResps)) {
            return Collections.emptyList();
        }
        List<OrderSkuSaleDTO> orderSkuSaleDTOS = orderSkuQuantityResps.stream().map(e -> {
            OrderSkuSaleDTO orderSkuSaleDTO = new OrderSkuSaleDTO();
            orderSkuSaleDTO.setSkuId(e.getSkuId());
            orderSkuSaleDTO.setSaleAmount(e.getSaleQuantity());
            orderSkuSaleDTO.setWarehouseId(e.getWarehouseId());
            orderSkuSaleDTO.setCity(e.getCity());
            return orderSkuSaleDTO;
        }).collect(Collectors.toList());
        return orderSkuSaleDTOS;
    }

    @Override
    public void fixOrder() {
//        orderMapper.fixOrder();
    }

    @Override
    public boolean orderDelivery(OrderDeliveryDTO orderDeliveryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long orderId = orderDeliveryDTO.getOrderId();
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        AssertCheckParams.notNull(orderResp, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单不存在");
        AssertCheckParams.expectTrue(WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(orderResp.getWarehouseType()), ResultDTOEnum.PARAMETER_MISSING.getCode(), "非无仓订单不可立即配送");
        AssertCheckParams.expectTrue(OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode().equals(orderResp.getStatus()) || OrderStatusEnum.WAITING_DELIVERY.getCode().equals(orderResp.getStatus()),
                ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单当前状态不可配送");

        if (NoWarehouseDeliveryTypeEnum.LOGISTIC_DELIVERY.getType().equals(orderDeliveryDTO.getDeliveryType())) {
            AssertCheckParams.expectTrue(!StringUtils.isEmpty(orderDeliveryDTO.getDeliveryNo()) && orderDeliveryDTO.getDeliveryNo().length() <= NumberConstant.TWENTY,
                    ResultDTOEnum.SERVER_ERROR.getCode(), "物流单号不可超过20个字符");
            Matcher matcher = CN_PATTERN.matcher(orderDeliveryDTO.getDeliveryNo());
            AssertCheckParams.expectFalse(matcher.find(),
                    ResultDTOEnum.SERVER_ERROR.getCode(), "物流单号不可包含中文");
        }

        String redisKey = RedisKeyEnum.CM00002.join(orderId);
        RLock lock = redissonClient.getLock(redisKey);

        // 未获取到锁，退出
        AssertCheckParams.expectTrue(lock.tryLock(), ResultDTOEnum.SERVER_ERROR.getCode(), ResultDTOEnum.ORDER_CONCURRENCY_ERROR.getMessage());
        try {
            TenantAccountVO tenantAccountVO = getTenantAccountVO(loginContextInfoDTO);
            List<OrderItemResp> orderItemResps = orderItemQueryFacade.queryOrderItemList(orderId);
            Map<Long, Long> orderItemMap = orderItemResps.stream().collect(Collectors.toMap(OrderItemResp::getId, OrderItemResp::getItemId, (v1, v2) -> v1));

            fulfillmentOrderOperateFacade.startFulfillmentWithoutWarehouse(orderDeliveryDTO, orderResp.getOrderNo(), orderItemMap, tenantAccountVO.getNickname());

            OrderItemUpdateDeliveryQuantityReq req = new OrderItemUpdateDeliveryQuantityReq();
            req.setOrderId(orderId);
            List<OrderItemQuantity> orderItemQuantities = Lists.newArrayList();
            req.setOrderItemQuantities(orderItemQuantities);

            List<OrderDeliveryDTO.OrderItemDeliveryDTO> list = orderDeliveryDTO.getDeliveryDTOList().stream()
                    .sorted(Comparator.comparing(OrderDeliveryDTO.OrderItemDeliveryDTO::getOrderItemId)).collect(Collectors.toList());
            for (OrderDeliveryDTO.OrderItemDeliveryDTO orderItemDeliveryDTO : list) {
                OrderItemQuantity tmpObj = new OrderItemQuantity();
                tmpObj.setOrderItemId(orderItemDeliveryDTO.getOrderItemId());
                tmpObj.setQuantity(orderItemDeliveryDTO.getQuantity());
                orderItemQuantities.add(tmpObj);
            }

            DubboResponse<Boolean> response = orderItemCommandProvider.updateDeliveryQuantity(req);
            return response.isSuccess();
        } finally {
            lock.unlock();
        }

    }

    @Override
    public ExcelImportResDTO importOrderDelivery(Long tenantId, MultipartFile file, LoginContextInfoDTO loginContextInfoDTO) {
        // 读excel
        List<OrderDeliveryExcelDataInput> list = null;
        try {
            list = ExcelUtils.read(file.getInputStream(), OrderDeliveryExcelDataInput.class);
        } catch (Exception e) {
            log.error("OrderDeliveryDTO,读取失败,tenantID={}", tenantId, e);
            throw new ProviderException("表格读取失败，请检查数据格式后重新试试，或联系客服处理一下~");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException("导入发货数据不能为空");
        }
        log.info("OrderDelivery.excelList={}", JSON.toJSONString(list));
        if (list.size() > NumberConstants.TWO_HUNDRED) {
            throw new BizException("单次导入数量上限为200个");
        }
        try {

            // 根据订单号查询订单信息
            List<String> orderNos = list.stream().map(OrderDeliveryExcelDataInput::getOrderNo).collect(Collectors.toList());
            List<OrderResp> orderResps = orderQueryFacade.queryByNos(orderNos);
            Map<String, OrderResp> orderMap = orderResps.stream().collect(Collectors.toMap(OrderResp::getOrderNo, Function.identity()));
//            Map<String, com.cosfo.ordercenter.client.resp.OrderDTO> orderMap = orderFacade.queryOrderByOrderNos(
//                            orderNos)
//                    .stream().collect(Collectors.toMap(com.cosfo.ordercenter.client.resp.OrderDTO::getOrderNo, Function.identity()));

            // 判断是否是供应商角色
            boolean isSupplierRole = authRoleService.isSupplierRole(tenantId, loginContextInfoDTO.getAuthUserId());
            List<Long> supplierIds = isSupplierRole ?
                    tenantAccountSupplierMappingService.queryByAccountId(
                            tenantAccountService.selectByAuthUserId(loginContextInfoDTO.getAuthUserId()).getId())
                    : new ArrayList<>();

            // 最高4并发
            final int maxThreads = 4; // 最大并发线程数
            final int partitionSize = 20; // 每20条数据一个线程的基准
            final int maxPartitionThreshold = maxThreads * partitionSize; // 多线程的阈值
            int dataSize = list.size();
            int actualThreads; // 实际将要使用的线程数量

            if (dataSize <= maxPartitionThreshold) {
                actualThreads = Math.min(maxThreads, (int) Math.ceil(dataSize / (double) partitionSize));
            } else {
                // 数据超过80条时，平均分配给4个线程
                actualThreads = maxThreads;
            }

            log.info("批量发货线程数量={}", actualThreads);

            ThreadFactory threadFactory = new ThreadFactoryBuilder()
                    .setNameFormat("importOrderDelivery-pool-%d")
                    .build();

            ExecutorService executorService = new ThreadPoolExecutor(
                    actualThreads,
                    actualThreads,
                    0L,
                    TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<>(),
                    threadFactory
            );

            List<OrderDeliveryExcelDataInput> errorList = Collections.synchronizedList(new ArrayList<>());
            try {
                List<List<OrderDeliveryExcelDataInput>> partitions = new ArrayList<>();
                int partitionLength = actualThreads == maxThreads ? (int) Math.ceil(dataSize / (double) actualThreads) : partitionSize;
                for (int i = 0; i < dataSize; i += partitionLength) {
                    partitions.add(list.subList(i, Math.min(i + partitionLength, dataSize)));
                }

                // 等待所有的并发任务完成
                CompletableFuture.allOf(partitions.stream()
                        .map(partition -> CompletableFuture.runAsync(() -> {
                            processOrderDeliveryPartition(partition, errorList, supplierIds, isSupplierRole, tenantId, loginContextInfoDTO, orderMap);
                        }, executorService)).toArray(CompletableFuture[]::new)).join();

            } finally {
                executorService.shutdown();
            }

            // 异常数据写入excel
            String qiNiuFilePath = null;
            if (!CollectionUtils.isEmpty(errorList)) {
                ExcelTypeEnum excelType = isSupplierRole ? ExcelTypeEnum.IMPORT_ORDER_DELIVERY_SUPPLIER : ExcelTypeEnum.IMPORT_ORDER_DELIVERY;
                String filePath = commonService.exportExcel(errorList, excelType.getName());
                qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "导入表格批量发货错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xlsx");
                commonService.deleteFile(filePath);
            }
            // 返回导入结果
            ExcelImportResDTO excelImportResDTO = new ExcelImportResDTO();
            excelImportResDTO.setFailRow(org.apache.commons.collections4.CollectionUtils.isEmpty(errorList) ? NumberConstants.ZERO : errorList.size());
            excelImportResDTO.setSuccessRow(list.size() - excelImportResDTO.getFailRow());
            excelImportResDTO.setErrorFilePath(qiNiuFilePath);
            return excelImportResDTO;
        } catch (Exception e) {
            log.error("请检查excel格式，或者联系管理员", e);
            throw new BizException("请检查excel格式，或者联系管理员");
        }
    }


    @Override
    public OrderDetailDeliveryVO deliveryDetails(Long orderId, LoginContextInfoDTO loginContextInfoDTO) {
        OrderVO orderVO = queryOrderVOByOrderId(orderId);
        AssertCheckParams.notNull(orderVO, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单不存在");
        OrderTenantIsolateCheckUtil.check(orderVO);
        // 获取订单项信息
        List<OrderItemVO> orderItemVOS = orderItemQueryFacade.queryOrderItemByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderItemVOS)) {
            throw new BizException("订单项不存在");
        }

        // 三方仓订单 城配履约 不展示快递信息（即使有）
        if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderVO.getWarehouseType()) && FulfillmentTypeEnum.CITY_DELIVERY.getValue().equals(orderVO.getFulfillmentType())) {
            return new OrderDetailDeliveryVO();
        }

        Map<Long, OrderItemVO> itemOrderMap = orderItemVOS.stream().collect(Collectors.toMap(OrderItemVO::getItemId, Function.identity(), (v1, v2) -> v1));

        // 查询自提信息
        List<OrderSelfLiftingDTO> orderSelfLiftingDTOS = orderSelfLiftingMapper.selectByOrderNo(orderVO.getOrderNo());
        // 是否是自营仓自提单
        boolean proprietaryLift = (WarehouseTypeEnum.PROPRIETARY.getCode().equals(orderVO.getWarehouseType()) && !CollectionUtils.isEmpty(orderSelfLiftingDTOS));
        WarehouseStorageResp warehouseStorageResp = null;
        OrderSelfLiftingDTO orderSelfLiftingDTO = null;
        if (proprietaryLift) {
            warehouseStorageResp = warehouseStorageQueryFacade.queryOneWarehouseStorage(Integer.valueOf(orderVO.getWarehouseNo()));
            orderSelfLiftingDTO = orderSelfLiftingDTOS.get(NumberConstant.ZERO);
        }
        // rpc查询已配送清单
        QueryFulfillmentDeliveryReq queryFulfillmentDeliveryReq = new QueryFulfillmentDeliveryReq();
        queryFulfillmentDeliveryReq.setOrderNoList(Lists.newArrayList(orderVO.getOrderNo()));
        List<FulfillmentDeliveryResp> fulfillmentDeliveryResps = Optional.ofNullable(fulfillmentOrderQueryFacade.queryOrderDelivery(queryFulfillmentDeliveryReq)).orElse(Lists.newArrayList());

        Map<String, List<FulfillmentDeliveryResp>> batchMap = fulfillmentDeliveryResps.stream().collect(Collectors.groupingBy(FulfillmentDeliveryResp::getBatchNo));

        // 组装已配送数据信息
        List<OrderDeliveryVO> deliveredList = Lists.newArrayList();
        for (Map.Entry<String, List<FulfillmentDeliveryResp>> batchEntry : batchMap.entrySet()) {
            List<FulfillmentDeliveryResp> list = batchEntry.getValue();
            OrderDeliveryVO orderDeliveryVO = builderOrderDeliveryVO(list, itemOrderMap, orderVO, orderSelfLiftingDTO, proprietaryLift, warehouseStorageResp);
            deliveredList.add(orderDeliveryVO);
        }
        QueryFulfillmentWaitDeliveryReq queryFulfillmentWaitDeliveryReq = new QueryFulfillmentWaitDeliveryReq();
        queryFulfillmentWaitDeliveryReq.setOrderNoList(Lists.newArrayList(orderVO.getOrderNo()));
        List<FulfillmentWaitDeliveryResp> fulfillmentWaitDeliveryResps = Optional.ofNullable(fulfillmentOrderQueryFacade.queryWaitDelivery(queryFulfillmentWaitDeliveryReq))
                .orElse(Lists.newArrayList());

        // 组装待配送信息
        List<OrderDeliveryVO.OrderDeliveryItemVO> waitDeliveryItemList = builderWaitDeliveryItemVoList(itemOrderMap, fulfillmentWaitDeliveryResps);

        OrderDetailDeliveryVO orderDetailDeliveryVO = new OrderDetailDeliveryVO();
        orderDetailDeliveryVO.setDeliveredList(deliveredList);
        orderDetailDeliveryVO.setWaitDeliveryItemList(waitDeliveryItemList);
        return orderDetailDeliveryVO;
    }

    /**
     * 组装待配送信息
     *
     * @param itemOrderMap
     * @param fulfillmentWaitDeliveryResps
     * @return
     */
    private List<OrderDeliveryVO.OrderDeliveryItemVO> builderWaitDeliveryItemVoList(Map<Long, OrderItemVO> itemOrderMap, List<FulfillmentWaitDeliveryResp> fulfillmentWaitDeliveryResps) {
        Map<String, List<FulfillmentWaitDeliveryResp>> itemDeliveryMap = fulfillmentWaitDeliveryResps.stream().collect(Collectors.groupingBy(FulfillmentWaitDeliveryResp::getItemId));

        List<OrderDeliveryVO.OrderDeliveryItemVO> waitList = Lists.newArrayList();
        for (Map.Entry<Long, OrderItemVO> itemOrderEntry : itemOrderMap.entrySet()) {
            List<FulfillmentWaitDeliveryResp> itemDeliveryResp = itemDeliveryMap.get(String.valueOf(itemOrderEntry.getKey()));
            Integer quantity = CollectionUtil.isNotEmpty(itemDeliveryResp) ? itemDeliveryResp.stream().mapToInt(FulfillmentWaitDeliveryResp::getQuantity).sum() : NumberConstant.ZERO;
            if (quantity <= NumberConstant.ZERO) {
                continue;
            }
            OrderItemVO orderItemVO = itemOrderEntry.getValue();

            OrderDeliveryVO.OrderDeliveryItemVO orderDeliveryItemVO = new OrderDeliveryVO.OrderDeliveryItemVO();
            orderDeliveryItemVO.setOrderItemId(orderItemVO.getId());
            orderDeliveryItemVO.setOrderId(orderItemVO.getOrderId());
            orderDeliveryItemVO.setDeliveryQuantity(quantity);
            orderDeliveryItemVO.setTitle(orderItemVO.getTitle());
            orderDeliveryItemVO.setMainPicture(orderItemVO.getMainPicture());
            orderDeliveryItemVO.setSpecification(orderItemVO.getSpecification());
            orderDeliveryItemVO.setSpecificationUnit(orderItemVO.getSpecificationUnit());
            orderDeliveryItemVO.setItemId(itemOrderEntry.getKey());
            orderDeliveryItemVO.setSkuId(orderItemVO.getSkuId());
            waitList.add(orderDeliveryItemVO);
        }
        return waitList;
    }

    /**
     * 转换为配送数据类型
     *
     * @param list
     * @param itemOrderMap
     * @param orderVO
     * @param orderSelfLiftingDTO
     * @param proprietaryLift
     * @param warehouseStorageResp
     * @return
     */
    private OrderDeliveryVO builderOrderDeliveryVO(List<FulfillmentDeliveryResp> list, Map<Long, OrderItemVO> itemOrderMap, OrderVO orderVO,
                                                   OrderSelfLiftingDTO orderSelfLiftingDTO, boolean proprietaryLift, WarehouseStorageResp warehouseStorageResp) {
        AssertCheckParams.expectTrue(CollectionUtil.isNotEmpty(list), ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单不存在");
        FulfillmentDeliveryResp resp = list.get(NumberConstant.ZERO);
        OrderDeliveryVO orderDeliveryVO = new OrderDeliveryVO();
        orderDeliveryVO.setDeliveryCompany(resp.getLogisticsCompany());
        orderDeliveryVO.setDeliveryNo(resp.getLogisticsNo());
        orderDeliveryVO.setDeliveryType(resp.getDeliveryType());
        orderDeliveryVO.setRemark(resp.getRemark());
        orderDeliveryVO.setBatchNo(resp.getBatchNo());
        orderDeliveryVO.setWarehouseName(resp.getWarehouseName());
        orderDeliveryVO.setOrderId(orderVO.getOrderId());

        if (Objects.nonNull(orderDeliveryVO.getDeliveryNo())) {
            orderDeliveryVO.setJumpUrl(builderJumpUrl(orderDeliveryVO.getDeliveryCompany(), orderDeliveryVO.getDeliveryNo()));
        }

        // 三方仓订单快递履约 配送信息 仅显示快递单号和快递公司
        if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderVO.getWarehouseType()) && FulfillmentTypeEnum.EXPRESS_DELIVERY.getValue().equals(orderVO.getFulfillmentType())){
            return orderDeliveryVO;
        }

        if (proprietaryLift) {
            orderDeliveryVO.setDeliveryTime(orderSelfLiftingDTO.getActualTime());
            // 自提为2
            orderDeliveryVO.setDeliveryType(2);
            orderDeliveryVO.setWarehouseNo(orderVO.getWarehouseNo());
            orderDeliveryVO.setSelfLiftingAddress(orderSelfLiftingDTO.getAddress());
            orderDeliveryVO.setWarehouseName(Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getWarehouseName).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
            orderDeliveryVO.setContact(Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getPersonContact).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
            orderDeliveryVO.setPhone(Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getPhone).orElse(org.apache.commons.lang3.StringUtils.EMPTY));
        }

        List<OrderDeliveryVO.OrderDeliveryItemVO> orderDeliveryItemVOList = list.stream().map(fulfillmentDeliveryResp -> {
            OrderDeliveryVO.OrderDeliveryItemVO orderDeliveryItemVO = new OrderDeliveryVO.OrderDeliveryItemVO();
            Long itemId = Long.valueOf(fulfillmentDeliveryResp.getItemId());
            OrderItemVO orderItemVO = itemOrderMap.get(itemId);
            orderDeliveryItemVO.setOrderItemId(orderItemVO.getId());
            orderDeliveryItemVO.setOrderId(orderVO.getOrderId());
            orderDeliveryItemVO.setItemId(itemId);
            orderDeliveryItemVO.setDeliveryQuantity(fulfillmentDeliveryResp.getQuantity());
            orderDeliveryItemVO.setTitle(orderItemVO.getTitle());
            orderDeliveryItemVO.setMainPicture(orderItemVO.getMainPicture());
            orderDeliveryItemVO.setSpecification(orderItemVO.getSpecification());
            orderDeliveryItemVO.setSpecificationUnit(orderItemVO.getSpecificationUnit());
            orderDeliveryItemVO.setSkuId(orderItemVO.getSkuId());
            return orderDeliveryItemVO;
        }).collect(Collectors.toList());
        orderDeliveryVO.setOrderDeliveryItemVOList(orderDeliveryItemVOList);
        return orderDeliveryVO;
    }

    private String builderJumpUrl(String deliveryCompany, String deliveryNo) {
        return String.format(Constants.DELIVERY_QUERY_URL, deliveryCompany, deliveryNo);
    }

    @Override
    public void updateDelivery(OrderDeliveryUpdateDTO orderDeliveryUpdateDTO, LoginContextInfoDTO loginContextInfoDTO) {
        TenantAccountVO tenantAccountVO = getTenantAccountVO(loginContextInfoDTO);
        OrderResp orderResp = orderQueryFacade.queryById(orderDeliveryUpdateDTO.getOrderId());
        OrderVO orderVO = OrderConvert.INSTANCE.convertResp2VO(orderResp);
        AssertCheckParams.notNull(orderVO, ResultDTOEnum.SERVER_ERROR.getCode(), "订单不存在");
        AssertCheckParams.expectTrue(OrderStatusEnum.isWaitStatusOrder(orderVO.getStatus()) || OrderStatusEnum.OUT_OF_STORAGE.getCode().equals(orderVO.getStatus())
                        || OrderStatusEnum.DELIVERING.getCode().equals(orderVO.getStatus()),
                ResultDTOEnum.SERVER_ERROR.getCode(), "订单当前状态不可修改履约单信息");

        if (NoWarehouseDeliveryTypeEnum.LOGISTIC_DELIVERY.getType().equals(orderDeliveryUpdateDTO.getDeliveryType())) {
            AssertCheckParams.expectTrue(!StringUtils.isEmpty(orderDeliveryUpdateDTO.getDeliveryNo()) && orderDeliveryUpdateDTO.getDeliveryNo().length() <= NumberConstant.TWENTY,
                    ResultDTOEnum.SERVER_ERROR.getCode(), "物流单号不可超过20个字符");
            Matcher matcher = CN_PATTERN.matcher(orderDeliveryUpdateDTO.getDeliveryNo());
            AssertCheckParams.expectFalse(matcher.find(),
                    ResultDTOEnum.SERVER_ERROR.getCode(), "物流单号不可包含中文");
        }

        fulfillmentOrderOperateFacade.updateDeliveryInfo(orderDeliveryUpdateDTO, orderVO, tenantAccountVO.getNickname());
    }

    @Override
    public Integer queryNeedDeliveryData(BatchDeliveryNotifyDTO batchDeliveryNotifyDTO, LoginContextInfoDTO loginContextInfoDTO) {
        List<String> orderNoList = queryNeedDeliveryOrderIds(batchDeliveryNotifyDTO, loginContextInfoDTO);
        return orderNoList.size();
    }

    @Override
    public OrderBatchNotifyVO batchDeliveryNotify(BatchDeliveryNotifyDTO batchDeliveryNotifyDTO, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.expectTrue(batchDeliveryNotifyDTO.getCutOffTime().isBefore(LocalDateTime.now()), ResultDTOEnum.SERVER_ERROR.getCode(), "截止时间不能大于当前时间");

        List<String> orderNoList = queryNeedDeliveryOrderIds(batchDeliveryNotifyDTO, loginContextInfoDTO);
        AssertCheckParams.expectTrue(org.apache.commons.collections.CollectionUtils.isNotEmpty(orderNoList), ResultDTOEnum.SERVER_ERROR.getCode(), "没有符合条件的订单需要通知仓库配送");
        AssertCheckParams.expectTrue(orderNoList.size() <= NumberConstant.TWO_HUNDRED, ResultDTOEnum.SERVER_ERROR.getCode(), "批量订单数量不能超过200条");

        TenantAccountVO tenantAccountVO = getTenantAccountVO(loginContextInfoDTO);
        return startFulfillment(orderNoList, loginContextInfoDTO, tenantAccountVO.getNickname());
    }

    @Override
    public OrderBatchNotifyVO autoBatchDeliveryNotify(BatchDeliveryNotifyDTO batchDeliveryNotifyDTO, Long tenantId) {
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(tenantId);
        List<String> orderNoList = queryNeedDeliveryOrderIds(batchDeliveryNotifyDTO, loginContextInfoDTO);
        if(CollectionUtils.isEmpty(orderNoList)){
            log.info("没有符合条件的订单需要通知仓库配送");
            return null;
        }

        return startFulfillment(orderNoList, loginContextInfoDTO, "系统自动");
    }

    @Override
    public OrderBatchNotifyVO deliveryNotify(Long orderId, LoginContextInfoDTO loginContextInfoDTO) {
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        AssertCheckParams.notNull(orderResp, ResultDTOEnum.SERVER_ERROR.getCode(), "订单不存在");
        AssertCheckParams.expectTrue(Objects.equals(orderResp.getWarehouseType(), WarehouseTypeEnum.PROPRIETARY.getCode()), ResultDTOEnum.SERVER_ERROR.getCode(), "订单非自营仓类型不可通知仓库配送");
        AssertCheckParams.expectTrue(orderResp.getTenantId().equals(loginContextInfoDTO.getTenantId()), ResultDTOEnum.SERVER_ERROR.getCode(), "无权操作该订单");
        AssertCheckParams.expectTrue(Objects.equals(orderResp.getStatus(), OrderStatusEnum.WAITING_DELIVERY.getCode()), ResultDTOEnum.SERVER_ERROR.getCode(), "订单当前状态不可通知仓库配送");

        List<String> orderNoList = Lists.newArrayList(orderResp.getOrderNo());
        TenantAccountVO tenantAccountVO = getTenantAccountVO(loginContextInfoDTO);
        OrderBatchNotifyVO orderBatchNotifyVO = startFulfillment(orderNoList, loginContextInfoDTO, tenantAccountVO.getNickname());
        List<OrderBatchNotifyVO.NotifyErrorVO> notifyErrorVOS = Optional.ofNullable(orderBatchNotifyVO).map(OrderBatchNotifyVO::getNotifyErrorList).orElse(null);
        if (CollectionUtil.isNotEmpty(notifyErrorVOS)) {
            throw new BizException(notifyErrorVOS.get(NumberConstant.ZERO).getMessage());
        }
        return orderBatchNotifyVO;
    }

    @Override
    public List<String> queryFastMallList(String expressName) {
        List<FastMallResp> fastMallRespList = fastMallServiceQueryFacade.queryFastMallList();
        if (CollectionUtils.isEmpty(fastMallRespList)) {
            return Lists.newArrayList();
        }
        List<String> expressNameList = fastMallRespList.stream().map(FastMallResp::getFastName).collect(Collectors.toList());

        // 模糊查询
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(expressName)) {
            List<String> tempNameList = expressNameList.stream().filter(name -> name.contains(expressName)).collect(Collectors.toList());
            // 兜底展示其它
            if (CollectionUtil.isEmpty(tempNameList)) {
                tempNameList = expressNameList.stream().filter(name -> name.contains(Constants.OTHER)).collect(Collectors.toList());
            }
            expressNameList = tempNameList;
        }

        // 按照汉字拼音字母排序
        Collections.sort(expressNameList, Collator.getInstance(java.util.Locale.CHINA));

        // 其它放在最后
        if (expressNameList.contains(Constants.OTHER)) {
            expressNameList.remove(Constants.OTHER);
            expressNameList.add(Constants.OTHER);
        }
        return expressNameList;
    }

    @Override
    public List<WarehouseStorageVO> querySelfWarehouseList(SelfWarehouseDTO selfWarehouseDTO, LoginContextInfoDTO merchantInfoDTO) {
        List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(merchantInfoDTO.getTenantId(), WarehouseSourceEnum.SAAS_WAREHOUSE, null);
        List<WarehouseStorageVO> WarehouseStorageList = warehouseStorageResps.stream().map(warehouseStorageResp -> {
            WarehouseStorageVO warehouseStorageVO = new WarehouseStorageVO();
            warehouseStorageVO.setWarehouseName(warehouseStorageResp.getWarehouseName());
            warehouseStorageVO.setWarehouseNo(warehouseStorageResp.getWarehouseNo());
            warehouseStorageVO.setAddress(warehouseStorageResp.getAddress());
            return warehouseStorageVO;
        }).collect(Collectors.toList());

        // 如果需要构建三方仓、无仓数据返回给前端
        Boolean needNoWareHouseData = selfWarehouseDTO.getNeedNoWareHouseData();
        Boolean needThirdWareHouseData = selfWarehouseDTO.getNeedThirdWareHouseData();
        if (Boolean.TRUE.equals(needThirdWareHouseData)) {
            WarehouseStorageVO warehouseStorageVO = new WarehouseStorageVO();
            warehouseStorageVO.setWarehouseName(WarehouseQueryEnum.THIRD_WAREHOUSE.getName());
            warehouseStorageVO.setWarehouseNo(WarehouseQueryEnum.THIRD_WAREHOUSE.getId());
            WarehouseStorageList.add(NumberConstant.ZERO, warehouseStorageVO);
        }
        if (Boolean.TRUE.equals(needNoWareHouseData)) {
            WarehouseStorageVO warehouseStorageVO = new WarehouseStorageVO();
            warehouseStorageVO.setWarehouseName(WarehouseQueryEnum.NO_WAREHOUSE.getName());
            warehouseStorageVO.setWarehouseNo(WarehouseQueryEnum.NO_WAREHOUSE.getId());
            WarehouseStorageList.add(NumberConstant.ZERO, warehouseStorageVO);
        }

        return WarehouseStorageList;
    }

    @Override
    public Long queryOrderSimpleDto(String orderNo) {
        OrderResp orderResp = orderQueryFacade.queryByNo(orderNo);
        return Optional.ofNullable(orderResp).map(OrderResp::getId).orElse(null);
    }

    @Override
    public List<WarehouseStorageVO> queryWarehouseList(SelfWarehouseDTO selfWarehouseDTO, LoginContextInfoDTO merchantInfoDTO) {
        List<WarehouseStorageVO> resultList = Lists.newArrayList();
        // 如果需要构建三方仓、无仓数据返回给前端
        Boolean needNoWareHouseData = selfWarehouseDTO.getNeedNoWareHouseData();
        Boolean needThirdWareHouseData = selfWarehouseDTO.getNeedThirdWareHouseData();
        if (Boolean.TRUE.equals(needNoWareHouseData)) {
            WarehouseStorageVO warehouseStorageVO = new WarehouseStorageVO();
            warehouseStorageVO.setWarehouseName(WarehouseQueryEnum.NO_WAREHOUSE.getName());
            warehouseStorageVO.setWarehouseNo(WarehouseQueryEnum.NO_WAREHOUSE.getId());
            resultList.add(warehouseStorageVO);
        }
        if (Boolean.TRUE.equals(needThirdWareHouseData)) {
            WarehouseStorageVO warehouseStorageVO = new WarehouseStorageVO();
            warehouseStorageVO.setWarehouseName(WarehouseQueryEnum.THIRD_WAREHOUSE.getName());
            warehouseStorageVO.setWarehouseNo(WarehouseQueryEnum.THIRD_WAREHOUSE.getId());
            resultList.add(warehouseStorageVO);
        }

        WarehouseStorageVO warehouseStorageVO = new WarehouseStorageVO();
        warehouseStorageVO.setWarehouseName(WarehouseQueryEnum.SELF_WAREHOUSE.getName());
        warehouseStorageVO.setWarehouseNo(WarehouseQueryEnum.SELF_WAREHOUSE.getId());
        List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(merchantInfoDTO.getTenantId(), WarehouseSourceEnum.SAAS_WAREHOUSE, null);
        List<WarehouseStorageVO> WarehouseStorageList = warehouseStorageResps.stream().map(warehouseStorageResp -> {
            WarehouseStorageVO storageVO = new WarehouseStorageVO();
            storageVO.setWarehouseName(warehouseStorageResp.getWarehouseName());
            storageVO.setWarehouseNo(warehouseStorageResp.getWarehouseNo());
            return storageVO;
        }).collect(Collectors.toList());
        warehouseStorageVO.setWarehouseStorageVOList(WarehouseStorageList);
        resultList.add(warehouseStorageVO);
        return resultList;
    }

    @Override
    public OrderVO queryOrderVOByOrderId(Long orderId) {
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        OrderVO orderVO = OrderConvert.INSTANCE.convertResp2VO(orderResp);
        if (Objects.isNull(orderVO)) {
            return null;
        }

        MerchantStoreResultResp merchantStoreResultResp = userCenterMerchantStoreFacade.getMerchantStoreById(orderVO.getStoreId());
        if (Objects.nonNull(merchantStoreResultResp)) {
            orderVO.setStoreName(merchantStoreResultResp.getStoreName());
            orderVO.setStoreType(merchantStoreResultResp.getType());
            orderVO.setStoreNo(merchantStoreResultResp.getStoreNo());
        }
        return orderVO;
    }

    @Override
    public void orderWaitDeliverySupplierNotice(Long orderId) {
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        // 其他直配供应商订单一定是无仓类型
        if (!WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(orderResp.getWarehouseType())) {
            log.info("每单待配通知非其他直配供应商订单，无需通知,订单ID:{}", orderId);
            return;
        }
        Long tenantId = orderResp.getTenantId();
        String cacheKey = RedisKeyEnum.CM00006.join(orderId);
        if (Objects.nonNull(redisUtils.get(cacheKey))) {
            log.info("每单待配通知已发送,订单ID:{}", orderId);
            return;
        }

        // 查询需要推送的租户账户、订单快照子项信息
        cn.hutool.core.lang.Pair<List<TenantAccountResultResp>, List<OrderItemVO>> pair = tenantAccountSupplierMappingService.queryNeedNotifyTenantAccount(orderId, tenantId, TenantAccountConfig.BussinessMsgTypeEnum.WAIT_DELIVERY);
        List<TenantAccountResultResp> tenantAccounts = pair.getKey();
        List<OrderItemVO> orderItemVOS = pair.getValue();
        if (CollectionUtils.isEmpty(tenantAccounts)) {
            return;
        }
        List<Long> tenantAccountIds = tenantAccounts.stream().map(TenantAccountResultResp::getId).collect(Collectors.toList());
        List<TenantAccountSupplierMapping> tenantAccountSupplierMappings = tenantAccountSupplierMappingService.queryByTenantAccountIds(tenantId, tenantAccountIds);
        Map<Long, List<Long>> accountSupplierMap = tenantAccountSupplierMappings.stream().collect(Collectors.groupingBy(TenantAccountSupplierMapping::getAccountId,
                Collectors.mapping(TenantAccountSupplierMapping::getSupplierId, Collectors.toList())));

        // 查询openId信息
        List<String> phone = tenantAccounts.stream().map(TenantAccountResultResp::getPhone).collect(Collectors.toList());
        Map<String, String> phoneMap = wechatFacade.queryAuthMapRespByPhones4FTGYL(phone, tenantId);

        // 组装参数，调用消息中心发送消息
        LocalDateTime payTime = orderResp.getPayTime();
        MessageBodyReq messageBodyReq = new MessageBodyReq();
        messageBodyReq.setContentType(MessageContentTypeEnum.NORMAL.getType());
        messageBodyReq.setTemplateCode(TemplateWechatEnum.TemplateCode.SAAS_PAY_SUCCESS_SUPPLIER_ORDER_CODE.getCode());
        messageBodyReq.setJumpUrlTypeEnum(JumpUrlTypeEnum.PAGE);
        messageBodyReq.setJumpUrl(weChatOaConfig.getMessageOrderJumpUrl());
        String keyword = TemplateWechatEnum.TemplateCode.SAAS_PAY_SUCCESS_SUPPLIER_ORDER_CODE.getKeyword();

        // 依次推送
        for (TenantAccountResultResp tenantAccount : tenantAccounts) {
            String openId = phoneMap.get(tenantAccount.getPhone());
            // 不存在openId，日志输出
            if (StringUtils.isEmpty(openId)) {
                log.info("每单待配通知,订单ID:{},推送过滤，不存在openId的账号信息为：{}", orderId, JSON.toJSONString(tenantAccount));
                continue;
            }
            List<Long> supplierIds = accountSupplierMap.get(tenantAccount.getId());
            if (CollectionUtil.isEmpty(supplierIds)) {
                log.info("每单待配通知,订单ID:{},推送过滤，不存在租户供应商映射信息：{}", orderId, JSON.toJSONString(tenantAccount));
                continue;
            }

            BigDecimal supplierPrice = orderItemVOS.stream().filter(orderItemVO -> supplierIds.contains(orderItemVO.getSupplierTenantId())).map(
                            snapshotDTO -> NumberUtil.mul(Optional.ofNullable(snapshotDTO.getSupplyPrice()).orElse(BigDecimal.ZERO), snapshotDTO.getAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            String data = String.format(keyword, NumberUtil.decimalFormat("0.00", supplierPrice), TimeUtils.convertToString(payTime, TimeUtils.FORMAT), "待发货");
            messageBodyReq.setData(data);
            messageServiceFacade.batchSendFTWechatOaMessage(tenantId, Collections.singletonList(openId), messageBodyReq);
        }

        // 设置每单待配通知缓存
        redisUtils.set(cacheKey, NumberConstant.ONE, TimeUnit.DAYS.toMillis(3));
        log.info("每单待配通知,订单ID:{},推送成功", orderId);
    }

    /**
     * 组装参数、发起rpc调用,批量更新订单状态
     *
     * @param orderNoList
     * @param loginContextInfoDTO
     * @param operator
     */
    private OrderBatchNotifyVO startFulfillment(List<String> orderNoList, LoginContextInfoDTO loginContextInfoDTO, String operator) {
        OrderBatchNotifyVO orderBatchNotifyVO = new OrderBatchNotifyVO();
        orderBatchNotifyVO.setTotalNum(orderNoList.size());
        Integer successNum = NumberConstant.ZERO;

        List<StartFulfillmentErrorOrderResp> totolErrorList = Lists.newArrayList();
        List<List<String>> list = Lists.partition(orderNoList, NumberConstant.TWO_HUNDRED);
        for (List<String> orderNos : list) {
            // orderNos执行移除会存在问题，所以需要转一下
            List tempList = Lists.newArrayList(orderNos);
            try {
                StartFulfillmentReq startFulfillmentReq = new StartFulfillmentReq();
                startFulfillmentReq.setOrderNoList(tempList);
                startFulfillmentReq.setOperator(operator);

                // 是否自动出库状态
                DubboResponse<Boolean> resp = tenantProvider.getAutoOutboundTaskSwitch4Order(loginContextInfoDTO.getTenantId());
                if (resp.isSuccess() && resp.getData() != null) {
                    startFulfillmentReq.setAutoCreateStockTask(resp.getData());
                }
                StartFulfillmentResp startFulfillmentResp = fulfillmentOrderOperateFacade.startFulfillment(startFulfillmentReq);
                if (CollectionUtil.isNotEmpty(startFulfillmentResp.getErrorOrderList())) {
                    totolErrorList.addAll(startFulfillmentResp.getErrorOrderList());
                    log.error("startFulfillment 批量通知接口存在异常单号 startFulfillmentResp={}", JSON.toJSONString(startFulfillmentResp));
                    List<String> failOrderNoList = startFulfillmentResp.getErrorOrderList().stream().map(StartFulfillmentErrorOrderResp::getSourceOrderNo).distinct().collect(Collectors.toList());
                    tempList.removeAll(failOrderNoList);
                }
                successNum = successNum + tempList.size();
            } catch (Exception e) {
                log.error("startFulfillment 请求异常,e", e);
                continue;
            }
            // 批量更新递进为出库中状态
            if (CollectionUtil.isNotEmpty(tempList)) {
                OrderStatusBatchUpdateReq req = new OrderStatusBatchUpdateReq();
                req.setOrderNos(tempList);
                req.setTenantId(loginContextInfoDTO.getTenantId());
                req.setUpdateStatus(OrderStatusEnum.OUT_OF_STORAGE.getCode());
                req.setOriginStatus(OrderStatusEnum.WAITING_DELIVERY.getCode());
                RpcResultUtil.handle(orderCommandProvider.batchUpdateStatus(req));
            }
        }

        // 组装错误信息返回
        List<OrderBatchNotifyVO.NotifyErrorVO> notifyErrorList = Lists.newArrayList();
        Map<String, List<StartFulfillmentErrorOrderResp>> errorMap = totolErrorList.stream().collect(Collectors.groupingBy(StartFulfillmentErrorOrderResp::getErrorMsg));
        for (Map.Entry<String, List<StartFulfillmentErrorOrderResp>> entry : errorMap.entrySet()) {
            OrderBatchNotifyVO.NotifyErrorVO notifyErrorVO = new OrderBatchNotifyVO.NotifyErrorVO();
            notifyErrorVO.setMessage(entry.getKey());
            notifyErrorVO.setOrderNoList(entry.getValue().stream().map(StartFulfillmentErrorOrderResp::getSourceOrderNo).collect(Collectors.toList()));
            notifyErrorList.add(notifyErrorVO);
        }
        orderBatchNotifyVO.setNotifyErrorList(notifyErrorList);

        orderBatchNotifyVO.setSuccessNum(successNum);
        orderBatchNotifyVO.setFailNum(Math.max(orderBatchNotifyVO.getTotalNum() - successNum, NumberConstant.ZERO));
        return orderBatchNotifyVO;
    }

    /**
     * 获取账号信息
     *
     * @param loginContextInfoDTO
     * @return
     */
    private TenantAccountVO getTenantAccountVO(LoginContextInfoDTO loginContextInfoDTO) {
        Long authUserId = loginContextInfoDTO.getAuthUserId();
        TenantAccountVO tenantAccountVO = tenantAccountService.getTenantAccountVO(authUserId);
        AssertCheckParams.notNull(tenantAccountVO, ResultDTOEnum.SERVER_ERROR.getCode(), "您的账号异常");
        return tenantAccountVO;
    }

    /**
     * 查询出符合条件的自营仓待配送订单ID列表
     *
     * @param batchDeliveryNotifyDTO
     * @param loginContextInfoDTO
     * @return
     */
    private List<String> queryNeedDeliveryOrderIds(BatchDeliveryNotifyDTO batchDeliveryNotifyDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Integer type = Optional.ofNullable(batchDeliveryNotifyDTO).map(BatchDeliveryNotifyDTO::getType).orElse(null);
        LocalDateTime cutOffTime = Optional.ofNullable(batchDeliveryNotifyDTO).map(BatchDeliveryNotifyDTO::getCutOffTime).orElse(null);
        List<Integer> warehouseNoList = Optional.ofNullable(batchDeliveryNotifyDTO).map(BatchDeliveryNotifyDTO::getWarehouseNoList).orElse(null);

        Long tenantId = loginContextInfoDTO.getTenantId();
        LocalDateTime payTime = Objects.equals(BatchDeliveryEnum.PAY.getType(), type) ? cutOffTime : null;
        LocalDateTime createTime = Objects.equals(BatchDeliveryEnum.PAY.getType(), type) ? null : cutOffTime;
        List<String> warehouseNoStrList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(warehouseNoList)) {
            warehouseNoStrList = warehouseNoList.stream().map(num -> num.toString()).collect(Collectors.toList());
        }
        OrderNeedDeliveryReq req = new OrderNeedDeliveryReq();
        req.setWarehouseNoList(warehouseNoStrList);
        req.setTenantId(tenantId);
        req.setPayTime(payTime);
        req.setCreateTime(createTime);
        return orderQueryFacade.queryNeedDeliveryOrder(req);
    }

    private void processOrderDeliveryPartition(List<OrderDeliveryExcelDataInput> list,
                                               List<OrderDeliveryExcelDataInput> errorList,
                                               List<Long> supplierIds,
                                               boolean isSupplierRole,
                                               Long tenantId,
                                               LoginContextInfoDTO loginContextInfoDTO,
                                               Map<String, OrderResp> orderMap) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        // 遍历数据逐条发货
        for (OrderDeliveryExcelDataInput input : list) {
            // 效验数据
            String errorMessage = checkOrderDeliveryImportData(input, isSupplierRole);
            if (!com.cosfo.manage.common.util.StringUtils.isBlank(errorMessage)) {
                input.setErrorMessage(errorMessage);
                errorList.add(input);
                continue;
            }

            OrderResp orderDTO = orderMap.getOrDefault(input.getOrderNo(), null);
            if (orderDTO == null || orderDTO.getId() == null || !Objects.equals(orderDTO.getTenantId(), tenantId)) {
                input.setErrorMessage("订单不存在");
                errorList.add(input);
                continue;
            }

            // 效验是否是无仓订单
            if (!WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(orderDTO.getWarehouseType())) {
                input.setErrorMessage("只支持无仓订单发货");
                errorList.add(input);
                continue;
            }

            // 组装发货数据
            OrderDeliveryDTO orderDeliveryDTO = new OrderDeliveryDTO();
            orderDeliveryDTO.setOrderId(orderDTO.getId());
            orderDeliveryDTO.setDeliveryCompany(input.getDeliveryCompany());
            orderDeliveryDTO.setDeliveryNo(input.getDeliveryNo());
            orderDeliveryDTO.setRemark(input.getRemark());
            orderDeliveryDTO.setDeliveryType(Objects.requireNonNull(NoWarehouseDeliveryTypeEnum.getByDesc(input.getDeliveryType())).getType());

            // 获取未发货商品及发货数量
            OrderDetailDeliveryVO orderDetailDeliveryVO = deliveryDetails(orderDTO.getId(), loginContextInfoDTO);
            List<OrderDeliveryVO.OrderDeliveryItemVO> waitDeliveryItemList = orderDetailDeliveryVO.getWaitDeliveryItemList();
            // 筛选供应商id为该供应商的订单项
            OrderItemSnapshotQueryReq snapshotQueryReq = new OrderItemSnapshotQueryReq();
            snapshotQueryReq.setOrderId(orderDTO.getId());
            snapshotQueryReq.setTenantId(orderDTO.getTenantId());
            List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryList(snapshotQueryReq);
            List<OrderItemSnapshotResp> orderItemSnapshotListBySupplierId;
            if (isSupplierRole) {
                orderItemSnapshotListBySupplierId = orderItemSnapshotResps.stream()
                        .filter(e -> supplierIds.contains(e.getSupplierTenantId())).collect(Collectors.toList());
            } else {
                orderItemSnapshotListBySupplierId = orderItemSnapshotResps.stream()
                        .filter(e -> e.getSupplierTenantId().equals(Long.parseLong(input.getSupplierTenantId())))
                        .collect(Collectors.toList());
            }
            waitDeliveryItemList = waitDeliveryItemList
                    .stream().filter(e -> orderItemSnapshotListBySupplierId
                            .stream()
                            .anyMatch(orderItemSnapshotDTO -> orderItemSnapshotDTO.getOrderItemId().equals(e.getOrderItemId())))
                    .collect(Collectors.toList());
            if (waitDeliveryItemList.isEmpty()) {
                input.setErrorMessage("无待发货商品");
                errorList.add(input);
                continue;
            }

            // 目前只支持全部发货
            List<OrderDeliveryDTO.OrderItemDeliveryDTO> deliveryDTOList = waitDeliveryItemList.stream().map(e -> {
                OrderDeliveryDTO.OrderItemDeliveryDTO orderItemDeliveryDTO = orderDeliveryDTO.new OrderItemDeliveryDTO();
                orderItemDeliveryDTO.setOrderItemId(e.getOrderItemId());
                orderItemDeliveryDTO.setQuantity(e.getDeliveryQuantity());
                return orderItemDeliveryDTO;
            }).collect(Collectors.toList());
            orderDeliveryDTO.setDeliveryDTOList(deliveryDTOList);


            // 发货
            try {
                boolean deliveryResult = orderDelivery(orderDeliveryDTO, loginContextInfoDTO);
                if (!deliveryResult) {
                    input.setErrorMessage("发货失败");
                    errorList.add(input);
                }
            } catch (Exception e) {
                log.error("发货失败,tenantID={}, order={}", tenantId, orderDTO, e);
                input.setErrorMessage(e.getMessage());
                errorList.add(input);
            }
        }
        log.info("发货线程持续时间:{}, 订单数: {}", stopwatch.stop(), list.size());
    }

    private String checkOrderDeliveryImportData(OrderDeliveryExcelDataInput input, boolean isSupplierRole) {
        if (StringUtils.isEmpty(input.getOrderNo())) {
            return "订单号不能为空";
        }
        if (!isSupplierRole && input.getSupplierTenantId() == null) {
            return "供应商编号不能为空";
        }
        if (!isSupplierRole && !NumberUtils.isLong(input.getSupplierTenantId())) {
            return "供应商编号格式不正确";
        }
        if (!"全部发货".equals(input.getDeliveryAmount())) {
            return "发货数量只支持全部发货";
        }
        NoWarehouseDeliveryTypeEnum deliveryType = NoWarehouseDeliveryTypeEnum.getByDesc(input.getDeliveryType());
        if (deliveryType == null) {
            return "配送方式不正确";
        }
        if (deliveryType == NoWarehouseDeliveryTypeEnum.NOT_NEED_DELIVERY) {
            if (StringUtils.isEmpty(input.getRemark())) {
                return "备注不能为空";
            }
            if (input.getRemark().length() > NumberConstant.HUNDRED) {
                return "备注超长";
            }
        }
        if (deliveryType == NoWarehouseDeliveryTypeEnum.LOGISTIC_DELIVERY) {
            if (StringUtils.isEmpty(input.getDeliveryCompany())) {
                return "物流公司不能为空";
            }
            if (StringUtils.isEmpty(input.getDeliveryNo())) {
                return "配送单号不能为空";
            }
            if (input.getDeliveryNo().length() > NumberConstant.THIRTY) {
                return "配送单号超长";
            }
        }
        return "";
    }

}
