package com.cosfo.manage.good.model.dto;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/4
 */
@Data
public class ProductAgentApplicationQueryDTO extends BasePageInput {
    /**
     * 商品名称
     */
    private String title;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 类目Id
     */
    private List<Long> categoryIds;

    /**
     * 代理服务商
     */
    private Long agentTenantId;

    /**
     * 审核状态 0审核中 1已通过 2已拒绝 3已取消
     */
    private Integer status;

    /**
     * 类目Id
     */
    private Long categoryId;

    /**
     * saas租户ID
     */
    private Long queryTenantId;

    /**
     * 租户名称
     */
    private String queryTenantName;

    /**
     * 鲜沐SKU编码
     */
    private String agentSkuCode;
}
