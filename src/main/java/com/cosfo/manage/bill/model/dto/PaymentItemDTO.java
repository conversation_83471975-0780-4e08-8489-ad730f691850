package com.cosfo.manage.bill.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/3
 */
@Data
public class PaymentItemDTO {
    /**
     * paymentId
     */
    private Long paymentId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店账号id
     */
    private Long accountId;

    /**
     * 商户/服务商appid
     */
    private String spAppid;

    /**
     * 商户/服务商id
     */
    private String spMchid;

    /**
     * 子商户appid
     */
    private String supAppid;

    /**
     * 子商户id
     */
    private String subMchid;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 支付金额
     */
    private BigDecimal totalPrice;

    /**
     * 状态：0、待支付 1、支付成功 2、支付失败 3、取消支付
     */
    private Integer status;

    /**
     * 预支付id
     */
    private String prepayId;

    /**
     * 交易流水号
     */
    private String transactionId;

    /**
     * 交易类型，枚举值：
     * JSAPI：公众号支付
     * NATIVE：扫码支付
     * APP：APP支付
     * MICROPAY：付款码支付
     * MWEB：H5支付
     * FACEPAY：刷脸支付
     */
    private String tradeType;

    /**
     * 交易状态，枚举值：
     SUCCESS：支付成功
     REFUND：转入退款
     NOTPAY：未支付
     CLOSED：已关闭
     REVOKED：已撤销（付款码支付）
     USERPAYING：用户支付中（付款码支付）
     PAYERROR：支付失败(其他原因，如银行返回失败)
     */
    private String tradeState;

    /**
     * 交易状态描述
     */
    private String tradeStateDesc;

    /**
     * 银行类型，采用字符串类型的银行标识
     */
    private String bankType;

    /**
     * 用户在商户/服务商appid下的唯一标识
     */
    private String spOpenid;

    /**
     * 用户在子商户appid下的唯一标识
     */
    private String subOpenid;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 支付成功时间
     */
    private LocalDateTime successTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 支付通道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;

    /**
     * 手续费费率
     */
    private BigDecimal feeRate;
    /**
     * 支付凭证
     */
    private String paymentReceipt;
    /**
     * 财务凭证
     */
    private String financialReceipt;
    /**
     * 收款日期
     */
    private LocalDate receiptDate;
    /**
     * 线下支付 财务备注
     */
    private String remark;
    /**
     * 线下支付方式0=银行转账;1=微信;2=支付宝
     */
    private Integer offlinePayType;
}
