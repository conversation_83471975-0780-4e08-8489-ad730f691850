package com.cosfo.manage.bill.model.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PrepaymentAmountVO implements Serializable {

    /**
     * 预付余额
     */
    private BigDecimal totalAmount;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 直配到店货品/代仓费
     */
    private BigDecimal commonAmount;

    /**
     * 直配到店货品
     */
    private BigDecimal productAmount;

    /**
     * 代仓费
     */
    private BigDecimal agentAmount;

    public PrepaymentAmountVO() {
        this.totalAmount = BigDecimal.ZERO;
        this.updateTime = LocalDateTime.now();
        this.commonAmount = BigDecimal.ZERO;
        this.productAmount = BigDecimal.ZERO;
        this.agentAmount = BigDecimal.ZERO;
    }
}
