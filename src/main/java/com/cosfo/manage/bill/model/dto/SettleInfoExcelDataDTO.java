package com.cosfo.manage.bill.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2022/12/27 9:18
 */
@Data
public class SettleInfoExcelDataDTO {

    /**
     * 结算时间
     */
    @ExcelProperty("结算时间")
    private String createTime;

    /**
     * 结算金额
     */
    @ExcelProperty("结算金额")
    private String transAmt;
    /**
     * 结算手续费
     */
    @ExcelProperty("结算手续费")
    private String feeAmt;
    /**
     * 系统结算状态
     */
    @ExcelProperty("系统结算状态")
    private String transStat;
    /**
     * 汇付系统返回描述
     */
    @ExcelProperty("汇付系统返回描述")
    private String reason;

}
