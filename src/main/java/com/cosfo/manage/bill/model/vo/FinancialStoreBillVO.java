package com.cosfo.manage.bill.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述: 应收账单
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/4
 */
@Data
public class FinancialStoreBillVO {
    /**
     * 账单Id
      */
    private Long billId;
    /**
     * 账单编号
     */
    private String billNo;
    /**
     * 门店提交凭证时间
     */
    private LocalDateTime storeCredentialsTime;
    /**
     * 账单生成日
     */
    private LocalDateTime createTime;
    /**
     * 账单开始时间
     */
    @JSONField(format="yyyy-MM-dd")
    private LocalDateTime billStartTime;
    /**
     * 账单结束时间
     */
    @JSONField(format="yyyy-MM-dd")
    private LocalDateTime billEndTime;
    /**
     * 门店Id
     */
    private Long storeId;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 订单应收金额
     */
    private BigDecimal receivableOrderPrice;
    /**
     * 售后单应收金额
     */
    private BigDecimal receivableOrderAfterSalePrice;
    /**
     * 订单运费总金额
     */
    private BigDecimal orderDeliveryFeeTotalPrice;
    /**
     * 账单总金额
     */
    private BigDecimal receivablePrice;
    /**
     * 门店类型 0、直营店 1、加盟店 2、托管店
     */
    private Integer storeType;
    /**
     * 门店电话
     */
    private String storePhone;
    /**
     * 账单状态0未收款1待审核2已还款3线下核对
     */
    private Integer status;
    /**
     * 门店上传凭证
     */
    private FinancialBillCredentialsVO storeCredentials;
    /**
     * 品牌方上传凭证
     */
    private FinancialBillCredentialsVO tenantCredentials;
    /**
     * 订单数
     */
    private Integer orderNum;
    /**
     * sku数
     */
    private Integer skuNum;
    /**
     * 下单件数
     */
    private Integer skuAmount;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 门店分组名称
     */
    private String merchantStoreGroupName;
}
