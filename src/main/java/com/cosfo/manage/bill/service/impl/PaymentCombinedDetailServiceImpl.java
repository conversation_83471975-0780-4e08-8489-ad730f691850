package com.cosfo.manage.bill.service.impl;

import com.cosfo.manage.bill.mapper.PaymentCombinedDetailMapper;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.model.po.PaymentCombinedDetail;
import com.cosfo.manage.bill.service.PaymentCombinedDetailService;
import com.cosfo.manage.common.context.PaymentTradeTypeEnum;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2025-05-08
 **/
@Service
public class PaymentCombinedDetailServiceImpl implements PaymentCombinedDetailService {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentCombinedDetailMapper paymentCombinedDetailMapper;

    @Override
    public List<PaymentCombinedDetail> querySuccessCombinedByOrderId(Long tenantId, Long orderId) {
        Payment payment = paymentMapper.querySuccessByOrderId(tenantId, orderId);
        if (payment == null) {
            return Collections.emptyList();
        }
        if (!Objects.equals(payment.getTradeType(), PaymentTradeTypeEnum.COMBINED_PAY.getDesc())) {
            return Collections.emptyList();
        }
        return paymentCombinedDetailMapper.selectByCombinedPaymentNo(payment.getPaymentNo());
    }

    @Override
    public Map<String, BigDecimal> querySuccessCombinedByOrderId(Long orderId) {
        paymentMapper.querySuccessByOrderId()
    }
}
