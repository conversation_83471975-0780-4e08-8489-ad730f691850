package com.cosfo.manage.bill.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.bill.mapper.PaymentCombinedDetailMapper;
import com.cosfo.manage.bill.mapper.PaymentCombinedOrderDetailMapper;
import com.cosfo.manage.bill.mapper.PaymentItemMapper;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.dto.OrderItemPriceDetailDTO;
import com.cosfo.manage.bill.model.dto.PaymentItemDTO;
import com.cosfo.manage.bill.model.dto.PaymentQueryDTO;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.model.po.PaymentCombinedDetail;
import com.cosfo.manage.bill.model.po.PaymentItem;
import com.cosfo.manage.bill.model.vo.PaymentCombinedOrderDetailVO;
import com.cosfo.manage.bill.service.PaymentService;
import com.cosfo.manage.common.context.PaymentTradeTypeEnum;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.cosfo.ordercenter.client.common.TradeTypeEnum;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.cosfo.ordercenter.client.common.PayTypeEnum;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2024-08-29
 **/
@Service
@Slf4j
public class PaymentServiceImpl implements PaymentService {

    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentCombinedDetailMapper paymentCombinedDetail;
    @Resource
    private PaymentCombinedOrderDetailMapper paymentCombinedOrderDetailMapper;
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;

    @Override
    public List<Long> getOrderIdsByPaymentNo(String paymentNo) {
        if(StringUtils.isBlank(paymentNo)){
            return Collections.emptyList();
        }
        Payment payment = paymentMapper.selectByPaymentNo(paymentNo);
        if (payment == null) {
            return Collections.emptyList();
        }
        Long id = payment.getId();
        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentId(id);
        if (CollectionUtils.isEmpty(paymentItems)) {
            return Collections.emptyList();
        }
        return paymentItems.stream().map(PaymentItem::getOrderId).collect(Collectors.toList());
    }

    @Override
    public PaymentItemDTO queryByOrderId(PaymentQueryDTO queryDTO) {
        List<PaymentItemDTO> paymentItemDTOS = paymentMapper.querySuccessPaymentByOrderIds (Collections.singletonList (queryDTO.getOrderId ()), queryDTO.getTenantId ());
        if(CollectionUtils.isNotEmpty (paymentItemDTOS)){
            return paymentItemDTOS.get (0);
        } else {
            return new PaymentItemDTO ();
        }
    }

    @Override
    public BigDecimal queryWithDiscountByOrder(OrderVO order) {
        BigDecimal deliveryFee = order.getDeliveryFee () == null? BigDecimal.ZERO : order.getDeliveryFee ();
        List<PaymentCombinedOrderDetailVO> listByOrderIds = paymentCombinedOrderDetailMapper.getListByOrderIds (Collections.singletonList (order.getOrderId ()));

        if(CollectionUtils.isEmpty (listByOrderIds)){
            if (PayTypeEnum.withoutDiscountsList ().contains (order.getPayType ())) {
                return BigDecimal.ZERO;
            } else {
                return order.getTotalPrice ().subtract (deliveryFee);
            }
        }else{
            List<PaymentCombinedOrderDetailVO> collect = listByOrderIds.stream ().filter (o -> o.getTradeType () != null && !PaymentTradeTypeEnum.withoutDiscountsList ().contains (o.getTradeType ()) && o.getTotalPrice () != null).collect (Collectors.toList ());
            if(CollectionUtils.isEmpty (collect)){
                return BigDecimal.ZERO;
            }else{
                return collect.stream ().map (PaymentCombinedOrderDetailVO::getTotalPrice).reduce (BigDecimal::add).get ();
            }
        }
    }
    @Override
    public List<OrderItemPriceDetailDTO> queryPayDetail(PaymentQueryDTO queryDTO, OrderVO order) {
        List<OrderItemVO> orderItemVOS = order.getOrderItemVOS ();
        List<OrderItemVO> allOrderItemVOS = new ArrayList<> ();
        allOrderItemVOS.addAll (orderItemVOS);

        PaymentItem paymentItem = paymentItemMapper.selectItemByOrderId (queryDTO.getTenantId (), queryDTO.getOrderId ());
        if (paymentItem == null) {
            return Collections.emptyList ();
        }
        Long paymentId = paymentItem.getPaymentId ();


        //如果不是组合支付，则只有一种支付方式
        if (!PayTypeEnum.COMBINED_PAY.getCode ().equals (order.getPayType ())) {
            return orderItemVOS.stream ().map (e -> getOrderItemPriceDetailDTO (order, paymentItem, paymentId, e)).collect (Collectors.toList ());
        } else {
            //如果是组合支付，则根据组合支付明细进行计算
            Map<Long, Boolean> canUseCoupon = marketItemService.canUseCoupon (orderItemVOS.stream ().map (OrderItemVO::getItemId).collect (Collectors.toList ()), queryDTO.getTenantId ());
            boolean hasTrueValue = canUseCoupon.values().stream().anyMatch(value -> value); // 判断是否有值为 true 的元素
            if (!hasTrueValue) {
                //没有可以使用优惠券的品
                return orderItemVOS.stream ().map (e -> {
                    OrderItemPriceDetailDTO orderItemPriceDetailDTO = getOrderItemPriceDetailDTO (order, paymentItem, paymentId, e);
                    orderItemPriceDetailDTO.setPriceMap (Collections.singletonMap (0, e.getTotalPrice ()));
                    return orderItemPriceDetailDTO;
                }).collect (Collectors.toList ());
            }else {
                //有可以使用优惠券的品
                //查询组合支付明细
                Payment payment = paymentMapper.selectById (paymentId);
                List<PaymentCombinedDetail> paymentCombinedDetails = paymentCombinedDetail.listByPaymentNo (payment.getPaymentNo ());

                //计算组合支付明细中，优惠券的支付金额
                BigDecimal withDiscount = paymentCombinedDetails != null ? paymentCombinedDetails.stream ()
                        .filter (e -> e != null && e.getTradeType () != null &&
                                !PaymentTradeTypeEnum.withoutDiscountsList ().contains (e.getTradeType ()))
                        .map (e -> e.getTotalPrice () != null ? e.getTotalPrice () : BigDecimal.ZERO)
                        .reduce (BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO;

                //无优惠金额
                if (withDiscount.compareTo (BigDecimal.ZERO) == 0) {
                    return orderItemVOS.stream ().map (e -> {
                        OrderItemPriceDetailDTO orderItemPriceDetailDTO = getOrderItemPriceDetailDTO (order, paymentItem, paymentId, e);
                        orderItemPriceDetailDTO.setPriceMap (Collections.singletonMap (0, e.getTotalPrice ()));
                        return orderItemPriceDetailDTO;
                    }).collect (Collectors.toList ());
                }else{
                    //有优惠金额，查询其他参与支付的订单项
                    List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentId (paymentId);

                    List<Long> orderIds = paymentItems.stream ().map (PaymentItem::getOrderId).filter (orderId -> !orderId.equals (order.getOrderId ())).collect (Collectors.toList ());

                    if(CollectionUtil.isNotEmpty (orderIds)){
                        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
                        orderItemQueryReq.setOrderIds(orderIds);
                        List<OrderItemVO> orderItems = orderItemQueryFacade.queryOrderItemListVO(orderItemQueryReq);
                        Map<Long, Boolean> otherItemCanUseCoupon  = marketItemService.canUseCoupon (orderItems.stream ().map (OrderItemVO::getItemId).collect (Collectors.toList ()), queryDTO.getTenantId ());
                        canUseCoupon.putAll (otherItemCanUseCoupon);
                        allOrderItemVOS.addAll (orderItems);
                    }
                    // 按照 id 从小到大排序
                    allOrderItemVOS = allOrderItemVOS.stream ().sorted (Comparator.comparing (OrderItemVO::getId)).collect (Collectors.toList ());
                    //可以使用优惠的商品的最后一个订单项id，用来收集尾差
                    Long maxId = allOrderItemVOS.stream ().filter (e -> canUseCoupon.get (e.getItemId ())).max (Comparator.comparing (OrderItemVO::getId)).get ().getId ();
                    //理论上 所有可以使用优惠券的金额和
                    BigDecimal canUseCouponTotalPrice = allOrderItemVOS.stream ().filter (e -> canUseCoupon.get (e.getItemId ())).map (OrderItemVO::getTotalPrice).reduce (BigDecimal.ZERO, BigDecimal::add);
                    if(canUseCouponTotalPrice.compareTo (BigDecimal.ZERO) == 0){
                        log.error ("组合支付金额计算错误,都不能使用优惠，但是支付单明细有优惠金额，order={},paymen={}", JSON.toJSONString (order), JSON.toJSONString (payment));
                        return orderItemVOS.stream ().map (e -> getOrderItemPriceDetailDTO (order, paymentItem, paymentId, e)).collect (Collectors.toList ());
                    }

                    List<OrderItemPriceDetailDTO> list = new ArrayList<> ();
                    BigDecimal useCouponTotalPrice = withDiscount;
                    for (OrderItemVO e : allOrderItemVOS) {
                        //不能使用优惠券
                        Boolean canUseCouponFlag = canUseCoupon.get (e.getItemId ());
                        if(Boolean.FALSE.equals(canUseCouponFlag)){
                            if(e.getOrderId ().equals (order.getOrderId ())){
                                OrderItemPriceDetailDTO orderItemPriceDetailDTO = getOrderItemPriceDetailDTO (order, paymentItem, paymentId, e);
                                orderItemPriceDetailDTO.setPriceMap (Collections.singletonMap (0, e.getTotalPrice ()));
                                list.add (orderItemPriceDetailDTO);
                            }
                        }else{
                            BigDecimal itemTotalPrice = e.getTotalPrice ();

                            Map<Integer, BigDecimal> map = new HashMap<> ();
                            if(!e.getId ().equals (maxId)){
                                //优惠金额 = 金额 / 理论上 所有可以使用优惠券的金额和 * 优惠券的支付金额
                                BigDecimal useCouponItemPrice = withDiscount.compareTo (canUseCouponTotalPrice)==0 ? itemTotalPrice:itemTotalPrice.divide (canUseCouponTotalPrice, 4, RoundingMode.HALF_UP).multiply (withDiscount);
                                useCouponItemPrice = useCouponItemPrice.compareTo (itemTotalPrice)>0 ? itemTotalPrice : useCouponItemPrice;
                                map.put (1, useCouponItemPrice);
                                BigDecimal subtract = itemTotalPrice.subtract (useCouponItemPrice);
                                map.put (0, subtract.compareTo (BigDecimal.ZERO)>0 ? subtract : BigDecimal.ZERO);
                                useCouponTotalPrice = useCouponTotalPrice.subtract (useCouponItemPrice);
                            }else{
                                useCouponTotalPrice = useCouponTotalPrice.compareTo (BigDecimal.ZERO)>0 ? (useCouponTotalPrice.compareTo (itemTotalPrice)>0 ? itemTotalPrice : useCouponTotalPrice) : BigDecimal.ZERO;
                                map.put (1, useCouponTotalPrice);
                                BigDecimal subtract = itemTotalPrice.subtract (useCouponTotalPrice);
                                map.put (0, subtract.compareTo (BigDecimal.ZERO)>0 ? subtract : BigDecimal.ZERO);
                            }
                            if(e.getOrderId ().equals (order.getOrderId ())){
                                OrderItemPriceDetailDTO orderItemPriceDetailDTO = getOrderItemPriceDetailDTO (order, paymentItem, paymentId, e);
                                orderItemPriceDetailDTO.setPriceMap (map);
                                list.add (orderItemPriceDetailDTO);
                            }
                        }
                    }
                    return list;
                }
            }
        }
    }


    private OrderItemPriceDetailDTO getOrderItemPriceDetailDTO(OrderVO order, PaymentItem paymentItem, Long paymentId, OrderItemVO e) {
        OrderItemPriceDetailDTO orderItemPriceDetailDTO = new OrderItemPriceDetailDTO ();
        orderItemPriceDetailDTO.setOrderId(e.getOrderId ());
        orderItemPriceDetailDTO.setTenantId(order.getTenantId ());
        orderItemPriceDetailDTO.setOrderItemId(e.getId ());
        orderItemPriceDetailDTO.setPaymentId(paymentId);
        orderItemPriceDetailDTO.setPaymentItemId(paymentItem.getId ());
        if (PayTypeEnum.withoutDiscountsList ().contains (order.getPayType ())) {
            orderItemPriceDetailDTO.setPriceMap (Collections.singletonMap (0, e.getTotalPrice ()));
        } else {
            orderItemPriceDetailDTO.setPriceMap (Collections.singletonMap (1, e.getTotalPrice ()));
        }
        return orderItemPriceDetailDTO;
    }
}
