package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.po.PaymentCombinedDetail;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-05-08
 **/
public interface PaymentCombinedDetailService {

    /**
     * 根据订单查询组合支付明细
     *
     * @param orderId
     * @return
     */
    List<PaymentCombinedDetail> querySuccessCombinedByOrderId(Long tenantId, Long orderId);

    /**
     *
     * @param orderId
     * @return
     */
    Map<String, BigDecimal> querySuccessCombinedByOrderId(Long orderId);
}
