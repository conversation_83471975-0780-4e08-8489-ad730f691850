package com.cosfo.manage.bill.service;

import com.cosfo.manage.bill.model.dto.OrderItemPriceDetailDTO;
import com.cosfo.manage.bill.model.dto.PaymentItemDTO;
import com.cosfo.manage.bill.model.dto.PaymentQueryDTO;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.order.model.vo.OrderVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-08-29
 **/
public interface PaymentService {

    /**
     * 根据支付单号获取订单ids
     * @param paymentNo
     * @return
     */
    List<Long> getOrderIdsByPaymentNo(String paymentNo);

    PaymentItemDTO queryByOrderId(PaymentQueryDTO queryDTO);

    List<OrderItemPriceDetailDTO> queryPayDetail(PaymentQueryDTO queryDTO, OrderVO order);

    /**
     * 查询订单的优惠金额
     * @param order
     * @return
     */
    BigDecimal queryWithDiscountByOrder(OrderVO order);
}
