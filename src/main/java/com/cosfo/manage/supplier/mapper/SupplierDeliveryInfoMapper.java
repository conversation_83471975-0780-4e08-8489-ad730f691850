package com.cosfo.manage.supplier.mapper;

import com.cosfo.manage.supplier.model.dto.SupplierDeliveryInfoDTO;
import com.cosfo.manage.supplier.model.po.SupplierDeliveryInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SupplierDeliveryInfoMapper {

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(SupplierDeliveryInfo record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(SupplierDeliveryInfo record);

    /**
     * 查询
     * @param id
     * @return
     */
    SupplierDeliveryInfo selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(SupplierDeliveryInfo record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(SupplierDeliveryInfo record);

    /**
     * 批量查询供应商运费
     *
     * @param orderNos
     * @return
     */
    List<SupplierDeliveryInfoDTO> batchQuery(@Param("orderNos") List<String> orderNos);
}
