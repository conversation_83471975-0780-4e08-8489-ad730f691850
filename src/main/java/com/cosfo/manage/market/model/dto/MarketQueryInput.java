package com.cosfo.manage.market.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/11
 */
@Data
public class MarketQueryInput {
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 分页数量
     */
    private Integer pageSize;
    /**
     * 主标题
     */
    private String title;
    /**
     * spu
     */
    private Long id;
    /**
     * 前台分类
     */
    private Long classificationId;
    /**
     * 分类ids
     */
    private List<Long> classificationIds;
    /**
     * 后台类目
     */
    private Long categoryId;
    /**
     * 类目ids
     */
    private List<Long> categoryIds;
    /**
     * 后台类目Id
     */
    private Long itemId;
    /**
     * 货
     */
    @Deprecated
    private Integer warehouseType;
    /**
     * 配送方式
     */
    @Deprecated
    private Integer deliveryType;
    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * marketIds
     */
    private List<Long> marketIds;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * @see com.cosfo.manage.common.context.OnSaleTypeEnum
     */
    private Integer onSale;

    /**
     * @see com.cosfo.manage.common.context.MarketDeleteFlagEnum
     */
    private Integer deleteFlag;

    /**
     * 货品类型 0-无货商品 1-报价商品 2-自营货品
     * @see  com.cosfo.manage.common.context.GoodsTypeEnum
     */
    private Integer goodsType;

    private List<Integer> goodsTypeList;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 防倒挂类型 0-以供应价售卖 1-已自定义价售卖 2-自动下架
     */
    private Integer unfairPriceStrategyType;

    /**
     * 防倒挂方式 1=是,0=否
     */
    private Integer unfairPriceStrategyDefaultFlag;
    /**
     * 管控门店库存0=不管;1=管控
     */
    private Boolean storeInventoryControlFlag;

    /**
     * 商品预售开关 0-不可预售 1-可预售
     */
    private Integer presaleSwitch;
}
