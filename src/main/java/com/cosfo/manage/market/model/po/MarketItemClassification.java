package com.cosfo.manage.market.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * 商品分类关联表(MarketItemClassification)实体类
 *
 * <AUTHOR>
 * @date 2022-05-10 17:29:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketItemClassification implements Serializable {
    /**
     * id主键
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 分类id
     */
    private Long classificationId;
    /**
     * marketId
     */
    private Long marketId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
