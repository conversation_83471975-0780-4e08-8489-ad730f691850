package com.cosfo.manage.market.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.PriceStrategyTypeEnum;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.req.MarketItemCommonQueryReq;
import com.cofso.item.client.req.MarketItemInfoQueryFlagReq;
import com.cofso.item.client.resp.MarketItemPriceStrategyUpdateResultResp;
import com.cofso.item.client.resp.PriceDetailResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceRangeResp;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.PriceStrategyConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.PriceUtil;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.MarketItemFacade;
import com.cosfo.manage.facade.PriceFacade;
import com.cosfo.manage.facade.category.CategoryServiceFacade;
import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import com.cosfo.manage.good.convert.ProductInfoConvert;
import com.cosfo.manage.good.dao.ProductAgentApplicationRecordDao;
import com.cosfo.manage.market.converter.MarketConvert;
import com.cosfo.manage.market.converter.MarketFacadeConvert;
import com.cosfo.manage.market.converter.MarketItemConverter;
import com.cosfo.manage.market.mapper.MarketItemMapper;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.po.MarketItem;
import com.cosfo.manage.market.model.vo.*;
import com.cosfo.manage.market.repository.MarketItemRepository;
import com.cosfo.manage.market.service.MarketAreaItemService;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.manage.marketing.service.ItemSaleLimitConfigService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAddressDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupQueryDTO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreGroupVO;
import com.cosfo.manage.merchant.service.MerchantStoreGroupMappingService;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.mapper.ProductPricingSupplyMapper;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.product.model.dto.ProductPricingSupplyCityRangeDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.po.ProductPricingSupply;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;
import com.cosfo.manage.product.service.ProductAgentSkuService;
import com.cosfo.manage.product.service.ProductPricingSupplyService;
import com.cosfo.manage.product.service.ProductSkuService;
import com.cosfo.manage.report.model.vo.ProductAgentWarehouseDataVO;
import com.cosfo.manage.supplier.service.SupplierService;
import com.cosfo.manage.tenant.model.vo.TenantFundAccountConfigVO;
import com.cosfo.manage.tenant.service.TenantFundAccountConfigService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/18 11:13
 */
@Service
@Slf4j
public class MarketItemServiceImpl implements MarketItemService {

    @Resource
    private MarketItemMapper marketItemMapper;
    @Autowired
    @Lazy
    private ProductSkuService productSkuService;
    @Resource
    private MarketItemRepository marketItemRepository;
    @Lazy
    @Resource
    private ProductPricingSupplyService productPricingSupplyService;
    @Resource
    private ProductAgentSkuService productAgentSkuService;
    @Resource
    private MarketFacade marketFacade;
    @Resource
    private MarketItemFacade marketItemFacade;
    @Resource
    private PriceFacade priceFacade;
    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Resource
    @Lazy
    private MerchantStoreService merchantStoreService;
    @Resource
    private ProductPricingSupplyMapper productPricingSupplyMapper;
    @DubboReference
    private MarketProvider marketProvider;
    @DubboReference
    private MarketItemProvider marketItemProvider;

    @Resource
    private MarketAreaItemService marketAreaItemService;
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;

    @Resource
    private CommonService commonService;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;
    @Resource
    private MerchantStoreGroupMappingService merchantStoreGroupMappingService;
    @Resource
    private ItemSaleLimitConfigService itemSaleLimitConfigService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private ProductAgentApplicationRecordDao productAgentApplicationRecordDao;

    @Resource
    private TenantFundAccountConfigService tenantFundAccountConfigService;

    @Override
    public Long save(MarketItemInput marketItemInput, Long tenantId) {
        marketItemInput.setTenantId(tenantId);
        checkLimitQuantityAndBuyMultiple(marketItemInput);
        Long marketItemId = marketFacade.addMarketItem(marketItemInput);
        // 新增限售配置
        if (marketItemInput.getSaleLimitRule() != null) {
            itemSaleLimitConfigService.saveItemSaleLimitConfig(ItemSaleLimitConfigDTO.builder()
                .tenantId(tenantId)
                .marketItemId(marketItemId)
                .saleLimitQuantity(marketItemInput.getSaleLimitQuantity())
                .saleLimitRule(marketItemInput.getSaleLimitRule())
                .build());
        }
        return marketItemId;
    }

    private void checkLimitQuantityAndBuyMultiple(MarketItemInput marketItemInput) {
        if (Objects.equals(Boolean.TRUE, marketItemInput.getBuyMultipleSwitch()) && Objects.equals(ItemSaleLimitRuleEnum.EVERY_TIME.getCode(), marketItemInput.getSaleLimitRule())) {
            Integer saleLimitQuantity = marketItemInput.getSaleLimitQuantity();
            if (saleLimitQuantity < marketItemInput.getBuyMultiple()) {
                throw new BizException("订货倍数不可大于每次下单上限");
            }
        }
    }

    @Override
    public void update(MarketItemInput marketItemInput, Long tenantId) {
        marketItemInput.setTenantId(tenantId);
        if (Objects.isNull(marketItemInput.getId())) {
            throw new BizException("商品ID不可为空");
        }
        checkLimitQuantityAndBuyMultiple(marketItemInput);
        // 如果不是无货商品， 则清空无货商品供应价
        if(!GoodsTypeEnum.NO_GOOD_TYPE.getCode ().equals (marketItemInput.getGoodsType ())) {
            marketItemInput.setNoGoodsSupplyPrice (null);
        }
        marketFacade.updateMarketItem(marketItemInput);
        // 新增限售配置
        if (marketItemInput.getSaleLimitRule() != null) {
            itemSaleLimitConfigService.saveItemSaleLimitConfig(ItemSaleLimitConfigDTO.builder()
                .tenantId(tenantId)
                .marketItemId(marketItemInput.getId())
                .saleLimitQuantity(marketItemInput.getSaleLimitQuantity())
                .saleLimitRule(marketItemInput.getSaleLimitRule())
                .build());
        }
    }


    @Override
    public MarketItemDTO detail(Long tenantId, Long itemId) {
        MarketItemDTO marketItemDto = marketFacade.getMarketItemDetail(tenantId, itemId);
        if (Objects.isNull(marketItemDto)) {
            return new MarketItemDTO();
        }
        // 补充门店信息
        fillStoreList(marketItemDto);
        // 如果不是自营品，查询货品信息
        if (!Objects.isNull(marketItemDto.getSkuId()) && GoodsTypeEnum.THIRD_DELIVERY_CODES.contains(marketItemDto.getGoodsType())) {
            // 查询货品信息
            ProductSkuDTO productSkuDTO = productSkuService.querySkuInfo(marketItemDto.getSkuId());
            if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemDto.getGoodsType())) {
                productSkuDTO.setAgentStatus(productAgentApplicationRecordDao.getAgentApplicationRecordStatus(marketItemDto.getSkuId(), tenantId));
            }
            marketItemDto.setProductSkuDTO(productSkuDTO);
        }
        assemblyItem(Collections.singletonList(marketItemDto), marketItemDto.getTenantId());
        // 补充限售
        ItemSaleLimitConfigDTO itemSaleLimitConfigDTO = itemSaleLimitConfigService.queryItemSaleLimitConfig(marketItemDto.getTenantId(), itemId);
        marketItemDto.setSaleLimitRule(itemSaleLimitConfigDTO.getSaleLimitRule());
        marketItemDto.setSaleLimitQuantity(itemSaleLimitConfigDTO.getSaleLimitQuantity());

        return marketItemDto;
    }

    /**
     * 补充门店信息
     *
     * @param marketItemDTO
     */
    private void fillStoreList(MarketItemDTO marketItemDTO) {
        if (CollectionUtils.isEmpty(marketItemDTO.getMarketAreaItemMappingVos())) {
            return;
        }

        List<Long> storeIds = new ArrayList<>();
        List<Long> storeGroupIds = new ArrayList<>();
        for (MarketAreaItemMappingVO areaItem : marketItemDTO.getMarketAreaItemMappingVos()) {
            if (CollectionUtil.isNotEmpty(areaItem.getStoreIds())) {
                storeIds.addAll(areaItem.getStoreIds());
            }
            if (CollectionUtil.isNotEmpty(areaItem.getStoreGroupIds())) {
                storeGroupIds.addAll(areaItem.getStoreGroupIds());
            }
        }

        Integer storeCount = merchantStoreService.selectCountByTenantId(marketItemDTO.getTenantId());


        Map<Long, MerchantStoreDTO> storeMap = new HashMap<>();
        Map<Long, MerchantStoreGroupVO> storeGroupMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(storeIds)) {
            List<MerchantStoreDTO> merchantStoreDtos = merchantStoreService.batchQueryDetailByStoreIds(storeIds, marketItemDTO.getTenantId());
            storeMap = merchantStoreDtos.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, Function.identity()));

        }

        if (CollectionUtil.isNotEmpty(storeGroupIds)) {
            List<MerchantStoreGroupVO> merchantStoreGroups = merchantStoreGroupService.listGroupByParam(MerchantStoreGroupQueryDTO.builder()
                .tenantId(marketItemDTO.getTenantId())
                .groupIds(storeGroupIds)
                .build());
            storeGroupMap = merchantStoreGroups.stream().collect(Collectors.toMap(MerchantStoreGroupVO::getId, Function.identity()));

        }
        List<String> cityNames = new ArrayList<>(NumberConstants.TEN);
        if (!Objects.isNull(marketItemDTO.getSkuId()) && GoodsTypeEnum.getThreePartiesCode().equals(marketItemDTO.getGoodsType())) {
            List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDTOS = productPricingSupplyService.querySupplyCity(marketItemDTO.getSkuId(), marketItemDTO.getTenantId());
            if (!org.springframework.util.CollectionUtils.isEmpty(productPricingSupplyCityMappingDTOS)) {
                cityNames = productPricingSupplyCityMappingDTOS.stream().map(ProductPricingSupplyCityMappingDTO::getCityName).collect(Collectors.toList());
            }
        }

        for (MarketAreaItemMappingVO areaMappingVo : marketItemDTO.getMarketAreaItemMappingVos()) {
            if (CollectionUtils.isNotEmpty(areaMappingVo.getStoreIds())) {
                List<MerchantStoreDTO> storeList = areaMappingVo.getStoreIds().stream()
                    .map(storeMap::get).filter(Objects::nonNull)
                    .collect(Collectors.toList());
                // 去除可能被删除的门店
                areaMappingVo.setStoreIds(storeList.stream().map(MerchantStoreDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()));
                areaMappingVo.setMerchantStoreDtoList(storeList);
                areaMappingVo.setTotalStoreCount(storeList.size());
            }
            if (CollectionUtils.isNotEmpty(areaMappingVo.getStoreGroupIds())) {
                Map<Long, MerchantStoreGroupVO> finalStoreGroupMap = storeGroupMap;
                // 去除可能被删除的门店分组
                areaMappingVo.setStoreGroupIds(areaMappingVo.getStoreGroupIds().stream()
                    .filter(i -> Objects.nonNull(finalStoreGroupMap.get(i)))
                    .collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(areaMappingVo.getStoreGroupIds())) {
                    areaMappingVo.setStoreGroupList(areaMappingVo.getStoreGroupIds().stream()
                        .map(storeGroupMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
                    areaMappingVo.setTotalStoreCount(areaMappingVo.getStoreGroupIds().stream()
                        .map(storeGroupMap::get)
                        .filter(Objects::nonNull)
                        .mapToInt(MerchantStoreGroupVO::getStoreNum)
                        .sum());
                }
            }
            if (PriceStrategyConstants.PRICE_TYPE_DEFAULT.equals(areaMappingVo.getPriceType())) {
                areaMappingVo.setTotalStoreCount(storeCount);
            }
            List<MerchantStoreDTO> merchantStoreDtoList = areaMappingVo.getMerchantStoreDtoList();
            if (CollectionUtils.isNotEmpty(merchantStoreDtoList)) {
                List<String> finalCityNames = cityNames;
                merchantStoreDtoList.forEach(merchantStoreDTO -> merchantStoreDTO.setSupplyStatus(finalCityNames.contains(merchantStoreDTO.getCity()) ? NumberConstants.ZERO : NumberConstants.ONE));
            }
        }
    }

    /**
     * 补充item的其它信息
     *
     * @param marketItemDtos
     * @param tenantId
     */
    private void assemblyItem(List<MarketItemDTO> marketItemDtos, Long tenantId) {
        // 关联报价的销售商品
        List<Long> quotationSkuIds = marketItemDtos.stream()
            .filter(marketItemDTO -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItemDTO.getGoodsType()))
            .map(MarketItemDTO::getSkuId)
            .collect(Collectors.toList());
        // 关联自营的销售商品
        List<Long> selfGoodSkuIds = marketItemDtos.stream()
            .filter(marketItemDTO -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemDTO.getGoodsType()))
            .map(MarketItemDTO::getSkuId)
            .collect(Collectors.toList());
        Map<Long, CostPriceRangeVO> skuPriceMap = new HashMap<>();
        // 查询成本价价格信息
        if (CollectionUtil.isNotEmpty(selfGoodSkuIds)) {
            List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingMapper.batchQueryBySkuIds(selfGoodSkuIds);
            if (CollectionUtil.isNotEmpty(productAgentSkuMappings)) {
                List<CostPriceRangeVO> costPriceRangeVOS = priceFacade.queryCostPriceRange(productAgentSkuMappings.stream().map(ProductAgentSkuMapping::getAgentSkuId).collect(Collectors.toSet()), tenantId);
                Map<Long, Long> agentSkuMap = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductAgentSkuMapping::getAgentSkuId, ProductAgentSkuMapping::getSkuId));
                Map<Long, CostPriceRangeVO> skuCityCostPriceMap = costPriceRangeVOS.stream().collect(Collectors.toMap(CostPriceRangeVO::getSkuId, Function.identity()));
                skuCityCostPriceMap.forEach((agentSkuId, costPriceRangeVO) -> {
                    Long skuId = agentSkuMap.get(agentSkuId);
                    skuPriceMap.put(skuId, costPriceRangeVO);
                });
            }
        }
        // 查询报价商品价格信息
        Map<Long, List<ProductPricingSupplyCityMappingDTO>> quotationMap = productPricingSupplyService.querySupplyPriceSkuIdMap (quotationSkuIds, tenantId);
        // 查询省心定价格信息
        Map<Long, List<Long>> preferentialCostPriceRangeQueryMap = new HashMap<> ();
        quotationMap.forEach ((skuId,citys)-> preferentialCostPriceRangeQueryMap.put (skuId,citys.stream().map (ProductPricingSupplyCityMappingDTO::getCityId).collect(Collectors.toList())));
        Map<Long, ProductSkuPreferentialCostPriceRangeResp> productSkuPreferentialCostPriceRangeMap = productPricingSupplyService.queryPreferentialCostPriceRange (tenantId,preferentialCostPriceRangeQueryMap);

        // 查询代仓库存
        Map<Long, List<ProductAgentWarehouseDataVO>> stockInfoMap = queryStockInfo(marketItemDtos, tenantId);
        marketItemDtos.forEach(marketItemDto -> {
            // 自营和代仓品
            if (GoodsTypeEnum.NO_WAREHOUSE_CODES.contains(marketItemDto.getGoodsType())) {
                if (Objects.isNull(marketItemDto.getAmount())) {
                    marketItemDto.setAmount(NumberConstant.ZERO);
                }
                if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemDto.getGoodsType())) {
                    if (stockInfoMap.containsKey(marketItemDto.getSkuId())) {
                        List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVos = stockInfoMap.get(marketItemDto.getSkuId());
                        int enabledTotalQuantity = productAgentWarehouseDataVos.stream().mapToInt(ProductAgentWarehouseDataVO::getEnabledQuantity).sum();
                        ProductSkuDTO productSkuDTO = marketItemDto.getProductSkuDTO();
                        productSkuDTO.setEnabledTotalQuantity(enabledTotalQuantity);
                        productSkuDTO.setWarehouseDataVos(ProductInfoConvert.INSTANCE.convert2VOs(productAgentWarehouseDataVos));
                        marketItemDto.setAmount(enabledTotalQuantity);
                    } else {
                        marketItemDto.setAmount(NumberConstant.ZERO);
                    }
                    if (skuPriceMap.containsKey(marketItemDto.getSkuId())) {
                        CostPriceRangeVO costPriceRangeVO = skuPriceMap.get(marketItemDto.getSkuId());
                        BigDecimal supplyMaxPrice = costPriceRangeVO.getMaxPrice();
                        BigDecimal supplyMinPrice = costPriceRangeVO.getMinPrice();
                        ProductSkuDTO productSkuDTO = marketItemDto.getProductSkuDTO();
                        productSkuDTO.setMinPrice(supplyMinPrice);
                        productSkuDTO.setMaxPrice(supplyMaxPrice);
                        productSkuDTO.setPriceStr(PriceUtil.buildPriceRange(supplyMaxPrice, supplyMinPrice));
                    }
                }
                // 供应商品
            } else if (GoodsTypeEnum.getThreePartiesCode().equals(marketItemDto.getGoodsType())) {
                // 查询商品是否有报价单
                List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDtos = quotationMap.get(marketItemDto.getSkuId());
                if (Objects.isNull(productPricingSupplyCityMappingDtos) && Objects.isNull(marketItemDto.getProductSkuDTO())) {
                    marketItemDto.setSkuId(null);
                    marketItemDto.setProductSkuDTO(null);
                    return;
                }
                // 查询报价单
                ProductPricingSupply productPricingSupply = productPricingSupplyMapper.selectBySupplierSkuId(marketItemDto.getTenantId(), marketItemDto.getSkuId());
                ProductSkuDTO productSkuDto = marketItemDto.getProductSkuDTO();
                // 查询供应城市数量和价格生效区间
                if (Objects.nonNull(productPricingSupply)) {
                    Long productPricingSupplyId = productPricingSupply.getId();
                    ProductPricingSupplyCityRangeDTO productPricingSupplyCityRangeDTO = productPricingSupplyService.queryCitySupplyPriceRange(productPricingSupplyId);
                    productSkuDto.setProductSupplyPriceId(productPricingSupplyId);
                    productSkuDto.setCityNum(productPricingSupplyCityRangeDTO.getCityNum());
                    productSkuDto.setStartTime(productPricingSupplyCityRangeDTO.getStartTime());
                    productSkuDto.setEndTime(productPricingSupplyCityRangeDTO.getEndTime());
                }

                ProductSkuPreferentialCostPriceRangeResp costPriceRange = productSkuPreferentialCostPriceRangeMap.get (marketItemDto.getSkuId ());
                if(CollectionUtils.isNotEmpty (productPricingSupplyCityMappingDtos)){
                    BigDecimal supplyMaxPrice = productPricingSupplyCityMappingDtos.stream().filter (e->ObjectUtil.isNotNull (e.getMaxPrice()) && e.getMaxPrice().compareTo(BigDecimal.ZERO) != NumberConstants.ZERO).map(ProductPricingSupplyCityMappingDTO::getMaxPrice).max(Comparator.comparing(x -> x)).orElse(null);
                    BigDecimal supplyMinPrice = productPricingSupplyCityMappingDtos.stream().filter (e->ObjectUtil.isNotNull (e.getMinPrice()) && e.getMinPrice().compareTo(BigDecimal.ZERO) != NumberConstants.ZERO).map(ProductPricingSupplyCityMappingDTO::getMinPrice).min(Comparator.comparing(x -> x)).orElse(null);
                    productSkuDto.setMinPrice(productPricingSupplyService.getMinPrice (supplyMinPrice,costPriceRange));
                    productSkuDto.setMaxPrice(productPricingSupplyService.getMaxPrice (supplyMaxPrice,costPriceRange));
                }
                productSkuDto.setPriceStr(PriceUtil.buildPriceRange(productSkuDto.getMaxPrice (),productSkuDto.getMinPrice ()));
            }
        });
    }
    /**
     * 查询代仓商品库存
     *
     * @param marketItemDtos
     * @param tenantId
     * @return
     */
    public Map<Long, List<ProductAgentWarehouseDataVO>> queryStockInfo(List<MarketItemDTO> marketItemDtos, Long tenantId) {
        // 关联代仓货品的销售商品
        List<Long> skuIds = marketItemDtos.stream()
            .filter(marketItemDTO -> GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(marketItemDTO.getGoodsType()))
            .map(MarketItemDTO::getSkuId)
            .collect(Collectors.toList());
        Map<Long, List<ProductAgentWarehouseDataVO>> ProductAgentWarehouseDataVoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<ProductAgentWarehouseDataVO> productAgentWarehouseDataVos = productAgentSkuService.queryAgentProductWarehouseData(skuIds, tenantId);
            // 根据货品Id进行分组
            ProductAgentWarehouseDataVoMap = productAgentWarehouseDataVos.stream().collect(Collectors.groupingBy(ProductAgentWarehouseDataVO::getSkuId));
        }

        return ProductAgentWarehouseDataVoMap;
    }

    @Override
    public List<MarketItemDTO> list(MarketItemQueryInput marketItemQueryInput, LoginContextInfoDTO loginContextInfoDTO) {
        PageInfo<MarketItemInfoDTO> pageInfo = marketItemFacade.pageMarketItemAllInfo(MarketItemQueryDTO.builder()
            .tenantId(loginContextInfoDTO.getTenantId())
            .marketId(marketItemQueryInput.getMarketId())
            .deleteFlag(MarketDeleteFlagEnum.NORMAL.getFlag())
            .build());
        return buildMarketItemDTO(loginContextInfoDTO.getTenantId(), pageInfo.getList());
    }

    @Override
    public List<MarketItemDTO> selectByParam(MarketItemQueryParam param) {
        Integer pageNum = 1, pageSize = 1000;
        List<MarketItemDTO> result = new ArrayList<>();
        while (true) {
            PageInfo<MarketItemInfoDTO> pageInfo = marketItemFacade.pageMarketItemAllInfo(MarketItemQueryDTO.builder()
                .tenantId(param.getTenantId())
                .marketIds(param.getMarketIds())
                .goodsType(param.getGoodsType())
                .pageNum(pageNum++)
                .pageSize(pageSize)
                .build());
            List<MarketItemDTO> marketItemDTOS = buildMarketItemDTO(param.getTenantId(), pageInfo.getList());
            result.addAll(marketItemDTOS);
            if (pageInfo.getTotal() <= (pageNum - 1) * pageSize || CollectionUtils.isEmpty(pageInfo.getList()) || pageInfo.getList().size() < 1000) {
                break;
            }
        }

        return result;
    }

    /**
     * 构造MarketItemDTO
     * 重复代码抽出
     *
     * @param marketItems
     * @return
     */
    private List<MarketItemDTO> buildMarketItemDTO(Long tenantId, List<MarketItemInfoDTO> marketItems) {
        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyList();
        }
        // 转换为MarketItemDto
        List<MarketItemDTO> marketItemDtos = marketItems.stream().map(marketItem -> {
            MarketItemDTO marketItemDto = MarketConvert.INSTANCE.convert2ItemDto(marketItem);
            // 如果不是自营品，查询货品信息
            if (!Objects.isNull(marketItem.getSkuId()) && GoodsTypeEnum.THIRD_DELIVERY_CODES.contains(marketItem.getGoodsType())) {
                // 查询货品信息
                ProductSkuDTO productSkuDTO = productSkuService.querySkuInfo(marketItem.getSkuId());
                marketItemDto.setProductSkuDTO(productSkuDTO);
            }
            return marketItemDto;
        }).collect(Collectors.toList());

        assemblyItem(marketItemDtos, tenantId);
        return marketItemDtos;
    }

    @Override
    public CommonResult changOnSale(MarketItemOnSaleInput marketItemInput, LoginContextInfoDTO contextInfoDTO) {
        marketItemInput.setTenantId(contextInfoDTO.getTenantId());
        marketFacade.changeOnSale(marketItemInput);
        return CommonResult.ok();
    }

    @Override
    public BatchOnSaleResultVO batchChangOnSale(MarketItemOnSaleInput marketItemInput, LoginContextInfoDTO contextInfoDTO) {
        marketItemInput.setTenantId(contextInfoDTO.getTenantId());
        return marketFacade.batchChangOnSale(marketItemInput);
    }

    @Override
    public List<MarketItemDTO> queryBySkuIds(Collection<Long> skuIds, Long tenantId) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }

        List<MarketItem> marketItems = marketItemRepository.batchQueryBySkuIds(tenantId, skuIds);
        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyList();
        }

        List<MarketItemDTO> marketItemDTOS = marketItems.stream().map(marketItem -> {
            MarketItemDTO marketItemDTO = MarketItemConverter.convertToMarketItemDTO(marketItem, null);
            return marketItemDTO;
        }).collect(Collectors.toList());
        return marketItemDTOS;
    }

    @Override
    public Map<Long, MarketItemDTO> getMapByItemIds(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }

        List<MarketItem> marketItems = marketItemRepository.batchQueryByItemIds(itemIds);
        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyMap();
        }

        List<MarketItemDTO> marketItemDTOS = marketItems.stream().map(marketItem -> {
            MarketItemDTO marketItemDTO = MarketItemConverter.convertToMarketItemDTO(marketItem, null);
            return marketItemDTO;
        }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(marketItemDTOS)) {
            return marketItemDTOS.stream().collect(Collectors.toMap(MarketItemDTO::getId, Function.identity()));
        } else {
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<Long, MarketItemInfoDTO> getMapByItemIds(List<Long> itemIds, Long tenantId) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }
        MarketItemQueryDTO queryDTO = new MarketItemQueryDTO();
        queryDTO.setItemIds(itemIds);
        queryDTO.setTenantId(tenantId);
        queryDTO.setPageSize(500);
        queryDTO.setPageNum(1);
        PageInfo<MarketItemInfoDTO> marketItemInfoDTOPageInfo;
        List<MarketItemInfoDTO> list = new ArrayList<>();
        do {
            marketItemInfoDTOPageInfo = marketItemFacade.pageMarketItemAllInfo(queryDTO);
            if (marketItemInfoDTOPageInfo != null && !CollectionUtils.isEmpty(marketItemInfoDTOPageInfo.getList())) {
                list.addAll(marketItemInfoDTOPageInfo.getList());
            }
            queryDTO.setPageNum(queryDTO.getPageNum() + 1);
        } while (marketItemInfoDTOPageInfo != null && marketItemInfoDTOPageInfo.isHasNextPage());

        return list.stream().collect(Collectors.toMap(MarketItemInfoDTO::getItemId, r -> r, (oldValue, newValue) -> oldValue));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(MarketDeleteDTO marketDeleteDTO) {
        marketFacade.deleteMarketItem(marketDeleteDTO.getTenantId(), marketDeleteDTO.getId());
    }

    @Override
    public List<MarketItem> listAll(MarketItemQueryDTO queryDTO) {
        return marketItemMapper.listAll(queryDTO);
    }

    @Override
    public PageInfo<MarketItemInfoPageVO> queryMarketItemList(MarketItemQueryDTO queryDTO,MarketItemInfoQueryFlagDTO dto) {
        MarketItemInfoQueryFlagReq marketItemInfoQueryFlagReq = MarketItemConverter.flagDTO2Req(dto);
        MarketItemCommonQueryReq req = MarketFacadeConvert.INSTANCE.convert2Query (queryDTO);
        req.setMarketItemInfoQueryFlagReq (marketItemInfoQueryFlagReq);

        PageInfo<MarketItemInfoPageVO> marketItemInfoPageVOPageInfo = marketItemFacade.pageMarketItem (req);
        List<MarketItemInfoPageVO> list = marketItemInfoPageVOPageInfo.getList();
        if(dto.getCategoryIdFlag () && CollectionUtil.isNotEmpty (list)){
            Set<Long> categoryIds = list.stream()
            .map(MarketItemInfoPageVO::getThirdCategoryId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
            Map<Long, ProductCategoryDTO> productCategoryDTOMap = categoryServiceFacade.selectWholeCategoryNewBatch(categoryIds);
            for (MarketItemInfoPageVO marketItem : list) {
                ProductCategoryDTO categoryDTO = productCategoryDTOMap.getOrDefault(marketItem.getThirdCategoryId(), new ProductCategoryDTO());
                marketItem.setFirstCategoryId(categoryDTO.getFirstCategoryId());
                marketItem.setFirstCategory(categoryDTO.getFirstCategoryName());
                marketItem.setSecondCategoryId(categoryDTO.getSecondCategoryId());
                marketItem.setSecondCategory(categoryDTO.getSecondCategoryName());
                marketItem.setThirdCategoryId(categoryDTO.getThirdCategoryId());
                marketItem.setThirdCategory(categoryDTO.getThirdCategoryName());
                marketItem.setCategoryStr(categoryDTO.getCategoryStr());
            }
        }

        if(CollectionUtil.isNotEmpty (list)) {
            List<Long> itemIds = list.stream().map(MarketItemInfoPageVO::getId).distinct().collect(Collectors.toList());
            // 补充限售
            Map<Long, ItemSaleLimitConfigDTO>  itemSaleLimitConfigMap = itemSaleLimitConfigService.queryItemSaleLimitConfigMap(queryDTO.getTenantId(), itemIds);
            for (MarketItemInfoPageVO marketItem : list) {
                ItemSaleLimitConfigDTO itemSaleLimitConfigDTO = itemSaleLimitConfigMap.getOrDefault(marketItem.getId(), ItemSaleLimitConfigDTO.DEFAULT());
                marketItem.setSaleLimitRule(itemSaleLimitConfigDTO.getSaleLimitRule());
                marketItem.setSaleLimitQuantity(itemSaleLimitConfigDTO.getSaleLimitQuantity());
            }
        }

        return marketItemInfoPageVOPageInfo;
    }

    @Override
    public List<MarketItemInfoPageVO> queryMarketItemListWithUnit(Long tenantId, Set<Long> itemIds) {
        MarketItemCommonQueryReq queryDTO = new MarketItemCommonQueryReq ();
        queryDTO.setTenantId (tenantId);
        queryDTO.setCombineFlag(Boolean.FALSE);
        queryDTO.setItemIds (new ArrayList<> (itemIds));
        queryDTO.setDeleteFlag (DeleteFlagEnum.NORMAL.getFlag ());
        queryDTO.setPageNum (1);
        queryDTO.setPageSize(itemIds.size ());
        MarketItemInfoQueryFlagReq flagDTO = new MarketItemInfoQueryFlagReq();
        flagDTO.setClassificationIdFlag(false);
        flagDTO.setPriceRangeFlag (false);
        flagDTO.setCategoryIdFlag (false);
        flagDTO.setUnitFlag (true);
        queryDTO.setMarketItemInfoQueryFlagReq (flagDTO);

        PageInfo<MarketItemInfoPageVO> marketItemInfoPageVOPageInfo = marketItemFacade.pageMarketItem (queryDTO);
        return marketItemInfoPageVOPageInfo.getList();
    }

    @Override
    public void batchUpdatePriceStrategy(Long tenantId, PriceStrategyFloatingRangeDTO dto) {
        priceFacade.batchUpdatePriceStrategy(tenantId, dto);
    }

    @Override
    public PriceStrategyRangeVO batchQueryPriceStrategy(Long tenantId, List<Long> marketItemIds) {
        return priceFacade.batchQueryPriceStrategy(tenantId, marketItemIds);
    }

    @Override
    public BigDecimal queryMaxCostPrice(Long tenantId, MaxCostPriceQueryDTO dto) {
        // 根据门店分组查询门店ID
        if (CollectionUtils.isNotEmpty(dto.getStoreGroupIds())) {
            List<MerchantStoreGroupResultResp> merchantStoreGroupResultResps = merchantStoreGroupMappingService.selectByGroupId(dto.getStoreGroupIds(), tenantId);
            if (CollectionUtils.isEmpty(merchantStoreGroupResultResps)) {
                log.info("没有门店，tenantId={},storeGroupIds={}", tenantId, dto.getStoreGroupIds());
                return null;
            } else {
                dto.setStoreIds(merchantStoreGroupResultResps.stream()
                    .map(MerchantStoreGroupResultResp::getStoreId)
                    .collect(Collectors.toList()));
            }
        }
        List<MerchantStoreAddressDTO> addressDtos = merchantStoreService.listStoreIdAndAddress(tenantId, dto.getStoreIds());
        if (CollectionUtils.isEmpty(addressDtos)) {
            log.info("没有门店");
            return null;
        }
        return priceFacade.queryMaxCostPrice(tenantId, dto.getSkuId(), addressDtos);
    }

    @Override
    public ExcelImportResDTO importPriceStrategy(Long tenantId, MultipartFile file) throws IOException {
        // 读excel
        List<ItemPriceStrategyExcelDataInput> list = null;
        try {
            list = ExcelUtils.read(file.getInputStream(), ItemPriceStrategyExcelDataInput.class);
        } catch (Exception e) {
            log.error("importPriceStrategy,读取失败，tenantid={}", tenantId, e);
            throw new DefaultServiceException("表格读取失败，请检查数据格式后重新试试，或联系客服处理一下~");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new DefaultServiceException("导入商品价格数据不能为空");
        }
        log.info("importPriceStrategy.excellist={}", JSON.toJSONString(list));
        if (list.size() > NumberConstants.ONE_THOUSAND) {
            throw new DefaultServiceException("单次导入数量上限为1000个");
        }
        try {
            Set<Long> dealIds = new HashSet<>();
            List<MarketItemPriceStrategyInput> priceStrategyInputs = new ArrayList<>();
            List<ItemPriceStrategyExcelDataInput> errorList = new ArrayList<>();
            for (ItemPriceStrategyExcelDataInput input : list) {
                if (dealIds.contains(input.getId())) {
                    input.setErrorMessage("编码重复");
                    errorList.add(input);
                    continue;
                } else {
                    dealIds.add(input.getId());
                }
                String errorMessage = checkPriceStrategyImportData(input);
                if (!StringUtils.isBlank(errorMessage)) {
                    input.setErrorMessage(errorMessage);
                    errorList.add(input);
                } else {
                    MarketItemPriceStrategyInput priceStrategyInput = new MarketItemPriceStrategyInput();
                    priceStrategyInput.setItemId(input.getId());
                    priceStrategyInput.setTenantId(tenantId);
                    priceStrategyInput.setPriceType(MarketAreaItemPriceTypeEnum.ALL_STORE_UNIFIED_PRICE.getCode());
                    MarketAreaItemMappingInput itemMappingInput = new MarketAreaItemMappingInput();
                    itemMappingInput.setStorePriceType(StorePriceTypeEnum.ALL.getCode());
                    itemMappingInput.setPriceType(NumberConstants.ONE);
                    itemMappingInput.setType(PriceStrategyTypeExcelEnum.getByDesc(input.getStrategyType()).getCode());
                    itemMappingInput.setMappingNumber(input.getStrategyValue());
                    priceStrategyInput.setDefaultPrice(itemMappingInput);
                    priceStrategyInputs.add(priceStrategyInput);
                }
            }
            if (CollectionUtils.isNotEmpty(priceStrategyInputs)) {
                log.info("importPriceStrategy.excellist={}", JSON.toJSONString(list));
                List<Long> errorIds = errorList.stream().map(ItemPriceStrategyExcelDataInput::getId).collect(Collectors.toList());
                Map<Long, List<ItemPriceStrategyExcelDataInput>> itemMap = list.stream().filter(e -> !errorIds.contains(e.getId())).collect(Collectors.groupingBy(ItemPriceStrategyExcelDataInput::getId));
                log.info("importPriceStrategy.itemMap={}", JSON.toJSONString(itemMap));
                List<MarketItemPriceStrategyUpdateResultResp> resp = marketFacade.batchUpdateTenantTargetTypePriceStrategy(priceStrategyInputs);
                resp.stream().filter(e -> !e.getSuccessFlag()).forEach(e -> {
                    Long itemId = e.getItemId();
                    List<ItemPriceStrategyExcelDataInput> inputs = itemMap.get(itemId);
                    log.info("importPriceStrategy.itemMap.get={}", JSON.toJSONString(inputs));
                    if (CollectionUtils.isNotEmpty(inputs)) {
                        inputs.forEach(item -> {
                            item.setErrorMessage(e.getErrorMessage());
                            errorList.add(item);
                        });
                    }
                });
            }
            // 异常数据写入excel
            String qiNiuFilePath = null;
            if (!CollectionUtils.isEmpty(errorList)) {
                String filePath = commonService.exportExcel(errorList, ExcelTypeEnum.IMPORT_ITEM_PRICE_STRATEGY.getName());
                qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "导入商品价格错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xlsx");
                commonService.deleteFile(filePath);
            }
            // 返回导入结果
            ExcelImportResDTO excelImportResDTO = new ExcelImportResDTO();
            excelImportResDTO.setFailRow(CollectionUtils.isEmpty(errorList) ? NumberConstants.ZERO : errorList.size());
            excelImportResDTO.setSuccessRow(list.size() - excelImportResDTO.getFailRow());
            excelImportResDTO.setErrorFilePath(qiNiuFilePath);
            return excelImportResDTO;
        } catch (Exception e) {
            log.error("请检查excel格式，或者联系管理员", e);
            throw new BizException("请检查excel格式，或者联系管理员");
        }
    }

    @Override
    public ExcelImportResDTO importOnSaleStrategy(Long tenantId, MultipartFile file) throws IOException {
        // 读excel
        List<ItemOnSaleStrategyExcelDataInput> list = null;
        try {
            list = ExcelUtils.read(file.getInputStream(), ItemOnSaleStrategyExcelDataInput.class);
        } catch (Exception e) {
            log.error("importOnSaleStrategy,读取失败，tenantid={}", tenantId, e);
            throw new DefaultServiceException("表格读取失败，请检查数据格式后重新试试，或联系客服处理一下~");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new DefaultServiceException("导入商品上下架数据不能为空");
        }
        log.info("importOnSaleStrategy.excellist={}", JSON.toJSONString(list));
        if (list.size() > NumberConstants.FIVE_HUNDRED) {
            throw new DefaultServiceException("单次导入数量上限为500个");
        }
        try {
            Set<Long> dealIds = new HashSet<>();
            List<ItemOnSaleStrategyExcelDataInput> dealList = new ArrayList<>();
            List<ItemOnSaleStrategyExcelDataInput> errorList = new ArrayList<>();
            for (ItemOnSaleStrategyExcelDataInput input : list) {
                if (dealIds.contains(input.getId())) {
                    input.setErrorMessage("编码重复");
                    errorList.add(input);
                    continue;
                } else {
                    dealIds.add(input.getId());
                    dealList.add(input);
                }
                String errorMessage = checkOnsaleStrategyImportData(input);
                if (!StringUtils.isBlank(errorMessage)) {
                    input.setErrorMessage(errorMessage);
                    errorList.add(input);
                }
            }
            Map<String, List<Long>> resultMap = dealList.stream().collect(Collectors.groupingBy(ItemOnSaleStrategyExcelDataInput::getOnsaleType, Collectors.mapping(ItemOnSaleStrategyExcelDataInput::getId, Collectors.toList())));
            log.info("importOnSaleStrategy.resultMap={}", JSON.toJSONString(resultMap));

            MarketItemOnSaleInput soldOut = null;
            MarketItemOnSaleInput onSale = null;

            if (resultMap.containsKey(OnsaleTypeExcelEnum.SOLD_OUT.getDesc())) {
                soldOut = new MarketItemOnSaleInput();

                soldOut.setItemIds(resultMap.get(OnsaleTypeExcelEnum.SOLD_OUT.getDesc()));
                soldOut.setOnSale(OnsaleTypeExcelEnum.SOLD_OUT.getCode());
                soldOut.setTenantId(tenantId);
            }
            if (resultMap.containsKey(OnsaleTypeExcelEnum.ON_SALE.getDesc())) {
                onSale = new MarketItemOnSaleInput();

                onSale.setItemIds(resultMap.get(OnsaleTypeExcelEnum.ON_SALE.getDesc()));
                onSale.setOnSale(OnsaleTypeExcelEnum.ON_SALE.getCode());
                onSale.setTenantId(tenantId);
            }

            BatchOnSaleResultVO batchOnSaleResultVO = marketFacade.batchChangOnSaleIncludeAllStatus(soldOut, onSale);

            Map<Long, List<ItemOnSaleStrategyExcelDataInput>> itemMap = dealList.stream().collect(Collectors.groupingBy(ItemOnSaleStrategyExcelDataInput::getId));
            log.info("importOnSaleStrategy.itemMap={}", JSON.toJSONString(itemMap));

            List<OnSaleFailResultVO> error = batchOnSaleResultVO.getResultList();
            if (CollectionUtils.isNotEmpty(error)) {
                error.forEach(e -> {
                    Long itemId = e.getItemId();
                    List<ItemOnSaleStrategyExcelDataInput> inputs = itemMap.get(itemId);
                    log.info("importOnSaleStrategy.itemMap.get={}", JSON.toJSONString(inputs));
                    if (CollectionUtils.isNotEmpty(inputs)) {
                        inputs.forEach(item -> {
                            item.setErrorMessage(e.getFailReason());
                            errorList.add(item);
                        });
                    }
                });
            }
            // 异常数据写入excel
            String qiNiuFilePath = null;
            if (!CollectionUtils.isEmpty(errorList)) {
                String filePath = commonService.exportExcel(errorList, ExcelTypeEnum.IMPORT_ITEM_ONSALE_STRATEGY.getName());
                qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "导入商品上下架错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xlsx");
                commonService.deleteFile(filePath);
            }
            // 返回导入结果
            ExcelImportResDTO excelImportResDTO = new ExcelImportResDTO();
            excelImportResDTO.setFailRow(CollectionUtils.isEmpty(errorList) ? NumberConstants.ZERO : errorList.size());
            excelImportResDTO.setSuccessRow(list.size() - errorList.size());
            excelImportResDTO.setErrorFilePath(qiNiuFilePath);
            return excelImportResDTO;
        } catch (Exception e) {
            log.error("请检查excel格式，或者联系管理员", e);
            throw new BizException("请检查excel格式，或者联系管理员");
        }
    }

    @Override
    public ExcelImportResDTO importNoGoodsSupplyPrice(Long tenantId, MultipartFile file) {
        // 读excel
        List<ItemNoGoodsSupplyPriceExcelDataInput> list = null;
        try {
            list = ExcelUtils.read(file.getInputStream(), ItemNoGoodsSupplyPriceExcelDataInput.class);
        } catch (Exception e) {
            log.error("importNoGoodsSupplyPrice,读取失败，tenantid={}", tenantId, e);
            throw new DefaultServiceException("表格读取失败，请检查数据格式后重新试试，或联系客服处理一下~");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new DefaultServiceException("导入商品成本价数据不能为空");
        }
        log.info("importNoGoodsSupplyPrice.excellist={}", JSON.toJSONString(list));
        if (list.size() > NumberConstants.FIVE_HUNDRED) {
            throw new DefaultServiceException("单次导入数量上限为500个");
        }
        try {
            Set<Long> dealIds = new HashSet<>();
            List<ItemNoGoodsSupplyPriceExcelDataInput> dealList = new ArrayList<>();
            List<ItemNoGoodsSupplyPriceExcelDataInput> errorList = new ArrayList<>();
            for (ItemNoGoodsSupplyPriceExcelDataInput input : list) {
                if (dealIds.contains(input.getItemId())) {
                    input.setErrorMessage("编码重复");
                    errorList.add(input);
                } else {
                    dealIds.add(input.getItemId());
                    String errorMessage = checkItemNoGoodsSupplyPriceImportData(input);
                    if (!StringUtils.isBlank(errorMessage)) {
                        input.setErrorMessage(errorMessage);
                        errorList.add(input);
                    } else {
                        dealList.add(input);
                    }
                }
            }
            Set<Long> supplierIds = dealList.stream().map(ItemNoGoodsSupplyPriceExcelDataInput::getSupplierId).collect(Collectors.toSet());
            Map<Long, SupplierInfoDTO> supplierInfoDTOMap = supplierService.batchQuerySupplierMap(tenantId, new ArrayList<>(supplierIds));

            List<ItemNoGoodsSupplyPriceExcelDataInput> result = new ArrayList<>();
            for (ItemNoGoodsSupplyPriceExcelDataInput input : dealList) {
                if (!supplierInfoDTOMap.containsKey(input.getSupplierId())) {
                    input.setErrorMessage("供应商不存在");
                    errorList.add(input);
                } else {
                    input.setSupplierName(supplierInfoDTOMap.get(input.getSupplierId()).getSupplierName());
                    result.add(input);
                }
            }
            BatchChangNoGoodsSupplyPriceResultVO batchChangNoGoodsSupplyPriceResultVO = marketItemFacade.batchChangNoGoodsSupplyPrice(tenantId, result);

            Map<Long, List<ItemNoGoodsSupplyPriceExcelDataInput>> itemMap = result.stream().collect(Collectors.groupingBy(ItemNoGoodsSupplyPriceExcelDataInput::getItemId));
            log.info("importNoGoodsSupplyPrice.itemMap={}", JSON.toJSONString(itemMap));

            List<NoGoodsSupplyPriceResultVO> error = batchChangNoGoodsSupplyPriceResultVO.getResultList();
            if (CollectionUtils.isNotEmpty(error)) {
                error.forEach(e -> {
                    Long itemId = e.getItemId();
                    List<ItemNoGoodsSupplyPriceExcelDataInput> inputs = itemMap.get(itemId);
                    log.info("importNoGoodsSupplyPrice.itemMap.get={}", JSON.toJSONString(inputs));
                    if (CollectionUtils.isNotEmpty(inputs)) {
                        inputs.forEach(item -> {
                            item.setErrorMessage(e.getFailReason());
                            errorList.add(item);
                        });
                    }
                });
            }
            // 异常数据写入excel
            String qiNiuFilePath = null;
            if (!CollectionUtils.isEmpty(errorList)) {
                String filePath = commonService.exportExcel(errorList, ExcelTypeEnum.IMPORT_NO_GOODS_SUPPLY_PRICE.getName());
                qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "导入商品成本价架错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xlsx");
                commonService.deleteFile(filePath);
            }
            // 返回导入结果
            ExcelImportResDTO excelImportResDTO = new ExcelImportResDTO();
            excelImportResDTO.setFailRow(CollectionUtils.isEmpty(errorList) ? NumberConstants.ZERO : errorList.size());
            excelImportResDTO.setSuccessRow(list.size() - errorList.size());
            excelImportResDTO.setErrorFilePath(qiNiuFilePath);
            return excelImportResDTO;
        } catch (Exception e) {
            log.error("请检查excel格式，或者联系管理员", e);
            throw new BizException("请检查excel格式，或者联系管理员");
        }
    }

    @Override
    public void updateItemCode(Long tenantId, ItemCodeUpdateDTO dto) {
        marketItemFacade.updateItemCode(dto.getItemCode(), dto.getMarketItemId(), tenantId);
    }

    private String checkItemNoGoodsSupplyPriceImportData(ItemNoGoodsSupplyPriceExcelDataInput input) {
        if (ObjectUtil.isNull(input.getItemId())) {
            return "商品编码不能为空";
        }
        if (ObjectUtil.isNull(input.getSupplierId())) {
            return "供应商编码不能为空";
        }
        if (Objects.isNull(input.getNoGoodsSupplyPrice())) {
            return "定价值不能为空";
        } else {
            if (input.getNoGoodsSupplyPrice().compareTo(BigDecimal.ZERO) <= 0) {
                return "定价值不能小于等于0";
            }
            if (input.getNoGoodsSupplyPrice().compareTo(new BigDecimal(100000)) > 0) {
                return "定价值不能大于100000";
            }
        }
        return null;
    }

    private String checkPriceStrategyImportData(ItemPriceStrategyExcelDataInput input) {
        String strategyType = input.getStrategyType();
        BigDecimal strategyValue = input.getStrategyValue();

        if (ObjectUtil.isNull(input.getId())) {
            return "商品编码不能为空";
        }
        if (StringUtils.isBlank(strategyType)) {
            return "定价方式不能为空";
        }
        if (Objects.isNull(strategyValue)) {
            return "定价值不能为空";
        } else {
            if (strategyValue.compareTo(new BigDecimal(1000000)) > 0) {
                return "定价值不能大于1000000";
            }
            if (PriceStrategyTypeExcelEnum.ASSIGN.getDesc().equals(strategyType)) {
                if (strategyValue.compareTo(BigDecimal.ZERO) <= 0) {
                    return "定价值不能小于等于0";
                }
            } else if (PriceStrategyTypeExcelEnum.COST_PRICE_ADD_FIXED.getDesc().equals(strategyType)) {
                if (strategyValue.compareTo(BigDecimal.ZERO) < 0) {
                    return "定价值不能小于0";
                }
            } else if (PriceStrategyTypeExcelEnum.COST_PRICE_ADD_PERCENTAGE.getDesc().equals(strategyType)) {
                if (strategyValue.compareTo(BigDecimal.ZERO) < -100) {
                    return "定价值不能小于-100";
                }
            } else {
                return "定价方式错误";
            }
            return null;
        }
    }

    private String checkOnsaleStrategyImportData(ItemOnSaleStrategyExcelDataInput input) {
        if (ObjectUtil.isNull(input.getId())) {
            return "商品编码不能为空";
        }

        String onsaleType = input.getOnsaleType();

        if (StringUtils.isBlank(onsaleType)) {
            return "定价方式不能为空";
        }
        if (OnsaleTypeExcelEnum.getByDesc(onsaleType) == null) {
            return "定价方式错误";
        }
        return null;
    }

    @Override
    public MarketItemDTO queryItemPriceStrategy(Long tenantId, Long itemId) {
        MarketItemDTO marketItemDto = marketFacade.queryItemPriceStrategy(tenantId, itemId);
        // 补充门店信息
        fillStoreList(marketItemDto);
        // 如果不是自营品，查询货品信息
        if (!Objects.isNull(marketItemDto.getSkuId()) && GoodsTypeEnum.THIRD_DELIVERY_CODES.contains(marketItemDto.getGoodsType())) {
            // 查询货品信息
            ProductSkuDTO productSkuDTO = productSkuService.querySkuInfo(marketItemDto.getSkuId());
            marketItemDto.setProductSkuDTO(productSkuDTO);
        }
        assemblyItem(Collections.singletonList(marketItemDto), marketItemDto.getTenantId());
        return marketItemDto;
    }

    @Override
    public void updatePriceStrategy(MarketItemPriceStrategyInput input) {
        marketFacade.updatePriceStrategy(input);
    }
    @Override
    public Map<Long,Boolean> canUseCoupon(List<Long> itemIds,Long tenantId) {
        MarketItemCommonQueryReq queryDTO = new MarketItemCommonQueryReq ();
        queryDTO.setTenantId (tenantId);
        queryDTO.setCombineFlag(Boolean.FALSE);
        queryDTO.setItemIds (new ArrayList<> (itemIds));
        queryDTO.setDeleteFlag (DeleteFlagEnum.NORMAL.getFlag ());
        queryDTO.setPageNum (1);
        queryDTO.setPageSize(itemIds.size ());
        MarketItemInfoQueryFlagReq flagDTO = new MarketItemInfoQueryFlagReq();
        flagDTO.setClassificationIdFlag(true);
        flagDTO.setPriceRangeFlag (false);
        flagDTO.setCategoryIdFlag (false);
        flagDTO.setUnitFlag (false);
        queryDTO.setMarketItemInfoQueryFlagReq (flagDTO);

        PageInfo<MarketItemInfoPageVO> marketItemInfoPageVOPageInfo = marketItemFacade.pageMarketItem (queryDTO);
        List<MarketItemInfoPageVO> list = marketItemInfoPageVOPageInfo.getList ();

        // 解析商品分组 获取限制商品组和运费配置
        Set<Long> limitClassificationIds;
        // 查询租户资金账户配置
        TenantFundAccountConfigVO config = tenantFundAccountConfigService.getByTenantId(tenantId);
        if (config != null && StringUtils.isNotEmpty (config.getLimitGoodsGroup())) {
            limitClassificationIds = parseLimitGoodsGroup(config.getLimitGoodsGroup());
        } else {
            limitClassificationIds = Collections.emptySet ();
        }
        if(CollectionUtils.isNotEmpty (limitClassificationIds)) {
            return list.stream ().collect (Collectors.toMap (MarketItemInfoPageVO::getId, item -> limitClassificationIds.contains (item.getSecondClassificationId ())));
        }else{
            return list.stream ().collect (Collectors.toMap (MarketItemInfoPageVO::getId, item -> true));
        }
    }
    private Set<Long> parseLimitGoodsGroup(String limitGoodsGroup) {
        try {
            if (org.apache.commons.lang3.StringUtils.isBlank(limitGoodsGroup)) {
                return Collections.emptySet();
            }

            // 格式是这样的[1,2,3]
            String replaced = limitGoodsGroup.replaceAll("[\\[\\]]", "");
            if (org.apache.commons.lang3.StringUtils.isBlank(replaced)) {
                return Collections.emptySet();
            }
            return Arrays.stream(replaced.split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            return Collections.emptySet();
        }
    }

}
