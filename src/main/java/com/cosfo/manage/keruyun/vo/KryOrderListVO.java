/**
  * Copyright 2023 bejson.com 
  */
package com.cosfo.manage.keruyun.vo;

/**
 * Auto-generated: 2023-11-30 13:45:53
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class KryOrderListVO {

    private Long orderId;
    private Integer tradeType;
    private Integer tradeStatus;
    private String tradeNo;
    private Long orderTime;
    private Long checkOutTime;
    private Integer source;
    private String sourceName;
    private Integer receivedAmount;
    private Integer custRealPay;
    private Integer tradeAmount;
    private Integer privilegeAmount;
    public void setOrderId(Long orderId) {
         this.orderId = orderId;
     }
     public Long getOrderId() {
         return orderId;
     }

    public void setTradeType(Integer tradeType) {
         this.tradeType = tradeType;
     }
     public Integer getTradeType() {
         return tradeType;
     }

    public void setTradeStatus(Integer tradeStatus) {
         this.tradeStatus = tradeStatus;
     }
     public Integer getTradeStatus() {
         return tradeStatus;
     }

    public void setTradeNo(String tradeNo) {
         this.tradeNo = tradeNo;
     }
     public String getTradeNo() {
         return tradeNo;
     }

    public void setOrderTime(Long orderTime) {
         this.orderTime = orderTime;
     }
     public Long getOrderTime() {
         return orderTime;
     }

    public void setCheckOutTime(Long checkOutTime) {
         this.checkOutTime = checkOutTime;
     }
     public Long getCheckOutTime() {
         return checkOutTime;
     }

    public void setSource(Integer source) {
         this.source = source;
     }
     public Integer getSource() {
         return source;
     }

    public void setSourceName(String sourceName) {
         this.sourceName = sourceName;
     }
     public String getSourceName() {
         return sourceName;
     }

    public void setReceivedAmount(Integer receivedAmount) {
         this.receivedAmount = receivedAmount;
     }
     public Integer getReceivedAmount() {
         return receivedAmount;
     }

    public void setCustRealPay(Integer custRealPay) {
         this.custRealPay = custRealPay;
     }
     public Integer getCustRealPay() {
         return custRealPay;
     }

    public void setTradeAmount(Integer tradeAmount) {
         this.tradeAmount = tradeAmount;
     }
     public Integer getTradeAmount() {
         return tradeAmount;
     }

    public void setPrivilegeAmount(Integer privilegeAmount) {
         this.privilegeAmount = privilegeAmount;
     }
     public Integer getPrivilegeAmount() {
         return privilegeAmount;
     }

}