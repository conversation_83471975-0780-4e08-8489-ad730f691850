package com.cosfo.manage.msg.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-18
 * @Description:
 */
@Data
public class MsgTipMarkDTO {

    /**
     * 发送记录ids
     */
    private List<Long> ids;

    /**
     * 标记状态0=未读,1=已读
     */
    @NotNull(message = "readStatus不能为空")
    private Integer readStatus;

}
