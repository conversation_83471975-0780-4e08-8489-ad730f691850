package com.cosfo.manage.wechat.bean.wxa;

public class GetnearbypoilistData{

	private Integer left_apply_num;

	private Integer max_apply_num;

	private GetnearbypoilistDataData data;

	public Integer getLeft_apply_num() {
		return left_apply_num;
	}

	public void setLeft_apply_num(Integer left_apply_num) {
		this.left_apply_num = left_apply_num;
	}

	public Integer getMax_apply_num() {
		return max_apply_num;
	}

	public void setMax_apply_num(Integer max_apply_num) {
		this.max_apply_num = max_apply_num;
	}

	public GetnearbypoilistDataData getData() {
		return data;
	}

	public void setData(GetnearbypoilistDataData data) {
		this.data = data;
	}

}
