package com.cosfo.manage.wechat.bean.paymch;

import com.cosfo.manage.wechat.bean.DynamicField;
import lombok.Data;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 退款查询
 *
 * <AUTHOR>
 *
 */

@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class RefundqueryResult extends MchBase implements DynamicField {

	private String device_info;

	private String transaction_id;

	private String out_trade_no;

	private Integer total_fee;
	
	private Integer settlement_total_fee;
	
	private String fee_type;

	private Integer cash_fee;

	private Integer refund_count;
	
	private String refund_account;
	
	/**
	 * 订单总退款次数
	 * @since 2.8.31
	 */
	private Integer total_refund_count;

	@XmlTransient
	private List<RefundqueryResultItem> refundqueryResultItems;

	
	@Override
	public void buildDynamicField(Map<String, String> dataMap) {
		if(dataMap != null){
			String refund_countStr = dataMap.get("refund_count");
			if(refund_countStr != null){
				List<RefundqueryResultItem> list = new ArrayList<RefundqueryResultItem>();
				for (int i = 0; i < Integer.parseInt(refund_countStr); i++) {
					RefundqueryResultItem item = new RefundqueryResultItem();
					item.setOut_refund_no(dataMap.get("out_refund_no_"+i));
					item.setRefund_id(dataMap.get("refund_id_"+i));
					item.setRefund_channel(dataMap.get("refund_channel_"+i));
					item.setRefund_fee(dataMap.get("refund_fee_"+i)==null?null:Integer.parseInt(dataMap.get("refund_fee_"+i)));
					item.setSettlement_refund_fee(dataMap.get("settlement_refund_fee_"+i)==null?null:Integer.parseInt(dataMap.get("settlement_refund_fee_"+i)));
					item.setCoupon_type(dataMap.get("coupon_type_"+i));
					item.setCoupon_refund_fee(dataMap.get("coupon_refund_fee_"+i)==null?null:Integer.parseInt(dataMap.get("coupon_refund_fee_"+i)));
					item.setCoupon_refund_count(dataMap.get("coupon_refund_count_"+i)==null?null:Integer.parseInt(dataMap.get("coupon_refund_count_"+i)));
					item.setCoupon_refund(dataMap.get("coupon_refund_"+i));
					item.setRefund_status(dataMap.get("refund_status_"+i));
					item.setRefund_recv_accout(dataMap.get("refund_recv_accout_"+i));
					item.setN(i);
					if(item.getCoupon_refund_count()!= null){
						List<Coupon> couponList = new ArrayList<Coupon>();
						for(int j=0;j<item.getCoupon_refund_count();j++){
							Coupon coupon = new Coupon(
									null,
									dataMap.get("coupon_refund_id_"+i+"_"+j),
									dataMap.get("coupon_refund_fee_"+i+"_"+j) == null ? null:Integer.parseInt(dataMap.get("coupon_refund_fee_"+i+"_"+j)), 
									j);
							couponList.add(coupon);
						}
						item.setCoupons(couponList);
					}
					list.add(item);
				}
				this.refundqueryResultItems = list;
			}
		}
	}
	
	
}
