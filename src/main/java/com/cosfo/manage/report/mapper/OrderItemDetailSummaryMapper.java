package com.cosfo.manage.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cosfo.manage.bill.model.dto.TenantBillQueryDTO;
import com.cosfo.manage.report.model.dto.OrderPaymentStatistics;
import com.cosfo.manage.report.model.dto.OrderSummaryDTO;
import com.cosfo.manage.report.model.po.OrderItemDetailSummary;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <p>
 * 订单明细详情汇总 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Mapper
public interface OrderItemDetailSummaryMapper extends BaseMapper<OrderItemDetailSummary> {

    @Select("select * from order_item_detail_summary ${ew.customSqlSegment}")
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
    @ResultType(OrderItemDetailSummary.class)
    void queryByConditionWithHandler(@Param(Constants.WRAPPER) Wrapper wrapper, ResultHandler<?> resultHandler);

    /**
     * 查询门店支付数
     * @param tenantBillQueryDTO
     */
    List<OrderPaymentStatistics> queryStoreCounts(TenantBillQueryDTO tenantBillQueryDTO);

    /**
     * 查询汇总
     *
     * @param tenantBillQueryDTO
     * @return {@link OrderSummaryDTO}
     */
    OrderSummaryDTO querySummary(TenantBillQueryDTO tenantBillQueryDTO);

}
