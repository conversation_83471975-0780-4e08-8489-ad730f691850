package com.cosfo.manage.report.service.impl;

import com.cosfo.manage.report.repository.OrderSoldBelowSupplySummaryRepository;
import com.cosfo.manage.report.service.OrderSoldBelowSupplySummaryService;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @desc
 * <AUTHOR>
 * @date 2023/4/26 9:34
 */
@Deprecated
@Service
public class OrderSoldBelowSupplySummaryServiceImpl implements OrderSoldBelowSupplySummaryService {

    @Resource
    private OrderSoldBelowSupplySummaryRepository orderSoldBelowSupplySummaryRepository;

    @Override
    public void queryByConditionWithHandler(Long tenantId, Long supplierId, LocalDateTime startTime, LocalDateTime endTime, ResultHandler<?> resultHandler) {
        orderSoldBelowSupplySummaryRepository.queryByConditionWithHandler(tenantId, supplierId, startTime, endTime, resultHandler);
    }
}
