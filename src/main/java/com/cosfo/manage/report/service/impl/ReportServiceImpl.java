package com.cosfo.manage.report.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.common.excel.easyexcel.ExcelLargeDataSetExporter;
import com.cosfo.common.excel.easyexcel.LargeDataSetExporter;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.ChartNameConstant;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.TimeDimensionEnum;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDetailPurchaseDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDetailPurchaseQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStorePurchaseQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStorePurchase;
import com.cosfo.manage.merchant.model.vo.MerchantStorePurchaseVO;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.product.model.dto.ProductDetailSalesDTO;
import com.cosfo.manage.product.model.dto.ProductDetailSalesQueryDTO;
import com.cosfo.manage.product.model.dto.ProductMovementQueryDTO;
import com.cosfo.manage.product.model.dto.ProductSalesOverviewQueryDTO;
import com.cosfo.manage.product.model.po.ProductMovement;
import com.cosfo.manage.product.model.po.ProductSalesOverview;
import com.cosfo.manage.product.model.vo.CategoryVO;
import com.cosfo.manage.product.model.vo.ProductMovementVO;
import com.cosfo.manage.product.model.vo.ProductSalesOverviewVO;
import com.cosfo.manage.product.service.ProductCategoryService;
import com.cosfo.manage.report.converter.ReportConverter;
import com.cosfo.manage.report.mapper.MerchantStoreDetailPurchaseMapper;
import com.cosfo.manage.report.mapper.MerchantStorePurchaseMapper;
import com.cosfo.manage.report.mapper.ProductDetailSalesMapper;
import com.cosfo.manage.report.mapper.ProductMovementMapper;
import com.cosfo.manage.report.mapper.ProductSalesOverviewMapper;
import com.cosfo.manage.report.model.dto.ExportParamDTO;
import com.cosfo.manage.report.model.dto.PurchasesBackDetailReportDTO;
import com.cosfo.manage.report.model.dto.ReportCommonQueryDTO;
import com.cosfo.manage.report.model.dto.ReportQueryDTO;
import com.cosfo.manage.report.model.po.ChartData;
import com.cosfo.manage.report.model.po.ChartLine;
import com.cosfo.manage.report.model.po.DamageDetailReport;
import com.cosfo.manage.report.model.po.DamageSaleRatioReport;
import com.cosfo.manage.report.model.po.PurchaseDetailReport;
import com.cosfo.manage.report.model.po.PurchasesBackDetailReport;
import com.cosfo.manage.report.model.vo.DamageDetailReportVO;
import com.cosfo.manage.report.model.vo.DamageSaleRatioDetailReportVO;
import com.cosfo.manage.report.model.vo.PurchaseDetailAggVO;
import com.cosfo.manage.report.model.vo.PurchaseDetailReportVO;
import com.cosfo.manage.report.model.vo.PurchasesBackDetailReportVO;
import com.cosfo.manage.report.repository.DamageDetailReportRepository;
import com.cosfo.manage.report.repository.DamageSaleRatioReportRepository;
import com.cosfo.manage.report.repository.PurchaseDetailReportRepository;
import com.cosfo.manage.report.repository.PurchasesBackDetailReportRepository;
import com.cosfo.manage.report.service.ReportService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description
 * @date 2022/10/11 15:37
 */
@Slf4j
@Service
public class ReportServiceImpl implements ReportService {

    @Resource
    private ProductSalesOverviewMapper productSalesOverviewMapper;
    @Resource
    private ProductMovementMapper productMovementMapper;
    @Resource
    private MerchantStorePurchaseMapper merchantStorePurchaseMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private ProductDetailSalesMapper productDetailSalesMapper;
    @Resource
    private MerchantStoreDetailPurchaseMapper merchantStoreDetailPurchaseMapper;
    @Resource
    private DamageDetailReportRepository damageDetailReportRepository;
    @Resource
    private DamageSaleRatioReportRepository damageSaleRatioReportRepository;
    @Resource
    private PurchaseDetailReportRepository purchaseDetailReportRepository;
    @Resource
    private PurchasesBackDetailReportRepository purchasesBackDetailReportRepository;
    @Resource
    private ProductCategoryService categoryService;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;

    /**
     * 无数据
     */
    public static final String NO_DATA_YET = "-";

    @Override
    public CommonResult<ProductSalesOverviewVO> queryProductSalesOverview(ProductSalesOverviewQueryDTO productSalesOverviewQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 类目层级
        productSalesOverviewQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());

        ProductSalesOverviewVO data = new ProductSalesOverviewVO();
        // 查询总计
        ProductSalesOverview productSalesOverview = productSalesOverviewMapper.querySummary(productSalesOverviewQueryDTO);
        if (Objects.isNull(productSalesOverview)) {
            productSalesOverview = new ProductSalesOverview(NumberConstants.ZERO, NumberConstants.ZERO, StringConstants.SEPARATING_IN_LINE, BigDecimal.ZERO, BigDecimal.ZERO, StringConstants.SEPARATING_IN_LINE, NumberConstants.ZERO, NumberConstants.ZERO, StringConstants.SEPARATING_IN_LINE, BigDecimal.ZERO, BigDecimal.ZERO, StringConstants.SEPARATING_IN_LINE, new ChartLine());
        }
        BeanUtils.copyProperties(productSalesOverview, data);

        //计算退款率和环比
        String lastRefundRate = calculateRefundRate(productSalesOverview.getLastRefundPrice(), productSalesOverview.getLastPaySuccessPrice());
        String refundRate = calculateRefundRate(productSalesOverview.getRefundPrice(), productSalesOverview.getPaySuccessPrice());
        data.setLastRefundRate(lastRefundRate);
        data.setRefundRate(refundRate);
        if (Objects.equals(lastRefundRate, NO_DATA_YET) || Objects.equals(refundRate, NO_DATA_YET)) {
            data.setRefundRateChain(NO_DATA_YET);
        } else {
            String refundRateChain = calculateChain(new BigDecimal(refundRate), new BigDecimal(lastRefundRate));
            data.setRefundRateChain(refundRateChain);
        }
        //计算环比
        calculateChain(data, productSalesOverview);

        String timeTag = productSalesOverviewQueryDTO.getTimeTag();

        // x轴存放时间
        List<String> timeTags = TimeUtils.getDatesBetweenTwoDate(timeTag, productSalesOverviewQueryDTO.getType(), TimeUtils.FORMAT_STRING);
        productSalesOverviewQueryDTO.setTimeTag(null);
        productSalesOverviewQueryDTO.setTimeTags(timeTags);
        productSalesOverviewQueryDTO.setType(TimeDimensionEnum.DAY.getType());
        List<ProductSalesOverview> dayDimensionOverviewList = productSalesOverviewMapper.batchQuerySummary(productSalesOverviewQueryDTO);
        Map<String, ProductSalesOverview> timeTagOverviewMap = dayDimensionOverviewList.stream().collect(Collectors.toMap(ProductSalesOverview::getTimeTag, item -> item));

        // y轴展示的数据
        assemblyDisplayData(data, timeTags, timeTagOverviewMap);
        return CommonResult.ok(data);
    }

    /**
     * 计算退款率
     * @param refundPrice
     * @param paySuccessPrice
     * @return
     */
    private String calculateRefundRate(BigDecimal refundPrice, BigDecimal paySuccessPrice) {
        if (Objects.isNull(paySuccessPrice) || paySuccessPrice.compareTo(BigDecimal.ZERO) == 0) {
            return NO_DATA_YET;
        }
        BigDecimal refundRate = NumberUtil.div(refundPrice, paySuccessPrice, NumberConstant.FOUR);
        BigDecimal refundRatePercent = refundRate.multiply(NumberConstants.ONE_HUNDRED);
        return String.format(Constants.DECIMAL_FORMAT, refundRatePercent);
    }

    private void assemblyDisplayData(ProductSalesOverviewVO data, List<String> timeTags, Map<String, ProductSalesOverview> timeTagOverviewMap) {
        List<String> paySuccessNumChartDataArray = new ArrayList<>();
        List<String> paySuccessPriceChartDataArray = new ArrayList<>();
        List<String> refundRateChartDataArray = new ArrayList<>();
        List<String> refundPriceChartDataArray = new ArrayList<>();
        for (int i = 0; i < timeTags.size(); i++) {
            String dayTimeTag = timeTags.get(i);
            ProductSalesOverview dayDimensionOverview = timeTagOverviewMap.get(dayTimeTag);
            if (Objects.isNull(dayDimensionOverview)) {
                paySuccessNumChartDataArray.add(StringConstants.ZERO);
                paySuccessPriceChartDataArray.add(StringConstants.ZERO);
                refundRateChartDataArray.add(StringConstants.ZERO);
                refundPriceChartDataArray.add(StringConstants.ZERO);
                continue;
            }
            Integer dayDimensionPaySuccessNum = dayDimensionOverview.getPaySuccessNum();
            paySuccessNumChartDataArray.add(Objects.isNull(dayDimensionPaySuccessNum) ? StringConstants.ZERO : dayDimensionPaySuccessNum.toString());

            BigDecimal dayDimensionPaySuccessPrice = dayDimensionOverview.getPaySuccessPrice();
            paySuccessPriceChartDataArray.add(Objects.isNull(dayDimensionPaySuccessPrice) ? StringConstants.ZERO : String.format(Constants.DECIMAL_FORMAT, dayDimensionPaySuccessPrice));

            BigDecimal dayDimensionRefundPrice = dayDimensionOverview.getRefundPrice();
            refundPriceChartDataArray.add(Objects.isNull(dayDimensionRefundPrice) ? StringConstants.ZERO : String.format(Constants.DECIMAL_FORMAT, dayDimensionRefundPrice));

            if (Objects.isNull(dayDimensionPaySuccessPrice) || Objects.isNull(dayDimensionRefundPrice) || dayDimensionPaySuccessPrice.compareTo(BigDecimal.ZERO) == 0) {
                refundRateChartDataArray.add(NO_DATA_YET);
            } else {
                BigDecimal refundRate = NumberUtil.div(dayDimensionRefundPrice, dayDimensionPaySuccessPrice, NumberConstants.FOUR);
                BigDecimal refundRatePercent = refundRate.multiply(NumberConstants.ONE_HUNDRED);
                refundRateChartDataArray.add(String.format(Constants.DECIMAL_FORMAT, refundRatePercent));
            }
        }
        // 折线图四条线数据
        ChartData paySuccessNumChartData = new ChartData();
        paySuccessNumChartData.setName(ChartNameConstant.PAY_SUCCESS_NUM_NAME);
        paySuccessNumChartData.setData(paySuccessNumChartDataArray);

        ChartData paySuccessPriceChartData = new ChartData();
        paySuccessPriceChartData.setName(ChartNameConstant.PAY_SUCCESS_PRICE_NAME);
        paySuccessPriceChartData.setData(paySuccessPriceChartDataArray);

        ChartData refundNumChartData = new ChartData();
        refundNumChartData.setName(ChartNameConstant.REFUND_RATE);
        refundNumChartData.setData(refundRateChartDataArray);

        ChartData refundPriceChartData = new ChartData();
        refundPriceChartData.setName(ChartNameConstant.REFUND_PRICE);
        refundPriceChartData.setData(refundPriceChartDataArray);

        List<ChartData> series = Arrays.asList(paySuccessNumChartData, paySuccessPriceChartData, refundNumChartData, refundPriceChartData);

        ChartLine chartLine = new ChartLine();
        chartLine.setXAxis(timeTags);
        chartLine.setSeries(series);
        data.setChartLine(chartLine);
    }

    private void calculateChain(ProductSalesOverviewVO data, ProductSalesOverview productSalesOverview) {
        Integer paySuccessNum = productSalesOverview.getPaySuccessNum();
        Integer lastSuccessNum = productSalesOverview.getLastSuccessNum();
        String paySuccessNumChain = calculateChain(BigDecimal.valueOf(paySuccessNum), BigDecimal.valueOf(lastSuccessNum));
        data.setPaySuccessNumChain(paySuccessNumChain);

        BigDecimal paySuccessPrice = productSalesOverview.getPaySuccessPrice();
        BigDecimal lastPaySuccessPrice = productSalesOverview.getLastPaySuccessPrice();
        String paySuccessPriceChain = calculateChain(paySuccessPrice, lastPaySuccessPrice);
        data.setPaySuccessPriceChain(paySuccessPriceChain);

        BigDecimal refundPrice = productSalesOverview.getRefundPrice();
        BigDecimal lastRefundPrice = productSalesOverview.getLastRefundPrice();
        String refundPriceChain = calculateChain(refundPrice, lastRefundPrice);
        data.setRefundPriceChain(refundPriceChain);
    }

    @Override
    public CommonResult<ProductMovementVO> queryProductMovement(ProductMovementQueryDTO productMovementQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        productMovementQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        String timeTag = productMovementQueryDTO.getTimeTag();
        ProductMovement productMovement = productMovementMapper.querySummary(productMovementQueryDTO);
        ProductMovementVO data = new ProductMovementVO();
        if (Objects.isNull(productMovement)) {
            productMovement = new ProductMovement(NumberConstants.ZERO, NumberConstants.ZERO, StringConstants.SEPARATING_IN_LINE, NumberConstants.ZERO, NumberConstants.ZERO, StringConstants.SEPARATING_IN_LINE, BigDecimal.ZERO, BigDecimal.ZERO, StringConstants.SEPARATING_IN_LINE);
        }
        BeanUtils.copyProperties(productMovement, data);
        // 计算环比
        calculateProductMovementChain(data);

        // x轴存放时间
        List<String> timeTags = TimeUtils.getDatesBetweenTwoDate(timeTag, productMovementQueryDTO.getType(), TimeUtils.FORMAT_STRING);

        productMovementQueryDTO.setTimeTag(null);
        productMovementQueryDTO.setTimeTags(timeTags);
        productMovementQueryDTO.setType(TimeDimensionEnum.DAY.getType());
        // 天维度
        List<ProductMovement> productMovements = productMovementMapper.batchQuerySummary(productMovementQueryDTO);
        Map<String, ProductMovement> timeTagMovementMap = productMovements.stream().collect(Collectors.toMap(ProductMovement::getTimeTag, item -> item));
        List<String> onSaleNumChartDataArray = new ArrayList<>();
        List<String> paySuccessNumChartDataArray = new ArrayList<>();
        List<String> saleRateDataArray = new ArrayList<>();

        for (String tag : timeTags) {
            ProductMovement dayDimensionMovement = timeTagMovementMap.get(tag);
            if (Objects.isNull(dayDimensionMovement)) {
                onSaleNumChartDataArray.add(StringConstants.ZERO);
                paySuccessNumChartDataArray.add(StringConstants.ZERO);
                saleRateDataArray.add(StringConstants.ZERO);
                continue;
            }
            Integer onSaleNum = dayDimensionMovement.getOnSaleNum();
            onSaleNumChartDataArray.add(onSaleNum.toString());

            Integer paySuccessNum = dayDimensionMovement.getPaySuccessNum();
            paySuccessNumChartDataArray.add(paySuccessNum.toString());

            BigDecimal saleRate = dayDimensionMovement.getSaleRate();
            saleRateDataArray.add(String.format(Constants.DECIMAL_FORMAT, saleRate));
        }

        ChartData onSaleNumChartData = new ChartData();
        onSaleNumChartData.setName(ChartNameConstant.ON_SALE_NUM);
        onSaleNumChartData.setData(onSaleNumChartDataArray);

        ChartData paySuccessNumChartData = new ChartData();
        paySuccessNumChartData.setName(ChartNameConstant.PAY_SUCCESS_NUM_NAME);
        paySuccessNumChartData.setData(paySuccessNumChartDataArray);

        ChartData saleRateChartData = new ChartData();
        saleRateChartData.setName(ChartNameConstant.SALE_RATE);
        saleRateChartData.setData(saleRateDataArray);

        List<ChartData> series = Arrays.asList(onSaleNumChartData, paySuccessNumChartData, saleRateChartData);

        ChartLine chartLine = new ChartLine();
        chartLine.setXAxis(timeTags);
        chartLine.setSeries(series);
        data.setChartLine(chartLine);
        return CommonResult.ok(data);
    }

    private void calculateProductMovementChain(ProductMovementVO data) {
        // 在售商品数环比
        data.setOnSaleChain(calculateChain(BigDecimal.valueOf(data.getOnSaleNum()), BigDecimal.valueOf(data.getLastOnSaleNum())));
        // 支付成功商品数
        data.setPaySuccessChain(calculateChain(BigDecimal.valueOf(data.getPaySuccessNum()), BigDecimal.valueOf(data.getLastPaySuccessNum())));
        // 商品动销率
        BigDecimal saleRate = data.getOnSaleNum().compareTo(NumberConstants.ZERO) == NumberConstants.ZERO ? BigDecimal.ZERO : NumberUtil.div(data.getPaySuccessNum(), data.getOnSaleNum()).setScale(NumberConstants.TWO, BigDecimal.ROUND_HALF_EVEN);
        data.setSaleRate(saleRate.multiply(NumberConstants.ONE_HUNDRED));
        // 上个周期商品动销率
        BigDecimal lastSaleRate = data.getLastOnSaleNum().compareTo(NumberConstants.ZERO) == NumberConstants.ZERO ? BigDecimal.ZERO : NumberUtil.div(data.getLastPaySuccessNum(), data.getLastOnSaleNum()).setScale(NumberConstants.TWO, BigDecimal.ROUND_HALF_EVEN);
        data.setLastSaleRate(lastSaleRate.multiply(NumberConstants.ONE_HUNDRED));
        // 动销率环比
        BigDecimal saleRateChain = lastSaleRate.compareTo(BigDecimal.ZERO) == NumberConstants.ZERO ? BigDecimal.ZERO : NumberUtil.div(saleRate, lastSaleRate).setScale(NumberConstants.TWO, BigDecimal.ROUND_HALF_EVEN);
        data.setSaleRateChain(saleRateChain.multiply(NumberConstants.ONE_HUNDRED).toString());
    }

    @Override
    public CommonResult<MerchantStorePurchaseVO> queryMerchantStorePurchase(MerchantStorePurchaseQueryDTO merchantStorePurchaseQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        merchantStorePurchaseQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        String timeTag = merchantStorePurchaseQueryDTO.getTimeTag();
        MerchantStorePurchase merchantStorePurchase = merchantStorePurchaseMapper.querySummary(merchantStorePurchaseQueryDTO);
        if (Objects.isNull(merchantStorePurchase)) {
            Integer zero = NumberConstants.ZERO;
            String doubleLine = StringConstants.SEPARATING_IN_LINE;
            merchantStorePurchase = new MerchantStorePurchase(zero, zero, zero, zero, zero, zero, doubleLine, zero, zero, doubleLine, zero, zero, doubleLine, zero, zero, doubleLine);
        }
        MerchantStorePurchaseVO data = new MerchantStorePurchaseVO();
        BeanUtils.copyProperties(merchantStorePurchase, data);

        // x轴存放时间
        List<String> timeTags = TimeUtils.getDatesBetweenTwoDate(timeTag, merchantStorePurchaseQueryDTO.getType(), TimeUtils.FORMAT_STRING);

        merchantStorePurchaseQueryDTO.setTimeTag(null);
        merchantStorePurchaseQueryDTO.setTimeTags(timeTags);
        merchantStorePurchaseQueryDTO.setType(TimeDimensionEnum.DAY.getType());
        // 天维度
        List<MerchantStorePurchase> merchantStorePurchases = merchantStorePurchaseMapper.batchQuerySummary(merchantStorePurchaseQueryDTO);
        Map<String, MerchantStorePurchase> timeTagsPurchaseMap = merchantStorePurchases.stream().collect(Collectors.toMap(MerchantStorePurchase::getTimeTag, item -> item));
        List<String> storeInOperationNumChartDataArray = new ArrayList<>(NumberConstants.TEN);
        List<String> directStoreInOperationNumChartDataArray = new ArrayList<>();
        List<String> joinStoreInOperationNumDataArray = new ArrayList<>();
        List<String> managedStoreInOperationNumDataArray = new ArrayList<>();

        for (String tag : timeTags) {
            MerchantStorePurchase dayDimensionPurchase = timeTagsPurchaseMap.get(tag);
            if (Objects.isNull(dayDimensionPurchase)) {
                storeInOperationNumChartDataArray.add(StringConstants.ZERO);
                directStoreInOperationNumChartDataArray.add(StringConstants.ZERO);
                joinStoreInOperationNumDataArray.add(StringConstants.ZERO);
                managedStoreInOperationNumDataArray.add(StringConstants.ZERO);
                continue;
            }
            Integer storeInOperationNum = dayDimensionPurchase.getStoreInOperationNum();
            storeInOperationNumChartDataArray.add(storeInOperationNum.toString());

            Integer directStoreInOperationNum = dayDimensionPurchase.getDirectStoreInOperationNum();
            directStoreInOperationNumChartDataArray.add(directStoreInOperationNum.toString());

            Integer joinStoreInOperationNum = dayDimensionPurchase.getJoinStoreInOperationNum();
            joinStoreInOperationNumDataArray.add(joinStoreInOperationNum.toString());

            Integer managedStoreInOperationNum = dayDimensionPurchase.getManagedStoreInOperationNum();
            managedStoreInOperationNumDataArray.add(managedStoreInOperationNum.toString());
        }

        ChartData storeInOperationNumChartData = new ChartData();
        storeInOperationNumChartData.setName(ChartNameConstant.STORE_IN_OPERATION_NUM);
        storeInOperationNumChartData.setData(storeInOperationNumChartDataArray);

        ChartData directStoreInOperationNumChartData = new ChartData();
        directStoreInOperationNumChartData.setName(ChartNameConstant.DIRECT_STORE_IN_OPERATION_NUM);
        directStoreInOperationNumChartData.setData(directStoreInOperationNumChartDataArray);

        ChartData joinStoreInOperationNumChartData = new ChartData();
        joinStoreInOperationNumChartData.setName(ChartNameConstant.JOIN_STORE_IN_OPERATION_NUM);
        joinStoreInOperationNumChartData.setData(joinStoreInOperationNumDataArray);

        ChartData managedStoreInOperationNumChartData = new ChartData();
        managedStoreInOperationNumChartData.setName(ChartNameConstant.MANAGED_STORE_IN_OPERATION_NUM);
        managedStoreInOperationNumChartData.setData(managedStoreInOperationNumDataArray);

        List<ChartData> series = Arrays.asList(storeInOperationNumChartData, directStoreInOperationNumChartData, joinStoreInOperationNumChartData, managedStoreInOperationNumChartData);
        ChartLine chartLine = new ChartLine();
        chartLine.setSeries(series);
        chartLine.setXAxis(timeTags);
        data.setChartLine(chartLine);
        return CommonResult.ok(data);
    }

    @Override
    public CommonResult exportReportExcel(ReportQueryDTO reportQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.isNull(reportQueryDTO.getStartTime()) || Objects.isNull(reportQueryDTO.getEndTime())) {
            throw new DefaultServiceException("请填写查询时间的范围");
        }
        reportQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());

        // 生成文件下载记录
        Map<String, String> paramsMap = new HashMap<>(NumberConstants.ONE);
        String startTime = TimeUtils.format(reportQueryDTO.getStartTime(), TimeUtils.FORMAT_STRING, TimeUtils.FORMAT_DATE);
        String endTime = TimeUtils.format(reportQueryDTO.getEndTime(), TimeUtils.FORMAT_STRING, TimeUtils.FORMAT_DATE);
        paramsMap.put(Constants.QUERY_TIME, startTime + StringConstants.SEPARATING_IN_LINE + endTime);
        List<ExportParamDTO> params = JSONArray.parseObject(reportQueryDTO.getExportParam(), new TypeReference<List<ExportParamDTO>>() {});
        if (!CollectionUtils.isEmpty(params)){
            params.forEach(i -> {
                if (Objects.nonNull(i.getLabel())){
                    paramsMap.put(i.getLabel(),i.getValue());
                }
            });
        }

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(reportQueryDTO.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        if (Objects.equals(reportQueryDTO.getType(), FileDownloadTypeEnum.PRODUCT_DETAIL_SALES.getType())) {
            recordDTO.setFileName(ExcelTypeEnum.PRODUCT_DETAIL_SALE.getFileName());
        } else if (Objects.equals(reportQueryDTO.getType(), FileDownloadTypeEnum.MERCHANT_STORE_DETAIL_PURCHASE.getType())) {
            recordDTO.setFileName(ExcelTypeEnum.MERCHANT_STORE_DETAIL_PURCHASE.getFileName());
        }
        recordDTO.setParams(JSON.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(reportQueryDTO, e -> writeDownloadCenter(e, recordDTO.getFileName()));

//        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
//        fileDownloadRecord.setParams(JSON.toJSONString(paramsMap));
//        fileDownloadRecord.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        fileDownloadRecord.setType(reportQueryDTO.getType());
//        fileDownloadRecord.setTenantId(loginContextInfoDTO.getTenantId());
//        fileDownloadRecordService.generateFileDownloadRecord(fileDownloadRecord);
//
//        // 异步导出
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
//                if (Objects.equals(reportQueryDTO.getType(), FileDownloadTypeEnum.PRODUCT_DETAIL_SALES.getType())) {
//                    ProductDetailSalesQueryDTO productDetailSalesQueryDTO = new ProductDetailSalesQueryDTO();
//                    BeanUtils.copyProperties(reportQueryDTO, productDetailSalesQueryDTO);
//                    generateProductDetailSalesExcel(productDetailSalesQueryDTO, fileDownloadRecord.getId());
//                } else if (Objects.equals(reportQueryDTO.getType(), FileDownloadTypeEnum.MERCHANT_STORE_DETAIL_PURCHASE.getType())) {
//                    MerchantStoreDetailPurchaseQueryDTO merchantStoreDetailPurchaseQueryDTO = new MerchantStoreDetailPurchaseQueryDTO();
//                    BeanUtils.copyProperties(reportQueryDTO, merchantStoreDetailPurchaseQueryDTO);
//                    generateMerchantStoreDetailPurchaseExcel(merchantStoreDetailPurchaseQueryDTO, fileDownloadRecord.getId());
//                }
//            } catch (Exception e) {
//                fileDownloadRecordService.updateFailStatus(fileDownloadRecord.getId());
//                log.error("导出报表失败", e);
//            }
//        });
        return CommonResult.ok();
    }


    public DownloadCenterOssRespDTO writeDownloadCenter(ReportQueryDTO reportQueryDTO, String fileName) {
        // 1、表格处理
        String filePath = null;
        if (Objects.equals(reportQueryDTO.getType(), FileDownloadTypeEnum.PRODUCT_DETAIL_SALES.getType())) {
            ProductDetailSalesQueryDTO productDetailSalesQueryDTO = new ProductDetailSalesQueryDTO();
            BeanUtils.copyProperties(reportQueryDTO, productDetailSalesQueryDTO);
            filePath = generateProductDetailSalesExcelStream(productDetailSalesQueryDTO, ExcelTypeEnum.PRODUCT_DETAIL_SALE);
        } else if (Objects.equals(reportQueryDTO.getType(), FileDownloadTypeEnum.MERCHANT_STORE_DETAIL_PURCHASE.getType())) {
            MerchantStoreDetailPurchaseQueryDTO merchantStoreDetailPurchaseQueryDTO = new MerchantStoreDetailPurchaseQueryDTO();
            BeanUtils.copyProperties(reportQueryDTO, merchantStoreDetailPurchaseQueryDTO);
            filePath = generateMerchantStoreDetailPurchaseExcelStream(merchantStoreDetailPurchaseQueryDTO, ExcelTypeEnum.MERCHANT_STORE_DETAIL_PURCHASE);
        }

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }


    @Override
    public void generateProductDetailSalesExcel(ProductDetailSalesQueryDTO productDetailSalesQueryDTO, Long fileDownloadRecordId) {
        log.info("文件id:{}, 开始生成商品销售数据excel", fileDownloadRecordId);
//        List<ProductDetailSalesDTO> productDetailSales = productDetailSalesMapper.queryAll(productDetailSalesQueryDTO);
//        Boolean isSuccess = commonService.generateAndUploadExcel(productDetailSales, ExcelTypeEnum.PRODUCT_DETAIL_SALE, fileDownloadRecordId);

//        Boolean isSuccess = generateProductDetailSalesExcelStream(productDetailSalesQueryDTO, fileDownloadRecordId, ExcelTypeEnum.PRODUCT_DETAIL_SALE);
//        log.info("文件id:{}, 生成商品销售数据excel结果:{}", fileDownloadRecordId, isSuccess);
    }

    private String generateProductDetailSalesExcelStream(ProductDetailSalesQueryDTO productDetailSalesQueryDTO, ExcelTypeEnum excelTypeEnum){
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), excelTypeEnum.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();

        LargeDataSetExporter<ProductDetailSalesDTO, ProductDetailSalesDTO> handler = new LargeDataSetExporter<ProductDetailSalesDTO, ProductDetailSalesDTO>(){
            @Override
            protected List<ProductDetailSalesDTO> convert(ProductDetailSalesDTO productDetailSalesDTO) {
                return Lists.newArrayList(productDetailSalesDTO);
            }

            @Override
            protected void flushData(List<ProductDetailSalesDTO> dataList) {
                excelWriter.fill(dataList, fillConfig, writeSheet);
                log.info("导出数据 size=" + dataList.size());
            }
        };
        productDetailSalesMapper.queryAll(productDetailSalesQueryDTO, handler);
        handler.clearData();
        excelWriter.finish();

        return filePath;
        // 上传数据到七牛云
//        return commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, excelTypeEnum, fileDownloadRecordId);
    }

    @Override
    public CommonResult<PageInfo<DamageDetailReportVO>> queryDamageDetail(ReportCommonQueryDTO reportCommonQueryDTO) {
        convertToThirdCategoryIds(reportCommonQueryDTO);
        Page<DamageDetailReport> page = damageDetailReportRepository.queryDamageDetailPage(reportCommonQueryDTO);
        PageInfo<DamageDetailReportVO> pageInfo = PageInfoConverter.toPageInfo(page, (DamageDetailReport p) -> {
            DamageDetailReportVO vo = new DamageDetailReportVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        });
        return CommonResult.ok(pageInfo);
    }

    /**
     * 获取相关联所有类目
     * @param reportCommonQueryDTO
     */
    private void convertToThirdCategoryIds(ReportCommonQueryDTO reportCommonQueryDTO) {
        if (!CollectionUtils.isEmpty(reportCommonQueryDTO.getCategoryIds())) {
            if (grayReleaseConfig.executeProductCenterGray(reportCommonQueryDTO.getTenantId())) {
                List<Long> categoryIds = categoryService.queryChildCategoryIds (reportCommonQueryDTO.getCategoryIds ().get (0));
                reportCommonQueryDTO.setCategoryIds (categoryIds);
            }else {
                // 查询类目集合
                List<CategoryVO> categoryVoS = categoryService.queryChildCategoryList (reportCommonQueryDTO.getCategoryIds ().get (0));
                List<Long> categoryIds = categoryVoS.stream ().map (CategoryVO::getId).collect (Collectors.toList ());
                reportCommonQueryDTO.setCategoryIds (categoryIds);
            }
        }
    }
    @Override
    public CommonResult exportDamageDetailReport(ReportCommonQueryDTO reportCommonQueryDTO) {
        convertToThirdCategoryIds(reportCommonQueryDTO);
        ExcelLargeDataSetExporter<DamageDetailReport, DamageDetailReport> handler = new ExcelLargeDataSetExporter<DamageDetailReport, DamageDetailReport>(ExcelTypeEnum.DAMAGE_DETAIL_REPORT.getName()) {
            @Override
            protected List<DamageDetailReport> convert(DamageDetailReport data) {
                return Lists.newArrayList(data);
            }
        };

        exportPurchaseReport(reportCommonQueryDTO, ExcelTypeEnum.DAMAGE_DETAIL_REPORT, () -> {
            damageDetailReportRepository.listDamageDetailForExport(reportCommonQueryDTO, handler);
            String filePath = handler.finish(true);
            return filePath;
        });
        return CommonResult.ok();
    }

    @Override
    public CommonResult<PageInfo<DamageSaleRatioDetailReportVO>> queryDamageSaleRatioDetail(ReportCommonQueryDTO reportCommonQueryDTO) {
        convertToThirdCategoryIds(reportCommonQueryDTO);
        Page<DamageSaleRatioReport> page = damageSaleRatioReportRepository.queryDamageSaleRatioDetailPage(reportCommonQueryDTO);
        PageInfo<DamageSaleRatioDetailReportVO> pageInfo = PageInfoConverter.toPageInfo(page, (DamageSaleRatioReport p) -> {
            DamageSaleRatioDetailReportVO vo = new DamageSaleRatioDetailReportVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        });
        return CommonResult.ok(pageInfo);
    }

    @Override
    public CommonResult exportDamageSaleRatioDetailReport(ReportCommonQueryDTO reportCommonQueryDTO) {
        convertToThirdCategoryIds(reportCommonQueryDTO);
        ExcelLargeDataSetExporter<DamageSaleRatioReport, DamageSaleRatioReport> handler = new ExcelLargeDataSetExporter<DamageSaleRatioReport, DamageSaleRatioReport>(ExcelTypeEnum.DAMAGE_SALE_RATIO_REPORT.getName()){

            @Override
            protected List<DamageSaleRatioReport> convert(DamageSaleRatioReport data) {
                return Lists.newArrayList(data);
            }
        };
        exportPurchaseReport(reportCommonQueryDTO, ExcelTypeEnum.DAMAGE_SALE_RATIO_REPORT, () -> {
            damageSaleRatioReportRepository.listDamageSaleRatioReportForExport(reportCommonQueryDTO, handler);
            String filePath = handler.finish(true);
            return filePath;
        });
        return CommonResult.ok();
    }

    private void exportPurchaseReport(ReportCommonQueryDTO reportCommonQueryDTO, ExcelTypeEnum excelTypeEnum, Supplier<String> supplier) {
        Map<String, String> paramsMap = new HashMap<>();
        if (!StringUtils.isBlank(reportCommonQueryDTO.getStartTime()) && !StringUtils.isBlank(reportCommonQueryDTO.getEndTime())) {
            String startTime = TimeUtils.format(reportCommonQueryDTO.getStartTime(), TimeUtils.FORMAT, TimeUtils.FORMAT_DATE);
            String endTime = TimeUtils.format(reportCommonQueryDTO.getEndTime(), TimeUtils.FORMAT, TimeUtils.FORMAT_DATE);
            paramsMap.put(Constants.QUERY_TIME, startTime + StringConstants.SEPARATING_IN_LINE + endTime);
        }
        if (Objects.nonNull(reportCommonQueryDTO.getWarehouseId()) && !StringUtils.isBlank(reportCommonQueryDTO.getWarehouseName())) {
            paramsMap.put(Constants.WAREHOUSE, reportCommonQueryDTO.getWarehouseName());
        }
        if (Objects.nonNull(reportCommonQueryDTO.getWarehouseId()) && !StringUtils.isBlank(reportCommonQueryDTO.getWarehouseName())) {
            paramsMap.put(Constants.WAREHOUSE, reportCommonQueryDTO.getWarehouseName());
        }
        if (Objects.nonNull(reportCommonQueryDTO.getSkuId())) {
            paramsMap.put(Constants.SKU_ID, reportCommonQueryDTO.getSkuId().toString());
        }
        if (!StringUtils.isBlank(reportCommonQueryDTO.getName())) {
            paramsMap.put(Constants.GOODS_NAME, reportCommonQueryDTO.getName());
        }
        if (!StringUtils.isBlank(reportCommonQueryDTO.getCategoryName())) {
            paramsMap.put(Constants.CATEGORY, reportCommonQueryDTO.getCategoryName());
        }

        FileDownloadTypeEnum downloadType;
        switch (excelTypeEnum) {
            case PURCHASE_DETAIL_REPORT:
                downloadType = FileDownloadTypeEnum.PURCHASE_DETAIL_REPORT;break;
            case PURCHASE_BACK_DETAIL_REPORT:
                downloadType = FileDownloadTypeEnum.PURCHASE_BACK_DETAIL_REPORT;break;
            case DAMAGE_DETAIL_REPORT:
                downloadType = FileDownloadTypeEnum.DAMAGE_DETAIL_REPORT;break;
            case DAMAGE_SALE_RATIO_REPORT:
                downloadType = FileDownloadTypeEnum.DAMAGE_SALE_RATIO_REPORT;break;
            default:
                downloadType = FileDownloadTypeEnum.PURCHASE_DETAIL_REPORT;break;
        }

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(downloadType.getType());
        recordDTO.setTenantId(reportCommonQueryDTO.getTenantId());
        recordDTO.setFileName(excelTypeEnum.getFileName());
        recordDTO.setParams(paramsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);

        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(reportCommonQueryDTO, ee -> {
            // 1、表格处理
            String filePath = supplier.get();

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


//        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
//        fileDownloadRecord.setParams(JSON.toJSONString(paramsMap));
//        fileDownloadRecord.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        FileDownloadTypeEnum downloadType;
//        switch (excelTypeEnum) {
//            case PURCHASE_DETAIL_REPORT:
//                downloadType = FileDownloadTypeEnum.PURCHASE_DETAIL_REPORT;break;
//            case PURCHASE_BACK_DETAIL_REPORT:
//                downloadType = FileDownloadTypeEnum.PURCHASE_BACK_DETAIL_REPORT;break;
//            case DAMAGE_DETAIL_REPORT:
//                downloadType = FileDownloadTypeEnum.DAMAGE_DETAIL_REPORT;break;
//            case DAMAGE_SALE_RATIO_REPORT:
//                downloadType = FileDownloadTypeEnum.DAMAGE_SALE_RATIO_REPORT;break;
//            default:
//                downloadType = FileDownloadTypeEnum.PURCHASE_DETAIL_REPORT;break;
//        }
//        fileDownloadRecord.setType(downloadType.getType());
//        fileDownloadRecord.setTenantId(reportCommonQueryDTO.getTenantId());
//        fileDownloadRecordService.generateFileDownloadRecord(fileDownloadRecord);
//
//        // 异步导出
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
////            Boolean isSuccess = commonService.generateAndUploadExcel(data, excelTypeEnum, fileDownloadRecord.getId());
//                consumer.accept(fileDownloadRecord.getId());
//            } catch (Exception e) {
//                fileDownloadRecordService.updateFailStatus(fileDownloadRecord.getId());
//                log.error("导出报表失败", e);
//            }
//        });
    }


    private void generateMerchantStoreDetailPurchaseExcel(MerchantStoreDetailPurchaseQueryDTO merchantStoreDetailPurchaseQueryDTO, Long fileDownloadRecordId) {
        log.info("文件id:{}, 开始生成门店采购数据excel", fileDownloadRecordId);
//        Long tenantId = merchantStoreDetailPurchaseQueryDTO.getTenantId();
//        List<MerchantStoreDetailPurchaseDTO> merchantStoreDetailPurchases = merchantStoreDetailPurchaseMapper.queryAll(merchantStoreDetailPurchaseQueryDTO);
//        Boolean isSuccess = commonService.generateAndUploadExcel(merchantStoreDetailPurchases, ExcelTypeEnum.MERCHANT_STORE_DETAIL_PURCHASE, fileDownloadRecordId);

//        Boolean isSuccess = generateMerchantStoreDetailPurchaseExcelStream(merchantStoreDetailPurchaseQueryDTO, fileDownloadRecordId, ExcelTypeEnum.MERCHANT_STORE_DETAIL_PURCHASE);
//        log.info("文件id:{}, 生成门店采购数据excel结果:{}", fileDownloadRecordId, isSuccess);
    }

    private String generateMerchantStoreDetailPurchaseExcelStream(MerchantStoreDetailPurchaseQueryDTO merchantStoreDetailPurchaseQueryDTO, ExcelTypeEnum excelTypeEnum) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), excelTypeEnum.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();

        LargeDataSetExporter<MerchantStoreDetailPurchaseDTO, MerchantStoreDetailPurchaseDTO> handler = new LargeDataSetExporter<MerchantStoreDetailPurchaseDTO, MerchantStoreDetailPurchaseDTO>(){
            @Override
            protected List<MerchantStoreDetailPurchaseDTO> convert(MerchantStoreDetailPurchaseDTO merchantStoreDetailPurchaseDTO) {
                return Lists.newArrayList(merchantStoreDetailPurchaseDTO);
            }

            @Override
            protected void flushData(List<MerchantStoreDetailPurchaseDTO> dataList) {
                excelWriter.fill(dataList, fillConfig, writeSheet);
                log.info("导出数据 size=" + dataList.size());
            }
        };
        merchantStoreDetailPurchaseMapper.queryAll(merchantStoreDetailPurchaseQueryDTO, handler);
        handler.clearData();
        excelWriter.finish();

        return filePath;
        // 上传数据到七牛云
//        return commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, excelTypeEnum, fileDownloadRecordId);
    }

    @Override
    public CommonResult<PurchaseDetailAggVO> queryPurchaseDetailAgg(ReportCommonQueryDTO reportCommonQueryDTO) {
        convertToThirdCategoryIds(reportCommonQueryDTO);
        PurchaseDetailAggVO purchaseDetailAggVO = purchaseDetailReportRepository.getPurchaseDetailAgg(reportCommonQueryDTO);
        PurchaseDetailAggVO purchaseBackDetailAgg = purchasesBackDetailReportRepository.getPurchaseBackDetailAgg(reportCommonQueryDTO);
        purchaseDetailAggVO.setBackQuantity(purchaseBackDetailAgg.getBackQuantity());
        purchaseDetailAggVO.setBackAmount(purchaseBackDetailAgg.getBackAmount());
        purchaseDetailAggVO.setInboundAmount(purchaseBackDetailAgg.getInboundAmount());
        purchaseDetailAggVO.setInboundQuantity(purchaseBackDetailAgg.getInboundQuantity());
        purchaseDetailAggVO.setUnboundAmount(purchaseBackDetailAgg.getUnboundAmount());
        purchaseDetailAggVO.setUnboundQuantity(purchaseBackDetailAgg.getUnboundQuantity());
        PurchaseDetailAggVO damageSaleRatioDetailAgg = damageSaleRatioReportRepository.getDamageSaleRatioDetailAgg(reportCommonQueryDTO);
        purchaseDetailAggVO.setDamageAmount(damageSaleRatioDetailAgg.getDamageAmount());
        purchaseDetailAggVO.setDamageQuantity(damageSaleRatioDetailAgg.getDamageQuantity());
        purchaseDetailAggVO.setDamageBackAmount(damageSaleRatioDetailAgg.getDamageBackAmount());
        purchaseDetailAggVO.setDamageBackQuantity(damageSaleRatioDetailAgg.getDamageBackQuantity());
        purchaseDetailAggVO.setOutboundQuantity(damageSaleRatioDetailAgg.getOutboundQuantity());
        purchaseDetailAggVO.setOutboundAmount(damageSaleRatioDetailAgg.getOutboundAmount());
        purchaseDetailAggVO.setDamageSaleRatio(damageSaleRatioDetailAgg.getDamageSaleRatio());
        return CommonResult.ok(purchaseDetailAggVO);
    }

    /**
     * 计算环比  环比公式 = (本期 - 上期) / 上期
     *
     * @param data
     * @param lastData
     * @return
     */
    private String calculateChain(BigDecimal data, BigDecimal lastData) {
        if (Objects.isNull(lastData) || lastData.compareTo(BigDecimal.ZERO) == 0) {
            return NO_DATA_YET;
        }
        BigDecimal decimalChain = NumberUtil.sub(data, lastData).divide(lastData, NumberConstants.FOUR, BigDecimal.ROUND_HALF_UP);
        BigDecimal chain = decimalChain.multiply(NumberConstants.ONE_HUNDRED);
        return String.format(Constants.DECIMAL_FORMAT, chain);
    }

    @Override
    public CommonResult<PageInfo<PurchaseDetailReportVO>> queryPurchaseDetailReport(ReportCommonQueryDTO queryDTO) {
        convertToThirdCategoryIds(queryDTO);
        Page<PurchaseDetailReport> entities = purchaseDetailReportRepository.queryPurchaseDetailPage(queryDTO);
        PageInfo<PurchaseDetailReportVO> pageInfo = PageInfoConverter.toPageInfo(entities, (PurchaseDetailReport p) -> {
            PurchaseDetailReportVO vo = new PurchaseDetailReportVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        });
        return CommonResult.ok(pageInfo);
    }

    @Override
    public CommonResult exportPurchaseDetailReport(ReportCommonQueryDTO reportCommonQueryDTO) {
        convertToThirdCategoryIds(reportCommonQueryDTO);
        ExcelLargeDataSetExporter<PurchaseDetailReport, PurchaseDetailReport> handler = new ExcelLargeDataSetExporter<PurchaseDetailReport, PurchaseDetailReport>(ExcelTypeEnum.PURCHASE_DETAIL_REPORT.getName()){

            @Override
            protected List<PurchaseDetailReport> convert(PurchaseDetailReport data) {
                return Lists.newArrayList(data);
            }
        };
        exportPurchaseReport(reportCommonQueryDTO, ExcelTypeEnum.PURCHASE_DETAIL_REPORT, () -> {
            purchaseDetailReportRepository.listPurchaseDetailForExport(reportCommonQueryDTO, handler);
            String filePath = handler.finish(true);
            return filePath;
        });

        return CommonResult.ok();
    }

    @Override
    public CommonResult<PageInfo<PurchasesBackDetailReportVO>> queryPurchaseBackDetailReport(ReportCommonQueryDTO queryDTO) {
        convertToThirdCategoryIds(queryDTO);
        Page<PurchasesBackDetailReport> entities = purchasesBackDetailReportRepository.queryPurchaseBackDetailPage(queryDTO);
        PageInfo<PurchasesBackDetailReportVO> pageInfo = PageInfoConverter.toPageInfo(entities, (PurchasesBackDetailReport p) -> {
            PurchasesBackDetailReportVO vo = new PurchasesBackDetailReportVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        });
        return CommonResult.ok(pageInfo);
    }

    @Override
    public CommonResult exportPurchaseBackDetailReport(ReportCommonQueryDTO reportCommonQueryDTO) {
        convertToThirdCategoryIds(reportCommonQueryDTO);
        ExcelLargeDataSetExporter<PurchasesBackDetailReport, PurchasesBackDetailReportDTO> handler = new ExcelLargeDataSetExporter<PurchasesBackDetailReport, PurchasesBackDetailReportDTO>(ExcelTypeEnum.PURCHASE_BACK_DETAIL_REPORT.getName()){

            @Override
            protected List<PurchasesBackDetailReportDTO> convert(PurchasesBackDetailReport data) {
                return ReportConverter.toPurchasesBackDetailReportDTOList(Lists.newArrayList(data));
            }
        };
        exportPurchaseReport(reportCommonQueryDTO, ExcelTypeEnum.PURCHASE_BACK_DETAIL_REPORT, () -> {
            purchasesBackDetailReportRepository.listPurchaseBackDetailForExport(reportCommonQueryDTO, handler);
            String filePath = handler.finish(true);
            return filePath;
        });
        return CommonResult.ok();
    }
}
