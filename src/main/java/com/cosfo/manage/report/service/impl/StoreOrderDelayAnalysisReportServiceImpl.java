package com.cosfo.manage.report.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.product.model.vo.StoreOrderDelayAnalysisVO;
import com.cosfo.manage.report.converter.MerchantStoreOrderHysteresisAnalysisConverter;
import com.cosfo.manage.report.model.dto.StoreOrderDelayAnalysisInput;
import com.cosfo.manage.report.model.po.MerchantStoreOrderHysteresisAnalysis;
import com.cosfo.manage.report.repository.MerchantStoreOrderHysteresisAnalysisRepository;
import com.cosfo.manage.report.service.StoreOrderDelayAnalysisReportService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: xiaowk
 * @time: 2023/11/15 下午3:59
 */
@Slf4j
@Service
public class StoreOrderDelayAnalysisReportServiceImpl implements StoreOrderDelayAnalysisReportService {

    @Resource
    private MerchantStoreOrderHysteresisAnalysisRepository merchantStoreOrderHysteresisAnalysisRepository;
    @Resource
    private CommonService commonService;

    @Override
    public PageInfo<StoreOrderDelayAnalysisVO> queryList(StoreOrderDelayAnalysisInput input) {
        Page<MerchantStoreOrderHysteresisAnalysis> page = merchantStoreOrderHysteresisAnalysisRepository.queryListByPage(input);
        PageInfo<StoreOrderDelayAnalysisVO> pageInfo = MerchantStoreOrderHysteresisAnalysisConverter.INSTANCE.fromPage(page);
        pageInfo.getList().forEach(e -> {
            if (e.getSpecification().contains(Constants.UNDERLINE)) {
                e.setSpecification(e.getSpecification().substring(e.getSpecification().indexOf(Constants.UNDERLINE) + 1));
            }
            e.setStoreTypeStr(MerchantStoreEnum.Type.getDesc(e.getStoreType()));
            e.setStoreStatusStr(MerchantStoreEnum.QueryStatus.getDesc(e.getStoreStatus()));
        });
        return pageInfo;
    }

    @Override
    public Long exportList(StoreOrderDelayAnalysisInput input) {
        Long tenantId = input.getTenantId();

        //生成对应的查询条件
        Map<String, Object> queryParamsMap = new LinkedHashMap<>(NumberConstants.FIVE);
        if (!StringUtils.isEmpty(input.getStoreName())) {
            queryParamsMap.put(Constants.STORE_NAME, input.getStoreName());
        }
        if (!StringUtils.isEmpty(input.getStoreCode())) {
            queryParamsMap.put(Constants.STORE_NO, input.getStoreCode());
        }
        if (input.getStoreType() != null) {
            String storeType = MerchantStoreEnum.Type.getDesc(input.getStoreType());
            queryParamsMap.put(Constants.STORE_TYPE, storeType);
        }
        if (input.getStoreStatus() != null) {
            queryParamsMap.put(Constants.STORE_STATUS, MerchantStoreEnum.QueryStatus.getDesc(input.getStoreStatus()));
        }
        if (!StringUtils.isEmpty(input.getTitle())) {
            queryParamsMap.put(Constants.TITLE, input.getTitle());
        }
        if (input.getItemId() != null) {
            queryParamsMap.put(Constants.ITEM_ID, input.getItemId());
        }
        if (input.getDelayDays() != null) {
            queryParamsMap.put(Constants.STORE_DELAY_DAYS, input.getDelayDays());
        }


        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd") + "门店滞叫分析导出" + ".xlsx";

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.MERCHANT_STORE_ORDER_HYSTERESIS_ANALYSIS.getType());
        recordDTO.setTenantId(tenantId);
        recordDTO.setFileName(fileName);
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSON.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        return DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(input, ee -> {
            // 1、表格处理
            String filePath = generateStoreOrderDelayAnalysisReport(input);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });
    }

    public String generateStoreOrderDelayAnalysisReport(StoreOrderDelayAnalysisInput input) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.MERCHANT_STORE_ORDER_HYSTERESIS_ANALYSIS.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        input.setPageSize(500);
        int pageIndex = 1;
        //查询数据
        PageInfo<StoreOrderDelayAnalysisVO> pageInfo;
        do {
            input.setPageIndex(pageIndex);
            pageInfo = queryList(input);
            excelWriter.fill(pageInfo.getList(), fillConfig, writeSheet);
            pageIndex++;
        } while (pageInfo.isHasNextPage());

        excelWriter.finish();
        return filePath;
    }
}
