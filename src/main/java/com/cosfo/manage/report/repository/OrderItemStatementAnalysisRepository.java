package com.cosfo.manage.report.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.report.model.dto.OrderItemStatementAnalysisInput;
import com.cosfo.manage.report.model.po.OrderItemStatementAnalysis;

/**
 * <p>
 * 订单明细对账分析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
public interface OrderItemStatementAnalysisRepository extends IService<OrderItemStatementAnalysis> {

    Page<OrderItemStatementAnalysis> queryListByPage(OrderItemStatementAnalysisInput input);

}
