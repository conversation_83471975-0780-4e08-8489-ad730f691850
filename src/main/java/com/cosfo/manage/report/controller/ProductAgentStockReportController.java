package com.cosfo.manage.report.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.product.model.vo.ProductStockWarnVO;
import com.cosfo.manage.report.model.dto.ProductAgentStockQueryDTO;
import com.cosfo.manage.report.service.ProductAgentStockReportService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Author: fansongsong
 * @Date: 2023-05-16
 * @Description: 货品-库存相关接口
 */
@RestController
@RequestMapping("/product/stock")
public class ProductAgentStockReportController extends BaseController {

    @Resource
    private ProductAgentStockReportService productAgentStockReportService;

    /**
     * 查询货品库存预警列表
     *
     * @return
     */
    @PostMapping("/query/product-warn-info")
    public CommonResult<PageInfo<ProductStockWarnVO>> queryProductWarnList(@RequestBody @Valid ProductAgentStockQueryDTO productAgentStockQueryDTO) {
        return CommonResult.ok(productAgentStockReportService.queryProductWarnList(productAgentStockQueryDTO, getMerchantInfoDTO()));
    }

    /**
     * 导出货品库存预警列表
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export/product-warn-info")
    public CommonResult exportProductWarnList(@RequestBody ProductAgentStockQueryDTO productAgentStockQueryDTO) {
        productAgentStockQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return productAgentStockReportService.exportProductWarnList(productAgentStockQueryDTO);
    }

    /**
     * 数据订正接口，订正某个报价单所有的城市库存信息
     *
     * @return
     */
    @PostMapping("correction/sku")
    public CommonResult correction(Long skuId) {
        productAgentStockReportService.correction(skuId);
        return CommonResult.ok();
    }
}
