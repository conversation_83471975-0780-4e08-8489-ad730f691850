package com.cosfo.manage.report.converter;

import cn.hutool.core.util.NumberUtil;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.context.GoodsTypeEnum;
import com.cosfo.manage.common.context.PricingTypeEnum;
import com.cosfo.manage.common.context.ReportGoodsTypeEnum;
import com.cosfo.manage.common.context.StoreTypeEnum;
import com.cosfo.ordercenter.client.common.PayTypeEnum;
import com.cosfo.manage.common.util.PageUtils;
import com.cosfo.manage.market.service.MarketService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreLinkedVO;
import com.cosfo.manage.report.model.dto.*;
import com.cosfo.manage.report.model.entity.PurchaseDetailReportEntity;
import com.cosfo.manage.report.model.po.*;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportResultVO;
import com.cosfo.manage.report.model.vo.MerchantStorePurchaseReportVO;
import com.cosfo.manage.report.model.vo.PurchaseDetailReportVO;
import com.cosfo.manage.report.model.vo.ReportCommonQueryVO;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.ResponsibilityTypeEnum;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/1/13 14:06
 */
public class ReportConverter {

    public static ReportCommonQueryDTO reportCommonQueryVO2DTO(ReportCommonQueryVO reportCommonQueryVO) {

        if (reportCommonQueryVO == null) {
            return null;
        }
        ReportCommonQueryDTO reportCommonQueryDTO = new ReportCommonQueryDTO();
        reportCommonQueryDTO.setStartTime(reportCommonQueryVO.getStartTime());
        reportCommonQueryDTO.setEndTime(reportCommonQueryVO.getEndTime());
        reportCommonQueryDTO.setWarehouseId(reportCommonQueryVO.getWarehouseId());
        reportCommonQueryDTO.setWarehouseName(reportCommonQueryVO.getWarehouseName());
        reportCommonQueryDTO.setSkuId(reportCommonQueryVO.getSkuId());
        reportCommonQueryDTO.setName(reportCommonQueryVO.getName());
        reportCommonQueryDTO.setCategoryIds(reportCommonQueryVO.getCategoryIds());
        reportCommonQueryDTO.setCategoryName(reportCommonQueryVO.getCategoryName());
        reportCommonQueryDTO.setPageIndex(reportCommonQueryVO.getPageIndex());
        reportCommonQueryDTO.setPageSize(reportCommonQueryVO.getPageSize());
        return reportCommonQueryDTO;
    }

    public static PurchaseDetailReportEntity purchaseDetailReport2Entity(PurchaseDetailReport purchaseDetailReport) {

        if (purchaseDetailReport == null) {
            return null;
        }
        PurchaseDetailReportEntity purchaseDetailReportEntity = new PurchaseDetailReportEntity();
        purchaseDetailReportEntity.setId(purchaseDetailReport.getId());
        purchaseDetailReportEntity.setPurchaseDate(purchaseDetailReport.getPurchaseDate());
        purchaseDetailReportEntity.setPurchaseNo(purchaseDetailReport.getPurchaseNo());
        purchaseDetailReportEntity.setPurchaser(purchaseDetailReport.getPurchaser());
        purchaseDetailReportEntity.setWarehouse(purchaseDetailReport.getWarehouse());
        purchaseDetailReportEntity.setWarehouseId(purchaseDetailReport.getWarehouseId());
        purchaseDetailReportEntity.setPurchaseStatus(purchaseDetailReport.getPurchaseStatus());
        purchaseDetailReportEntity.setSkuId(purchaseDetailReport.getSkuId());
        purchaseDetailReportEntity.setSpuId(purchaseDetailReport.getSpuId());
        purchaseDetailReportEntity.setName(purchaseDetailReport.getName());
        purchaseDetailReportEntity.setSupplier(purchaseDetailReport.getSupplier());
        purchaseDetailReportEntity.setSupplierId(purchaseDetailReport.getSupplierId());
        purchaseDetailReportEntity.setSpecification(purchaseDetailReport.getSpecification());
        purchaseDetailReportEntity.setUnit(purchaseDetailReport.getUnit());
        purchaseDetailReportEntity.setInboundStatus(purchaseDetailReport.getInboundStatus());
        purchaseDetailReportEntity.setInboundDate(purchaseDetailReport.getInboundDate());
        purchaseDetailReportEntity.setPurchaseAmount(purchaseDetailReport.getPurchaseAmount());
        purchaseDetailReportEntity.setInboundAmount(purchaseDetailReport.getInboundAmount());
        purchaseDetailReportEntity.setPurchaseQuantity(purchaseDetailReport.getPurchaseQuantity());
        purchaseDetailReportEntity.setInboundQuantity(purchaseDetailReport.getInboundQuantity());
        purchaseDetailReportEntity.setTenantId(purchaseDetailReport.getTenantId());
        purchaseDetailReportEntity.setCategoryId(purchaseDetailReport.getCategoryId());
        purchaseDetailReportEntity.setCreateTime(purchaseDetailReport.getCreateTime());
        purchaseDetailReportEntity.setUpdateTime(purchaseDetailReport.getUpdateTime());
        return purchaseDetailReportEntity;
    }

    public static PurchaseDetailReportVO purchaseDetailEntity2VO(PurchaseDetailReportEntity purchaseDetailReportEntity) {

        if (purchaseDetailReportEntity == null) {
            return null;
        }
        PurchaseDetailReportVO purchaseDetailReportVO = new PurchaseDetailReportVO();
        purchaseDetailReportVO.setId(purchaseDetailReportEntity.getId());
        purchaseDetailReportVO.setPurchaseDate(purchaseDetailReportEntity.getPurchaseDate());
        purchaseDetailReportVO.setPurchaseNo(purchaseDetailReportEntity.getPurchaseNo());
        purchaseDetailReportVO.setPurchaser(purchaseDetailReportEntity.getPurchaser());
        purchaseDetailReportVO.setWarehouse(purchaseDetailReportEntity.getWarehouse());
        purchaseDetailReportVO.setWarehouseId(purchaseDetailReportEntity.getWarehouseId());
        purchaseDetailReportVO.setPurchaseStatus(purchaseDetailReportEntity.getPurchaseStatus());
        purchaseDetailReportVO.setSkuId(purchaseDetailReportEntity.getSkuId());
        purchaseDetailReportVO.setSpuId(purchaseDetailReportVO.getSpuId());
        purchaseDetailReportVO.setName(purchaseDetailReportEntity.getName());
        purchaseDetailReportVO.setSupplier(purchaseDetailReportEntity.getSupplier());
        purchaseDetailReportVO.setSupplierId(purchaseDetailReportEntity.getSupplierId());
        purchaseDetailReportVO.setSpecification(purchaseDetailReportEntity.getSpecification());
        purchaseDetailReportVO.setUnit(purchaseDetailReportEntity.getUnit());
        purchaseDetailReportVO.setInboundStatus(purchaseDetailReportEntity.getInboundStatus());
        purchaseDetailReportVO.setInboundDate(purchaseDetailReportEntity.getInboundDate());
        purchaseDetailReportVO.setPurchaseAmount(purchaseDetailReportEntity.getPurchaseAmount());
        purchaseDetailReportVO.setInboundAmount(purchaseDetailReportEntity.getInboundAmount());
        purchaseDetailReportVO.setPurchaseQuantity(purchaseDetailReportEntity.getPurchaseQuantity());
        purchaseDetailReportVO.setInboundQuantity(purchaseDetailReportEntity.getInboundQuantity());
        purchaseDetailReportVO.setTenantId(purchaseDetailReportEntity.getTenantId());
        purchaseDetailReportVO.setCategoryId(purchaseDetailReportEntity.getCategoryId());
        purchaseDetailReportVO.setCreateTime(purchaseDetailReportEntity.getCreateTime());
        purchaseDetailReportVO.setUpdateTime(purchaseDetailReportEntity.getUpdateTime());
        return purchaseDetailReportVO;
    }


    public static List<PurchasesBackDetailReportDTO> toPurchasesBackDetailReportDTOList(List<PurchasesBackDetailReport> purchasesBackDetailReportList) {
        if (purchasesBackDetailReportList == null) {
            return Collections.emptyList();
        }
        List<PurchasesBackDetailReportDTO> purchasesBackDetailReportDTOList = new ArrayList<>();
        for (PurchasesBackDetailReport purchasesBackDetailReport : purchasesBackDetailReportList) {
            purchasesBackDetailReportDTOList.add(toPurchasesBackDetailReportDTO(purchasesBackDetailReport));
        }
        return purchasesBackDetailReportDTOList;
    }

    public static PurchasesBackDetailReportDTO toPurchasesBackDetailReportDTO(PurchasesBackDetailReport purchasesBackDetailReport) {
        if (purchasesBackDetailReport == null) {
            return null;
        }
        PurchasesBackDetailReportDTO purchasesBackDetailReportDTO = new PurchasesBackDetailReportDTO();
        purchasesBackDetailReportDTO.setId(purchasesBackDetailReport.getId());
        purchasesBackDetailReportDTO.setBackDate(purchasesBackDetailReport.getBackDate());
        purchasesBackDetailReportDTO.setBackNo(purchasesBackDetailReport.getBackNo());
        purchasesBackDetailReportDTO.setOperator(purchasesBackDetailReport.getOperator());
        purchasesBackDetailReportDTO.setPurchaser(purchasesBackDetailReport.getPurchaser());
        purchasesBackDetailReportDTO.setSkuId(purchasesBackDetailReport.getSkuId());
        purchasesBackDetailReportDTO.setSpuId(purchasesBackDetailReport.getSpuId());
        purchasesBackDetailReportDTO.setName(purchasesBackDetailReport.getName());
        purchasesBackDetailReportDTO.setSpecification(purchasesBackDetailReport.getSpecification());
        purchasesBackDetailReportDTO.setUnit(purchasesBackDetailReport.getUnit());
        purchasesBackDetailReportDTO.setQualityDate(purchasesBackDetailReport.getQualityDate());
        purchasesBackDetailReportDTO.setProductionDate(purchasesBackDetailReport.getProductionDate());
        purchasesBackDetailReportDTO.setBackWarehouse(purchasesBackDetailReport.getBackWarehouse());
        purchasesBackDetailReportDTO.setBackWarehouseId(purchasesBackDetailReport.getBackWarehouseId());
        purchasesBackDetailReportDTO.setOutboundStatus(purchasesBackDetailReport.getOutboundStatus());
        purchasesBackDetailReportDTO.setBackAmount(purchasesBackDetailReport.getBackAmount());
        purchasesBackDetailReportDTO.setBackQuantity(purchasesBackDetailReport.getBackQuantity());
        purchasesBackDetailReportDTO.setTenantId(purchasesBackDetailReport.getTenantId());
        purchasesBackDetailReportDTO.setCategoryId(purchasesBackDetailReport.getCategoryId());
        purchasesBackDetailReportDTO.setTimeTag(purchasesBackDetailReport.getTimeTag());
        purchasesBackDetailReportDTO.setCreateTime(purchasesBackDetailReport.getCreateTime());
        purchasesBackDetailReportDTO.setUpdateTime(purchasesBackDetailReport.getUpdateTime());
        purchasesBackDetailReportDTO.setBackType(purchasesBackDetailReport.getBackType() == 0 ? "未入库退货" : "已入库退货");
        purchasesBackDetailReportDTO.setSupplier(purchasesBackDetailReport.getSupplier());
        purchasesBackDetailReportDTO.setWarehouseServiceProvider(purchasesBackDetailReport.getWarehouseServiceProvider());
        purchasesBackDetailReportDTO.setPurchasePrice(purchasesBackDetailReport.getPurchasePrice());
        return purchasesBackDetailReportDTO;
    }

    public static MerchantStoreQueryDTO merchantStoreVO2DTO(MerchantStoreLinkedVO storeVO){

        if (storeVO == null) {
            return null;
        }
        MerchantStoreQueryDTO merchantStoreQueryDTO = new MerchantStoreQueryDTO();
        merchantStoreQueryDTO.setStoreNo(storeVO.getStoreNo());
        merchantStoreQueryDTO.setTenantId(storeVO.getTenantId());
        merchantStoreQueryDTO.setStoreName(storeVO.getStoreName());
        merchantStoreQueryDTO.setType(storeVO.getType());
        merchantStoreQueryDTO.setStatus(storeVO.getStatus());
        merchantStoreQueryDTO.setProvince(storeVO.getProvince());
        merchantStoreQueryDTO.setCity(storeVO.getCity());
        merchantStoreQueryDTO.setArea(storeVO.getArea());
        merchantStoreQueryDTO.setGroupId(storeVO.getGroupId());
        if (ObjectUtils.isNotEmpty(storeVO.getId())){
            merchantStoreQueryDTO.setGroupId(storeVO.getId());
        }
        merchantStoreQueryDTO.setPageIndex(storeVO.getPageIndex());
        merchantStoreQueryDTO.setPageSize(storeVO.getPageSize());
        return merchantStoreQueryDTO;
    }


    public static MerchantStorePurchaseReportQueryDTO merchantStorePurchaseReportVO2DTO(MerchantStorePurchaseReportVO merchantStorePurchaseReportVO){

        if (merchantStorePurchaseReportVO == null) {
            return null;
        }
        MerchantStorePurchaseReportQueryDTO merchantStorePurchaseReportQueryDTO = new MerchantStorePurchaseReportQueryDTO();
        merchantStorePurchaseReportQueryDTO.setTenantId(merchantStorePurchaseReportVO.getTenantId());
        merchantStorePurchaseReportQueryDTO.setStartTime(merchantStorePurchaseReportVO.getStartTime());
        if (ObjectUtils.isNotEmpty(merchantStorePurchaseReportVO.getEndTime())){
            // 截止时间的下一天的前一毫秒
            merchantStorePurchaseReportQueryDTO.setEndTime(TimeUtils.getLocalDateTimeByTimestamp(merchantStorePurchaseReportVO.getEndTime()));
        }
        merchantStorePurchaseReportQueryDTO.setProductNo(merchantStorePurchaseReportVO.getProductNo());
        merchantStorePurchaseReportQueryDTO.setProductName(merchantStorePurchaseReportVO.getProductName());
        merchantStorePurchaseReportQueryDTO.setWarehouseType(merchantStorePurchaseReportVO.getWarehouseType());
        merchantStorePurchaseReportQueryDTO.setDeliveryType(merchantStorePurchaseReportVO.getDeliveryType());
        merchantStorePurchaseReportQueryDTO.setMarketClassification(merchantStorePurchaseReportVO.getMarketClassification());
        merchantStorePurchaseReportQueryDTO.setCategory(merchantStorePurchaseReportVO.getCategory());
        merchantStorePurchaseReportQueryDTO.setBrand(merchantStorePurchaseReportVO.getBrand());
        merchantStorePurchaseReportQueryDTO.setStoreName(merchantStorePurchaseReportVO.getStoreName());
        merchantStorePurchaseReportQueryDTO.setType(merchantStorePurchaseReportVO.getType());
        merchantStorePurchaseReportQueryDTO.setStoreGroup(merchantStorePurchaseReportVO.getStoreGroup());
        merchantStorePurchaseReportQueryDTO.setGoodsType(merchantStorePurchaseReportVO.getGoodsType());
        merchantStorePurchaseReportQueryDTO.setPageIndex(merchantStorePurchaseReportVO.getPageIndex());
        merchantStorePurchaseReportQueryDTO.setPageSize(merchantStorePurchaseReportVO.getPageSize());
        if (!CollectionUtils.isEmpty(merchantStorePurchaseReportVO.getSortList())){
            merchantStorePurchaseReportQueryDTO.setSortType(merchantStorePurchaseReportVO.getSortList().get(NumberConstant.ZERO).getOrderBy());
            merchantStorePurchaseReportQueryDTO.setSortWord(PageUtils.getQueryParams(merchantStorePurchaseReportVO.getSortList().get(NumberConstant.ZERO).getSortBy()));
        }
        merchantStorePurchaseReportQueryDTO.setOffset(merchantStorePurchaseReportVO.getOffset());
        merchantStorePurchaseReportQueryDTO.setGoodsType(merchantStorePurchaseReportQueryDTO.getGoodsType());
        return merchantStorePurchaseReportQueryDTO;
    }


    public static MerchantStorePurchaseReportResultVO toMerchantStorePurchaseReportResultVO(MerchantStorePurchaseReportResultDTO merchantStorePurchaseReportResultDTO, MarketService marketService) {
        if (merchantStorePurchaseReportResultDTO == null) {
            return null;
        }
        MerchantStorePurchaseReportResultVO merchantStorePurchaseReportResultVO = new MerchantStorePurchaseReportResultVO();
        merchantStorePurchaseReportResultVO.setId(merchantStorePurchaseReportResultDTO.getId());
        merchantStorePurchaseReportResultVO.setStoreNo(merchantStorePurchaseReportResultDTO.getStoreNo());
        merchantStorePurchaseReportResultVO.setStoreCode(merchantStorePurchaseReportResultDTO.getStoreCode());
        merchantStorePurchaseReportResultVO.setStoreName(merchantStorePurchaseReportResultDTO.getStoreName());
        merchantStorePurchaseReportResultVO.setType(StoreTypeEnum.getDesc(merchantStorePurchaseReportResultDTO.getType()));
        merchantStorePurchaseReportResultVO.setStoreGroup(merchantStorePurchaseReportResultDTO.getStoreGroup());
        merchantStorePurchaseReportResultVO.setProductNo(merchantStorePurchaseReportResultDTO.getProductNo());
        merchantStorePurchaseReportResultVO.setProductName(merchantStorePurchaseReportResultDTO.getProductName());
        merchantStorePurchaseReportResultVO.setSpecification(merchantStorePurchaseReportResultDTO.getSpecification());
        merchantStorePurchaseReportResultVO.setSpecificationUnit(merchantStorePurchaseReportResultDTO.getSpecificationUnit());
        merchantStorePurchaseReportResultVO.setWarehouseType(marketService.getOperationMode(merchantStorePurchaseReportResultDTO.getWarehouseType(), merchantStorePurchaseReportResultDTO.getGoodsType()));
        merchantStorePurchaseReportResultVO.setDeliveryType(merchantStorePurchaseReportResultDTO.getDeliveryType());
        merchantStorePurchaseReportResultVO.setMarketClassification(merchantStorePurchaseReportResultDTO.getMarketClassification());
        merchantStorePurchaseReportResultVO.setCategory(merchantStorePurchaseReportResultDTO.getCategory());
        merchantStorePurchaseReportResultVO.setBrand(merchantStorePurchaseReportResultDTO.getBrand());
        merchantStorePurchaseReportResultVO.setQuantity(merchantStorePurchaseReportResultDTO.getQuantity());
        merchantStorePurchaseReportResultVO.setAmount(merchantStorePurchaseReportResultDTO.getAmount());
        merchantStorePurchaseReportResultVO.setDeliveryTime(
                TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(merchantStorePurchaseReportResultDTO.getDeliveryTimeMin()), TimeUtils.FORMAT_DATE) + "~" +
                TimeUtils.changeDate2String(TimeUtils.localDateTimeConvertDate(merchantStorePurchaseReportResultDTO.getDeliveryTimeMax())));
        merchantStorePurchaseReportResultVO.setReceivedQuantity(merchantStorePurchaseReportResultDTO.getReceivedQuantity());
        merchantStorePurchaseReportResultVO.setReissueQuantity(merchantStorePurchaseReportResultDTO.getReissueQuantity());
        merchantStorePurchaseReportResultVO.setAfterSaleAmount(merchantStorePurchaseReportResultDTO.getAfterSaleAmount());
        merchantStorePurchaseReportResultVO.setPageIndex(merchantStorePurchaseReportResultDTO.getPageIndex());
        merchantStorePurchaseReportResultVO.setPageSize(merchantStorePurchaseReportResultDTO.getPageSize());
        merchantStorePurchaseReportResultVO.setGoodsType(GoodsTypeEnum.getShowDescByCode(merchantStorePurchaseReportResultDTO.getGoodsType()));
        return merchantStorePurchaseReportResultVO;
    }

    public static OrderItemDetailSummaryExcelDTO OrderStatementSummary2ExcelDTO(OrderItemDetailSummary data) {

        if (data == null) {
            return null;
        }
        OrderItemDetailSummaryExcelDTO orderItemDetailSummaryExcelDTO = new OrderItemDetailSummaryExcelDTO();
        orderItemDetailSummaryExcelDTO.setId(data.getId());
        orderItemDetailSummaryExcelDTO.setTenantId(data.getTenantId());
        orderItemDetailSummaryExcelDTO.setTimeTag(data.getTimeTag());
        orderItemDetailSummaryExcelDTO.setOrderNo(data.getOrderNo());
        orderItemDetailSummaryExcelDTO.setStoreName(data.getStoreName());
        orderItemDetailSummaryExcelDTO.setStoreType(data.getStoreType());
        orderItemDetailSummaryExcelDTO.setGroupName(data.getGroupName());
        orderItemDetailSummaryExcelDTO.setProvince(data.getProvince());
        orderItemDetailSummaryExcelDTO.setCity(data.getCity());
        orderItemDetailSummaryExcelDTO.setArea(data.getArea());
        orderItemDetailSummaryExcelDTO.setAddress(data.getAddress());
        orderItemDetailSummaryExcelDTO.setContactPhone(data.getContactPhone());
        orderItemDetailSummaryExcelDTO.setOrderTime(data.getOrderTime());
        orderItemDetailSummaryExcelDTO.setPayTime(data.getPayTime());
        orderItemDetailSummaryExcelDTO.setPayType(data.getPayType());
        orderItemDetailSummaryExcelDTO.setDeliveryTime(data.getDeliveryTime());
        orderItemDetailSummaryExcelDTO.setFinishedTime(data.getFinishedTime());
        orderItemDetailSummaryExcelDTO.setItemTitle(data.getItemTitle());
        orderItemDetailSummaryExcelDTO.setItemId(data.getItemId());
        orderItemDetailSummaryExcelDTO.setItemCode(data.getItemCode());
        orderItemDetailSummaryExcelDTO.setItemSpecification(data.getItemSpecification());
        orderItemDetailSummaryExcelDTO.setItemPricingType(data.getItemPricingType());
        orderItemDetailSummaryExcelDTO.setItemPricingNumber(data.getItemPricingNumber());
        orderItemDetailSummaryExcelDTO.setPayablePrice(data.getPayablePrice());
        orderItemDetailSummaryExcelDTO.setAmount(data.getAmount());
        orderItemDetailSummaryExcelDTO.setTotalPrice(data.getTotalPrice());
        orderItemDetailSummaryExcelDTO.setDeliveryFee(data.getDeliveryFee());
        orderItemDetailSummaryExcelDTO.setItemRefundPrice(data.getItemRefundPrice());
        orderItemDetailSummaryExcelDTO.setDeliveryRefundFee(Optional.ofNullable(data.getDeliveryRefundFee()).orElse(BigDecimal.ZERO));
        orderItemDetailSummaryExcelDTO.setTotalPriceDeductedRefund(data.getTotalPriceDeductedRefund());
        orderItemDetailSummaryExcelDTO.setRemark(data.getRemark());
        orderItemDetailSummaryExcelDTO.setSalesAndSupplyDifference(data.getSalesAndSupplyDifference());
        orderItemDetailSummaryExcelDTO.setSalesAndSupplyDifferenceDeductedRefund(data.getSalesAndSupplyDifferenceDeductedRefund());
        orderItemDetailSummaryExcelDTO.setGoodsTitle(data.getGoodsTitle());
        orderItemDetailSummaryExcelDTO.setGoodsSku(data.getGoodsSku());
        orderItemDetailSummaryExcelDTO.setGoodsSpecification(data.getGoodsSpecification());
        orderItemDetailSummaryExcelDTO.setGoodsSupplierName(data.getGoodsSupplierName());
        orderItemDetailSummaryExcelDTO.setGoodsType(data.getGoodsType());
        orderItemDetailSummaryExcelDTO.setGoodsAgentRule(data.getGoodsAgentRule());
        orderItemDetailSummaryExcelDTO.setGoodsAgentFee(data.getGoodsAgentFee());
        orderItemDetailSummaryExcelDTO.setGoodsSupplyPrice(data.getGoodsSupplyPrice());
        orderItemDetailSummaryExcelDTO.setGoodsSupplyTotalPrice(data.getGoodsSupplyTotalPrice());
        orderItemDetailSummaryExcelDTO.setGoodsDeliveryFee(data.getGoodsDeliveryFee());
        orderItemDetailSummaryExcelDTO.setGoodsRefundPrice(data.getGoodsRefundPrice());
        orderItemDetailSummaryExcelDTO.setGoodsTotalPrice(data.getGoodsTotalPrice());
        orderItemDetailSummaryExcelDTO.setStoreTypeDesc(StoreTypeEnum.getDesc(data.getStoreType()));
        orderItemDetailSummaryExcelDTO.setPayTypeDesc(PayTypeEnum.getPayType(data.getPayType()).getDesc());
        orderItemDetailSummaryExcelDTO.setItemPricingTypeDesc(PricingTypeEnum.getDesc(data.getItemPricingType()));
        orderItemDetailSummaryExcelDTO.setGoodsTypeDesc(ReportGoodsTypeEnum.getDesc(data.getGoodsType()));
        orderItemDetailSummaryExcelDTO.setStoreNo(data.getStoreNo());
        orderItemDetailSummaryExcelDTO.setContactName(data.getContactName());
        orderItemDetailSummaryExcelDTO.setFirstClassification(data.getFirstClassification());
        orderItemDetailSummaryExcelDTO.setSecondClassification(data.getSecondClassification());
        orderItemDetailSummaryExcelDTO.setGoodsDeliveryFeeRefund(Optional.ofNullable(data.getGoodsDeliveryFeeRefund()).orElse(BigDecimal.ZERO));
        orderItemDetailSummaryExcelDTO.setOrderFinishedNode(data.getOrderFinishedNode());
        return orderItemDetailSummaryExcelDTO;
    }

    public static OrderAfterSaleDetailSummaryExcelDTO OrderAfterSaleDetailSummary2ExcelDTO(OrderAfterSaleDetailSummary data) {

        if (data == null) {
            return null;
        }
        OrderAfterSaleDetailSummaryExcelDTO orderAfterSaleDetailSummaryExcelDTO = new OrderAfterSaleDetailSummaryExcelDTO();
        orderAfterSaleDetailSummaryExcelDTO.setId(data.getId());
        orderAfterSaleDetailSummaryExcelDTO.setTenantId(data.getTenantId());
        orderAfterSaleDetailSummaryExcelDTO.setTimeTag(data.getTimeTag());
        orderAfterSaleDetailSummaryExcelDTO.setAfterSaleOrderNo(data.getAfterSaleOrderNo());
        orderAfterSaleDetailSummaryExcelDTO.setOrderNo(data.getOrderNo());
        orderAfterSaleDetailSummaryExcelDTO.setOrderTime(data.getOrderTime());
        orderAfterSaleDetailSummaryExcelDTO.setFinishedTime(data.getFinishedTime());
        orderAfterSaleDetailSummaryExcelDTO.setItemTitle(data.getItemTitle());
        orderAfterSaleDetailSummaryExcelDTO.setItemId(data.getItemId());
        orderAfterSaleDetailSummaryExcelDTO.setItemCode(data.getItemCode());
        orderAfterSaleDetailSummaryExcelDTO.setItemSpecification(data.getItemSpecification());
        orderAfterSaleDetailSummaryExcelDTO.setItemAmount(data.getItemAmount());
        orderAfterSaleDetailSummaryExcelDTO.setAfterSaleAmount(data.getAfterSaleAmount());
        orderAfterSaleDetailSummaryExcelDTO.setAfterSaleType(data.getAfterSaleType());
        orderAfterSaleDetailSummaryExcelDTO.setServiceType(data.getServiceType());
        orderAfterSaleDetailSummaryExcelDTO.setResponsibilityType(data.getResponsibilityType());
        orderAfterSaleDetailSummaryExcelDTO.setReason(data.getReason());
        orderAfterSaleDetailSummaryExcelDTO.setTotalRefundPrice(data.getTotalRefundPrice());
        orderAfterSaleDetailSummaryExcelDTO.setItemRefundPrice(data.getItemRefundPrice());
        orderAfterSaleDetailSummaryExcelDTO.setDeliveryRefundFee(data.getDeliveryRefundFee());
        orderAfterSaleDetailSummaryExcelDTO.setGoodsTitle(data.getGoodsTitle());
        orderAfterSaleDetailSummaryExcelDTO.setGoodsSku(data.getGoodsSku());
        orderAfterSaleDetailSummaryExcelDTO.setGoodsSpecification(data.getGoodsSpecification());
        orderAfterSaleDetailSummaryExcelDTO.setGoodsSupplierName(data.getGoodsSupplierName());
        orderAfterSaleDetailSummaryExcelDTO.setGoodsType(data.getGoodsType());
        orderAfterSaleDetailSummaryExcelDTO.setGoodsAgentFee(data.getGoodsAgentFee());
        orderAfterSaleDetailSummaryExcelDTO.setGoodsRefundPrice(data.getGoodsRefundPrice());
        orderAfterSaleDetailSummaryExcelDTO.setAfterSaleTypeDesc(OrderAfterSaleTypeEnum.getDesc(data.getAfterSaleType()));
        orderAfterSaleDetailSummaryExcelDTO.setServiceTypeDesc(OrderAfterSaleServiceTypeEnum.getDesc(data.getServiceType()));
        orderAfterSaleDetailSummaryExcelDTO.setGoodsTypeDesc(ReportGoodsTypeEnum.getDesc(data.getGoodsType()));
        Integer responsibilityType = data.getResponsibilityType();
        orderAfterSaleDetailSummaryExcelDTO.setResponsibilityTypeDesc(Objects.equals(responsibilityType, 0) ? "供应商" : Objects.equals(responsibilityType, 1) ? "品牌方" : "门店");
        orderAfterSaleDetailSummaryExcelDTO.setStoreName(data.getStoreName());
        orderAfterSaleDetailSummaryExcelDTO.setPayTypeDesc(PayTypeEnum.getPayType(data.getPayType()).getDesc());
        orderAfterSaleDetailSummaryExcelDTO.setFirstClassification(data.getFirstClassification());
        orderAfterSaleDetailSummaryExcelDTO.setSecondClassification(data.getSecondClassification());
        orderAfterSaleDetailSummaryExcelDTO.setStoreNo(data.getStoreNo());
        orderAfterSaleDetailSummaryExcelDTO.setAfterSaleTime(data.getAfterSaleTime());
        orderAfterSaleDetailSummaryExcelDTO.setGoodsDeliveryFeeRefund(data.getGoodsDeliveryFeeRefund());
        return orderAfterSaleDetailSummaryExcelDTO;
    }

    public static OrderSoldBelowSupplySummaryExcelDTO OrderSoldBelowSupplySummary2ExcelDTO(OrderSoldBelowSupplySummary data) {
        if (data == null) {
            return null;
        }
        OrderSoldBelowSupplySummaryExcelDTO orderSoldBelowSupplySummaryExcelDTO = new OrderSoldBelowSupplySummaryExcelDTO();
        orderSoldBelowSupplySummaryExcelDTO.setId(data.getId());
        orderSoldBelowSupplySummaryExcelDTO.setTenantId(data.getTenantId());
        orderSoldBelowSupplySummaryExcelDTO.setTimeTag(data.getTimeTag());
        orderSoldBelowSupplySummaryExcelDTO.setOrderNo(data.getOrderNo());
        orderSoldBelowSupplySummaryExcelDTO.setStoreName(data.getStoreName());
        orderSoldBelowSupplySummaryExcelDTO.setAddress(data.getAddress());
        orderSoldBelowSupplySummaryExcelDTO.setContactPhone(data.getContactPhone());
        orderSoldBelowSupplySummaryExcelDTO.setOrderTime(data.getOrderTime());
        orderSoldBelowSupplySummaryExcelDTO.setPayTime(data.getPayTime());
        orderSoldBelowSupplySummaryExcelDTO.setPayType(data.getPayType());
        orderSoldBelowSupplySummaryExcelDTO.setDeliveryTime(data.getDeliveryTime());
        orderSoldBelowSupplySummaryExcelDTO.setFinishedTime(data.getFinishedTime());
        orderSoldBelowSupplySummaryExcelDTO.setProfitSharingTime(data.getProfitSharingTime());
        orderSoldBelowSupplySummaryExcelDTO.setDifference(data.getDifference());
        orderSoldBelowSupplySummaryExcelDTO.setItemTitle(data.getItemTitle());
        orderSoldBelowSupplySummaryExcelDTO.setFirstClassificationName(data.getFirstClassificationName());
        orderSoldBelowSupplySummaryExcelDTO.setSecondClassificationName(data.getSecondClassificationName());
        orderSoldBelowSupplySummaryExcelDTO.setItemId(data.getItemId());
        orderSoldBelowSupplySummaryExcelDTO.setItemCode(data.getItemCode());
        orderSoldBelowSupplySummaryExcelDTO.setItemSpecification(data.getItemSpecification());
        orderSoldBelowSupplySummaryExcelDTO.setAmount(data.getAmount());
        orderSoldBelowSupplySummaryExcelDTO.setPayablePrice(data.getPayablePrice());
        orderSoldBelowSupplySummaryExcelDTO.setItemTotalPrice(data.getItemTotalPrice());
        orderSoldBelowSupplySummaryExcelDTO.setDeliveryFee(data.getDeliveryFee());
        orderSoldBelowSupplySummaryExcelDTO.setRefundPrice(data.getRefundPrice());
        orderSoldBelowSupplySummaryExcelDTO.setTotalPrice(data.getTotalPrice());
        orderSoldBelowSupplySummaryExcelDTO.setRemark(data.getRemark());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsTitle(data.getGoodsTitle());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsSku(data.getGoodsSku());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsSpecification(data.getGoodsSpecification());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsSupplierName(data.getGoodsSupplierName());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsType(data.getGoodsType());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsSupplyPrice(data.getGoodsSupplyPrice());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsSupplyTotalPrice(data.getGoodsSupplyTotalPrice());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsSupplyDeliveryFee(data.getGoodsSupplyDeliveryFee());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsSupplyRefundPrice(data.getGoodsSupplyRefundPrice());
        orderSoldBelowSupplySummaryExcelDTO.setPayTypeDesc(PayTypeEnum.getPayType(data.getPayType()).getDesc());
        orderSoldBelowSupplySummaryExcelDTO.setGoodsTypeDesc(ReportGoodsTypeEnum.getDesc(data.getGoodsType()));
        return orderSoldBelowSupplySummaryExcelDTO;
    }

    public static MarketItemSalesSummaryExcelDTO MarketItemSalesSummary2ExcelDTO(MarketItemSalesSummary data) {

        if (data == null) {
            return null;
        }
        MarketItemSalesSummaryExcelDTO marketItemSalesSummaryExcelDTO = new MarketItemSalesSummaryExcelDTO();
        marketItemSalesSummaryExcelDTO.setId(data.getId());
        marketItemSalesSummaryExcelDTO.setTenantId(data.getTenantId());
        marketItemSalesSummaryExcelDTO.setTimeTag(data.getTimeTag());
        marketItemSalesSummaryExcelDTO.setItemId(data.getItemId());
        marketItemSalesSummaryExcelDTO.setItemCode(data.getItemCode());
        marketItemSalesSummaryExcelDTO.setItemTitle(data.getItemTitle());
        marketItemSalesSummaryExcelDTO.setItemSpecification(data.getItemSpecification());
        marketItemSalesSummaryExcelDTO.setTotalAmount(data.getTotalAmount());
        marketItemSalesSummaryExcelDTO.setAveragePayablePrice(data.getTotalAmount() == 0 ? BigDecimal.ZERO : NumberUtil.div(data.getTotalPrice(), data.getTotalAmount()).setScale(NumberConstant.TWO, ROUND_HALF_UP));
        marketItemSalesSummaryExcelDTO.setTotalPrice(data.getTotalPrice());
        marketItemSalesSummaryExcelDTO.setTotalRefundPrice(data.getTotalRefundPrice());
        marketItemSalesSummaryExcelDTO.setTotalPriceDeductedRefund(data.getTotalPriceDeductedRefund());
        marketItemSalesSummaryExcelDTO.setGoodAverageSupplyPrice(data.getTotalAmount() == 0 ? BigDecimal.ZERO : NumberUtil.div(data.getGoodsTotalSupplyPrice(), data.getTotalAmount()).setScale(NumberConstant.TWO, ROUND_HALF_UP));
        marketItemSalesSummaryExcelDTO.setGoodsTotalSupplyPrice(data.getGoodsTotalSupplyPrice());
        marketItemSalesSummaryExcelDTO.setSalesAndSupplyDifferenceDeductedPrice(data.getSalesAndSupplyDifferenceDeductedPrice());
        marketItemSalesSummaryExcelDTO.setPayType(data.getPayType());
        marketItemSalesSummaryExcelDTO.setGoodsRefundPrice(data.getGoodsRefundPrice());
        marketItemSalesSummaryExcelDTO.setGoodsPriceDeductedRefund(data.getGoodsPriceDeductedRefund());
        return marketItemSalesSummaryExcelDTO;
    }

    public static OrderStatementSummaryExcelDTO orderStatementSummary2ExcelDTO(OrderStatementSummary orderStatementSummary) {

        if (orderStatementSummary == null) {
            return null;
        }
        OrderStatementSummaryExcelDTO orderStatementSummaryExcelDTO = new OrderStatementSummaryExcelDTO();
        orderStatementSummaryExcelDTO.setId(orderStatementSummary.getId());
        orderStatementSummaryExcelDTO.setTenantId(orderStatementSummary.getTenantId());
        orderStatementSummaryExcelDTO.setTimeTag(orderStatementSummary.getTimeTag());
        orderStatementSummaryExcelDTO.setTotalPrice(orderStatementSummary.getTotalPrice());
        orderStatementSummaryExcelDTO.setWechatPayTotalPrice(orderStatementSummary.getWechatPayTotalPrice());
        orderStatementSummaryExcelDTO.setBillBalancePayTotalPrice(orderStatementSummary.getBillBalancePayTotalPrice());
        orderStatementSummaryExcelDTO.setSupplyTotalPrice(orderStatementSummary.getSupplyTotalPrice());
        orderStatementSummaryExcelDTO.setSupplyDeliveryFee(orderStatementSummary.getSupplyDeliveryFee());
        orderStatementSummaryExcelDTO.setRefundPriceDeductedSupply(orderStatementSummary.getRefundPriceDeductedSupply());
        orderStatementSummaryExcelDTO.setTotalDifference(orderStatementSummary.getTotalDifference());
        orderStatementSummaryExcelDTO.setGoodsAgentFee(orderStatementSummary.getGoodsAgentFee());
        orderStatementSummaryExcelDTO.setGoodsAgentRefundFeeSupplyResponsibility(orderStatementSummary.getGoodsAgentRefundFeeSupplyResponsibility());
        orderStatementSummaryExcelDTO.setSalesAndSupplyDifference(orderStatementSummary.getSalesAndSupplyDifference());
        orderStatementSummaryExcelDTO.setRefundSalesAndSupplyDifference(orderStatementSummary.getRefundSalesAndSupplyDifference());
        orderStatementSummaryExcelDTO.setGrossProfit(orderStatementSummary.getGrossProfit());
        orderStatementSummaryExcelDTO.setWechatPaySalesAndSupplyDifferenceDeductedRefund(orderStatementSummary.getWechatPaySalesAndSupplyDifferenceDeductedRefund());
        orderStatementSummaryExcelDTO.setBillBalancePaySalesAndSupplyDifferenceDeductedRefund(orderStatementSummary.getBillBalancePaySalesAndSupplyDifferenceDeductedRefund());
        orderStatementSummaryExcelDTO.setOrderCount(orderStatementSummary.getOrderCount());
        orderStatementSummaryExcelDTO.setOrderItemCount(orderStatementSummary.getOrderItemCount());
        orderStatementSummaryExcelDTO.setCreateTime(orderStatementSummary.getCreateTime());
        orderStatementSummaryExcelDTO.setUpdateTime(orderStatementSummary.getUpdateTime());
        orderStatementSummaryExcelDTO.setWechatPayTotalPriceDeductedDeliveryFee(orderStatementSummary.getWechatPayTotalPriceDeductedDeliveryFee());
        orderStatementSummaryExcelDTO.setBillBalancePayTotalPriceDeductedDeliveryFee(orderStatementSummary.getBillBalancePayTotalPriceDeductedDeliveryFee());
        orderStatementSummaryExcelDTO.setWechatPaySupplyDeliveryFee(orderStatementSummary.getWechatPaySupplyDeliveryFee());
        orderStatementSummaryExcelDTO.setBillBalancePaySupplyDeliveryFee(orderStatementSummary.getBillBalancePaySupplyDeliveryFee());
        return orderStatementSummaryExcelDTO;
    }

    public static OrderAfterSaleInvertedSummaryDTO toOrderAfterSaleInvertedSummaryDTO(OrderAfterSaleInvertedSummary orderAfterSaleInvertedSummary){
        if (orderAfterSaleInvertedSummary == null) {
            return null;
        }
        OrderAfterSaleInvertedSummaryDTO orderAfterSaleInvertedSummaryDTO = new OrderAfterSaleInvertedSummaryDTO();
        orderAfterSaleInvertedSummaryDTO.setId(orderAfterSaleInvertedSummary.getId());
        orderAfterSaleInvertedSummaryDTO.setCreateTime(orderAfterSaleInvertedSummary.getCreateTime());
        orderAfterSaleInvertedSummaryDTO.setUpdateTime(orderAfterSaleInvertedSummary.getUpdateTime());
        orderAfterSaleInvertedSummaryDTO.setTenantId(orderAfterSaleInvertedSummary.getTenantId());
        orderAfterSaleInvertedSummaryDTO.setAfterSaleOrderNo(orderAfterSaleInvertedSummary.getAfterSaleOrderNo());
        orderAfterSaleInvertedSummaryDTO.setOrderNo(orderAfterSaleInvertedSummary.getOrderNo());
        orderAfterSaleInvertedSummaryDTO.setApplyTime(orderAfterSaleInvertedSummary.getApplyTime());
        orderAfterSaleInvertedSummaryDTO.setAuditTime(orderAfterSaleInvertedSummary.getAuditTime());
        orderAfterSaleInvertedSummaryDTO.setRefundTotalAmount(orderAfterSaleInvertedSummary.getRefundTotalAmount());
        orderAfterSaleInvertedSummaryDTO.setResponsibilityType(orderAfterSaleInvertedSummary.getResponsibilityType());
        orderAfterSaleInvertedSummaryDTO.setBrandRefundableAmount(orderAfterSaleInvertedSummary.getBrandRefundableAmount());
        orderAfterSaleInvertedSummaryDTO.setSupplierRefundableAmount(orderAfterSaleInvertedSummary.getSupplierRefundableAmount());
        orderAfterSaleInvertedSummaryDTO.setBrandActualAmount(orderAfterSaleInvertedSummary.getBrandActualAmount());
        orderAfterSaleInvertedSummaryDTO.setSupplierActualAmount(orderAfterSaleInvertedSummary.getSupplierActualAmount());
        orderAfterSaleInvertedSummaryDTO.setInvertedAmount(orderAfterSaleInvertedSummary.getInvertedAmount());
        String responsibilityTypeDesc = null;
        if(ResponsibilityTypeEnum.SUPPLIER.getType().equals(orderAfterSaleInvertedSummary.getResponsibilityType())){
            responsibilityTypeDesc = ResponsibilityTypeEnum.SUPPLIER.getDesc();
        }else if(ResponsibilityTypeEnum.BRAND.getType().equals(orderAfterSaleInvertedSummary.getResponsibilityType())) {
            responsibilityTypeDesc = ResponsibilityTypeEnum.BRAND.getDesc();
        }else if(ResponsibilityTypeEnum.MERCHANT.getType().equals(orderAfterSaleInvertedSummary.getResponsibilityType())) {
            responsibilityTypeDesc = ResponsibilityTypeEnum.MERCHANT.getDesc();
        }

        orderAfterSaleInvertedSummaryDTO.setResponsibilityTypeDesc(responsibilityTypeDesc);
        return orderAfterSaleInvertedSummaryDTO;
    }
}
