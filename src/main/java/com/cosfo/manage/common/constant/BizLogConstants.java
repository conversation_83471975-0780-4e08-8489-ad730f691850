package com.cosfo.manage.common.constant;

import com.cosfo.manage.common.context.ProductAgentItemStatusEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.log.dto.OperatorDto;

/**
 * @author: monna.chen
 * @Date: 2023/12/26 21:33
 * @Description:
 */
public class BizLogConstants {

    public static final String SKU_AGENT_DOMAIN = "货品";

    public static final String SKU_AGENT_ENTITY_TYPE = "代仓申请记录";

    public static final String SKU_AGENT_REFUSE = "代仓审核拒绝";

    public static final String SKU_AGENT_SUCCESS = "代仓审核通过";

    public static final String SKU_AGENT_PROCESSING = "代仓发起申请";

    public static final String SKU_AGENT_CANCEL = "代仓取消申请";

    public static final Integer DEFAULT_PAGE_INDEX = 1;

    public static final Integer DEFAULT_PAGE_SIZE = 1000;

    public static final Long NO_EXIST_USER_ID = 0L;

    public static OperatorDto getDefaultOperator() {
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setTenantId(XianmuSupplyTenant.TENANT_ID);
        operatorDto.setSystemOrigin(SystemOriginEnum.ADMIN.getType());
        operatorDto.setUserId(NO_EXIST_USER_ID);
        return operatorDto;
    }

    public static OperatorDto getSystemOperator(Long tenantId) {
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setTenantId(tenantId);
        operatorDto.setSystemOrigin(SystemOriginEnum.COSFO_MANAGE.getType());
        operatorDto.setUserId(NO_EXIST_USER_ID);
        return operatorDto;
    }

    public static String getAgentOperationNameByStatus(Integer agentStatus) {
        if (agentStatus.equals(ProductAgentItemStatusEnum.PROCESSING.getStatus())) {
            return SKU_AGENT_PROCESSING;
        } else if (agentStatus.equals(ProductAgentItemStatusEnum.SUCCESS.getStatus())) {
            return SKU_AGENT_SUCCESS;
        } else if (agentStatus.equals(ProductAgentItemStatusEnum.FAIL.getStatus())) {
            return SKU_AGENT_REFUSE;
        } else if (agentStatus.equals(ProductAgentItemStatusEnum.CANCEL.getStatus())) {
            return SKU_AGENT_CANCEL;
        } else {
            return null;
        }
    }

}
