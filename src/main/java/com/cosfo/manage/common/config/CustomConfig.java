package com.cosfo.manage.common.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2023-12-26
 * @Description:定制功能配置
 */
@Slf4j
@Configuration
@Data
public class CustomConfig {

    /**
     * 绝配订单导出租户
     */
    @NacosValue(value = "${order.export.juepei.tenantId:32}", autoRefreshed = true)
    public Set<Long> juepeiTenantIds;

    /**
     * 货品自有编码必填限制租户
     */
    @NacosValue(value = "${product.sku.customskucode.required.tenantId:32}", autoRefreshed = true)
    public Set<Long> customSkuCodeRequiredTenantIds;

    /**
     * 密码超过指定天数,弹窗不再提示的天数
     */
    @NacosValue(value = "${password.no.prompt.days:180}", autoRefreshed = true)
    public Long noPromptDays;

    @NacosValue(value = "${change.password.interval.days:180}", autoRefreshed = true)
    private Integer changePasswordIntervalDays;

    /**
     * 超级账号token过去时间，默认单位h
     */
    @NacosValue(value = "${admin.token.expire.time:4}", autoRefreshed = true)
    private Long superTokenExpireTime;

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }

}
