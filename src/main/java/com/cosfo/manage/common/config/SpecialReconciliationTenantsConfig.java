package com.cosfo.manage.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @description: 特殊对账租户配置
 * @author: <PERSON>
 * @date: 2024-02-23
 **/
@Component
@Data
public class SpecialReconciliationTenantsConfig {

    @NacosValue(value = "${open.special.reconciliation.tenants}", autoRefreshed = true)
    private String specialReconciliationTenants;

    public List<Long> getSpecialReconciliationTenants() {
        return specialReconciliationTenants == null ? Collections.emptyList() : JSON.parseArray(specialReconciliationTenants, Long.class);
    }
}
