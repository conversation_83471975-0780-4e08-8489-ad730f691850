package com.cosfo.manage.common.converter;

import com.cosfo.manage.report.model.dto.ProductAgentShelfLifeQueryDTO;
import net.summerfarm.manage.client.saas.req.PageQueryPurchaseBatchInventoryReq;
import net.xianmu.common.input.PageSortInput;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface PageQueryPurchaseBatchInventoryReqMapper {

    PageQueryPurchaseBatchInventoryReqMapper INSTANCE = Mappers.getMapper(PageQueryPurchaseBatchInventoryReqMapper.class);

    @Mapping(target = "quantityDateSort", source = "sortInput.orderBy", defaultValue = "asc")
    PageQueryPurchaseBatchInventoryReq queryToReq(ProductAgentShelfLifeQueryDTO dto, PageSortInput sortInput);
}
