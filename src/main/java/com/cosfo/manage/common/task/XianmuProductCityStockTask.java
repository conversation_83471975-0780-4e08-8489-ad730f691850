package com.cosfo.manage.common.task;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.mapper.ProductSkuMapper;
import com.cosfo.manage.product.model.dto.AgentSkuDumpDTO;
import com.cosfo.manage.product.model.dto.AgentTenantSkuDTO;
import com.cosfo.manage.product.model.dto.AgentTenantSkuQueryDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.report.model.dto.ProductAgentStockQueryDTO;
import com.cosfo.manage.report.service.ProductAgentStockReportService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: fansongsong
 * @Date: 2023-05-19
 * @Description:鲜沐报价货品城市库存同步定时任务
 */
@Component
@Slf4j
public class XianmuProductCityStockTask extends XianMuJavaProcessorV2 {

    @Resource
    private ProductSkuMapper productSkuMapper;
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private ProductAgentStockReportService productAgentStockReportService;

    @Override
    public ProcessResult processResult(XmJobInput context) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("鲜沐报价货品城市库存同步定时任务-start");

        List<AgentSkuDumpDTO> agentSkuDumpDTOS = agentSkuDumpDTOS();
        if (CollectionUtils.isEmpty(agentSkuDumpDTOS)) {
            log.error("无需更新库存");
            return new ProcessResult(true, "没有找到需要更新库存的SKU");
        }

        log.info("一共有:{}个报价城市需要同步库存", agentSkuDumpDTOS.size());
        AtomicInteger resultCounter = new AtomicInteger(0);
        agentSkuDumpDTOS.stream().forEach(agentSkuDumpDTO -> {
            List<String> skuCodes = Arrays.asList(agentSkuDumpDTO.getAgentSkuCodeList().split(","));
            List<ProductAgentSkuMapping> productAgentSkuMappingList = productAgentSkuMappingMapper.queryAgentSkuInfoByAgentSkuCodes(skuCodes);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(productAgentSkuMappingList)) {
                return;
            }
            Map<String, ProductAgentSkuMapping> skuCodeToSkuIdMap = productAgentSkuMappingList.stream()
                .collect(Collectors.toMap(ProductAgentSkuMapping::getAgentSkuCode, item -> item));
            List<AgentTenantSkuDTO> agentTenantSkuDTOList = skuCodes.stream().map(skuCode -> {
                AgentTenantSkuDTO agentTenantSkuDTO = new AgentTenantSkuDTO();
                agentTenantSkuDTO.setAgentSkuCode(skuCode);
                agentTenantSkuDTO.setCityId(agentSkuDumpDTO.getCityId());
                agentTenantSkuDTO.setCityName(agentSkuDumpDTO.getCity());
                ProductAgentSkuMapping productAgentSkuMapping = skuCodeToSkuIdMap.get(skuCode);
                if (null == productAgentSkuMapping) {
                    log.error("Can not find:{}", skuCode);
                    return null;
                }
                agentTenantSkuDTO.setId(productAgentSkuMapping.getSkuId());
                return agentTenantSkuDTO;
            }).filter(item -> item != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(agentTenantSkuDTOList)) {
                log.warn("empty list");
                return;
            }

            // 同步库存信息
            // 每个城市调用一次WMS的接口，预计调用400余次；product_agent_sku_mapping
            log.info("同步城市:{} 的SKU库存信息:{}", agentSkuDumpDTO.getCity(), agentSkuDumpDTO.getAgentSkuCodeList());
            productAgentStockReportService.syncSkuCityStock(agentTenantSkuDTOList, false);
            resultCounter.addAndGet(agentTenantSkuDTOList.size());
            try {
                TimeUnit.MILLISECONDS.sleep(NumberConstant.HUNDRED);
            } catch (InterruptedException e) {
                log.error("Current thread was interrupted", e);
            }
        });

        log.info("鲜沐报价货品城市库存同步定时任务-end, spent={}", stopwatch.stop());
        return new ProcessResult(true, "完成库存同步的sku数:" + resultCounter.get());
    }

    /**
     * 分页查询鲜沐商品报价单
     *
     * @return
     */
    private PageInfo<AgentTenantSkuDTO> pageQuerySupplyByAgentTenantId(ProductAgentStockQueryDTO productAgentStockQueryDTO) {
        Page<AgentTenantSkuDTO> page = PageHelper.startPage(productAgentStockQueryDTO.getPageNum(), productAgentStockQueryDTO.getPageSize());
        // 查询鲜沐报价货品信息
        AgentTenantSkuQueryDTO agentTenantSkuQueryDTO = AgentTenantSkuQueryDTO.builder().agentTenantId(XianmuSupplyTenant.TENANT_ID).build();
        List<AgentTenantSkuDTO> agentTenantSkuDTOList = productSkuMapper.selectAgentTenantSkuInfoByAgentTenantId(agentTenantSkuQueryDTO);
        if (CollectionUtils.isEmpty(agentTenantSkuDTOList)) {
            return PageInfoHelper.createPageInfo(Collections.EMPTY_LIST, productAgentStockQueryDTO.getPageSize());
        }
        return PageInfoConverter.toPageInfo(page, agentTenantSkuDTOList);
    }

    private List<AgentSkuDumpDTO> agentSkuDumpDTOS() {
        return productSkuMapper.dumpAgentTenantSkuInfoByAgentTenantId(XianmuSupplyTenant.TENANT_ID);
    }
}
