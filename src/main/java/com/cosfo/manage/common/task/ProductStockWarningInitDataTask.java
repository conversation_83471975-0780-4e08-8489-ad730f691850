package com.cosfo.manage.common.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.manage.common.context.GoodsTypeEnum;
import com.cosfo.manage.facade.WarehouseInventoryFacade;
import com.cosfo.manage.facade.WarehouseStorageQueryFacade;
import com.cosfo.manage.good.dao.ProductAgentApplicationRecordDao;
import com.cosfo.manage.good.model.dto.ProductAgentApplicationRecordQueryConditionDTO;
import com.cosfo.manage.good.model.po.ProductAgentApplicationRecord;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.mapper.ProductPricingSupplyMapper;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;
import com.cosfo.manage.report.model.dto.ProductStockChangeDTO;
import com.cosfo.manage.report.service.ProductSkuOrderSummaryService;
import com.cosfo.manage.report.service.ProductStockForewarningReportService;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsMappingQueryProvider;
import net.summerfarm.goods.client.req.ProductMappingQueryReq;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.summerfarm.wms.inventory.resp.WarehouseSkuInventoryPageResp;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * 库存预警表初始化历史数据 定时任务
 *
 * @author: xiaowk
 * @time: 2023/10/31 下午1:41
 */
@Component
@Slf4j
public class ProductStockWarningInitDataTask extends XianMuJavaProcessorV2 {

    @Resource
    private ProductSkuOrderSummaryService productSkuOrderSummaryService;

    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;

    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;

    @Resource
    private WarehouseInventoryFacade warehouseInventoryFacade;

    @Resource
    private ProductStockForewarningReportService productStockForewarningReportService;

    @Resource
    private ProductPricingSupplyMapper productPricingSupplyMapper;

    @Resource
    private ProductAgentApplicationRecordDao productAgentApplicationRecordDao;

    @DubboReference
    private ProductsMappingQueryProvider productsMappingQueryProvider;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("库存预警初始化销量&库存 task start, 参数：{}", context);

        InitDataDTO initDataDTO = null;

        String jobParameters = context.getJobParameters();
        String instanceParameters = context.getInstanceParameters();

        if (!StringUtils.isEmpty(jobParameters)) {
            initDataDTO = JSONObject.parseObject(jobParameters, InitDataDTO.class);
        }
        if (!StringUtils.isEmpty(instanceParameters)) {
            initDataDTO = JSONObject.parseObject(instanceParameters, InitDataDTO.class);

        }

        if (initDataDTO == null) {
            throw new ProviderException("任务参数配置错误");
        }

        if(initDataDTO.isNeedInitSaleQty()) {
            // 初始化销量表
            productSkuOrderSummaryService.initProductSkuOrderSummary(30);
        }

        if (initDataDTO.isNeedInitStockQty()) {
            initStockWarn(initDataDTO.getTenantIdList());
        }

        log.info("库存预警初始化销量&库存 task end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }


    public void initStockWarn(List<Long> tenantIdList){
        if(CollectionUtils.isEmpty(tenantIdList)){
            return;
        }

        for (Long tenantId : tenantIdList) {
            // 处理自营货品
            handleSelfSku(tenantId);

            // 处理报价货品
            handleQuotationSku(tenantId);
        }

    }

    /**
     * 处理自营货品
     * @param tenantId
     */
    private void handleSelfSku(Long tenantId){
        // 1. 获取租户的自营仓、代仓
        // WarehouseSourceEnum 1-代仓 2-自营仓
        List<WarehouseStorageResp> warehouseList = warehouseStorageQueryFacade.queryWarehouseStorageList(tenantId,null, null);
        // 没有仓库，一定没有库存，不处理
        if(CollectionUtils.isEmpty(warehouseList)){
            return;
        }

//        List<Integer> warehouseNoList = warehouseList.stream().map(WarehouseStorageResp::getWarehouseNo).distinct().collect(Collectors.toList());
        Map<WarehouseSourceEnum, List<Integer>> warehouseMap = warehouseList.stream().collect(Collectors.groupingBy(WarehouseStorageResp::getWarehouseSourceEnum, Collectors.mapping(WarehouseStorageResp::getWarehouseNo, Collectors.toList())));
        // 自营仓
        List<Integer> selfWarehouseNoList = warehouseMap.get(WarehouseSourceEnum.SAAS_WAREHOUSE);
        // 代仓
        List<Integer> agentWarehouseNoList = warehouseMap.get(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE);


        // 2. 获取当前租户的自营货品
        ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
        queryReq.setTenantId(tenantId);
        List<ProductsMappingResp> productAgentSkuMappings = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
        if(CollectionUtils.isEmpty(productAgentSkuMappings)){
            return;
        }


        Map<String, Long> skucode2skuIdMap = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductsMappingResp::getSku, ProductsMappingResp::getSkuId, (v1, v2) -> v1));
        Map<Long, String> skuId2skuCodeMap = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductsMappingResp::getSkuId, ProductsMappingResp::getSku, (v1, v2) -> v1));
        List<String> skucodeList = productAgentSkuMappings.stream().map(ProductsMappingResp::getSku).distinct().collect(Collectors.toList());

        // 3. 根据sku + warehouseNo 查询库存

        // 自营仓 查库存
        // 4. 初始化库存预警记录
        selfSku(tenantId, skucodeList, selfWarehouseNoList, skucode2skuIdMap, 100);


        if(CollectionUtils.isEmpty(agentWarehouseNoList)){
            return;
        }

        ProductAgentApplicationRecordQueryConditionDTO conditionDTO = new ProductAgentApplicationRecordQueryConditionDTO();
        conditionDTO.setStatus(1);
        conditionDTO.setTenantId(tenantId);
        List<ProductAgentApplicationRecord> productAgentApplicationRecords = productAgentApplicationRecordDao.listByCondition (conditionDTO);
        if(CollectionUtils.isEmpty(productAgentApplicationRecords)){
            return;
        }

        // 有代仓记录的skucode
        List<String> agentSkucodeList = productAgentApplicationRecords.stream()
                .filter(e -> skuId2skuCodeMap.containsKey(e.getSkuId()))
                .map(ProductAgentApplicationRecord::getSkuId)
                .distinct()
                .map(e -> skuId2skuCodeMap.get(e)).collect(Collectors.toList());

        // 自营代仓查库存
        selfSku(tenantId, agentSkucodeList, agentWarehouseNoList, skucode2skuIdMap, 20);

    }

    private void selfSku(Long tenantId, List<String> skucodeList, List<Integer> warehouseNoList, Map<String, Long> skucode2skuIdMap, Integer partitionSize){
        if(CollectionUtils.isEmpty(skucodeList) || CollectionUtils.isEmpty(warehouseNoList) || CollectionUtils.isEmpty(skucode2skuIdMap)){
            return;
        }
        List<List<String>> partition = Lists.partition(skucodeList, partitionSize);
        for (List<String> skucodes : partition) {
            Map<String, List<WarehouseSkuInventoryPageResp>> stockMap = warehouseInventoryFacade.queryWarehouseSkuInventory(skucodes, warehouseNoList);
            for (String skucode : skucodes) {
                List<WarehouseSkuInventoryPageResp> warehouseStockList = stockMap.get(skucode);
                if(CollectionUtils.isEmpty(warehouseStockList)){
                    continue;
                }

                warehouseStockList = warehouseStockList.stream().filter(e -> e.getAvailableQuantity() != null && e.getAvailableQuantity() > 0).collect(Collectors.toList());

                for (WarehouseSkuInventoryPageResp warehouseSkuInventoryPageResp : warehouseStockList) {
                    ProductStockChangeDTO productStockChangeDTO = new ProductStockChangeDTO();
                    productStockChangeDTO.setTenantId(tenantId);
                    productStockChangeDTO.setSkuId(skucode2skuIdMap.get(skucode));
                    productStockChangeDTO.setSkuCode(skucode);
                    productStockChangeDTO.setWarehouseNo(warehouseSkuInventoryPageResp.getWarehouseNo().intValue());
                    productStockChangeDTO.setGoodsType(GoodsTypeEnum.SELF_GOOD_TYPE.getCode());
                    productStockChangeDTO.setNewOnlineQuantity(warehouseSkuInventoryPageResp.getAvailableQuantity());

                    // 初始化库存预警记录
                    productStockForewarningReportService.updateStockWarn(productStockChangeDTO);
                }
            }
        }
    }

    /**
     * 处理报价货品
     * @param tenantId
     */
    private void handleQuotationSku(Long tenantId){

        // 1. 获取当前租户报价货品
        List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingMapper.selectSupplySkuByTenantId(tenantId);
        Map<String, Long> skucode2skuIdMap = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductAgentSkuMapping::getAgentSkuCode, ProductAgentSkuMapping::getSkuId, (v1, v2) -> v1));
        List<String> skucodeList = productAgentSkuMappings.stream().map(ProductAgentSkuMapping::getAgentSkuCode).distinct().collect(Collectors.toList());
        List<Long> skuIdList = productAgentSkuMappings.stream().map(ProductAgentSkuMapping::getSkuId).distinct().collect(Collectors.toList());

        List<ProductPricingSupplyCityMappingDTO> mappingDTOS = productPricingSupplyMapper.queryEffectSupplyCityBySkuId(skuIdList, tenantId);
        Map<Long, List<String>> skuId2CitysMap = mappingDTOS.stream().collect(Collectors.groupingBy(ProductPricingSupplyCityMappingDTO::getSupplySkuId, Collectors.mapping(ProductPricingSupplyCityMappingDTO::getCityName, Collectors.toList())));

        for (String skucode : skucodeList) {
            List<String> cityNames = skuId2CitysMap.get(skucode2skuIdMap.get(skucode));

            // 2. 根据sku+报价城市获取仓库
            Map<String, Set<Integer>> sku2warehouseNosMap = warehouseStorageQueryFacade.queryXmWarehouseNosBySkuCity(Collections.singletonList(skucode), cityNames);

            // skucode 报价城市，对应的仓库
            List<Integer> warehouseNoList = Lists.newArrayList(sku2warehouseNosMap.get(skucode));
            if(CollectionUtils.isEmpty(warehouseNoList)){
                continue;
            }

            // 3. 根据sku + warehouseNo 查询库存
            Map<String, List<WarehouseSkuInventoryPageResp>> skuStockMap = warehouseInventoryFacade.queryWarehouseSkuInventory(Collections.singletonList(skucode), warehouseNoList);
            List<WarehouseSkuInventoryPageResp> skuStockList = skuStockMap.get(skucode);
            if (CollectionUtils.isEmpty(skuStockList)) {
                continue;
            }

            skuStockList = skuStockList.stream().filter(e -> e.getAvailableQuantity() != null && e.getAvailableQuantity() > 0).collect(Collectors.toList());

            Map<Integer, Integer> warehouseNoStockMap = skuStockList.stream().collect(Collectors.toMap(e -> e.getWarehouseNo().intValue(), WarehouseSkuInventoryPageResp::getAvailableQuantity, (v1, v2) -> v1));

            for (Integer needInsertWarehouseNo : warehouseNoList) {
                Integer availableQuantity = warehouseNoStockMap.get(needInsertWarehouseNo);
                if (availableQuantity == null) {
                    continue;
                }

                ProductStockChangeDTO productStockChangeDTO = new ProductStockChangeDTO();
                productStockChangeDTO.setTenantId(tenantId);
                productStockChangeDTO.setSkuId(skucode2skuIdMap.get(skucode));
                productStockChangeDTO.setSkuCode(skucode);
                productStockChangeDTO.setWarehouseNo(needInsertWarehouseNo);
                productStockChangeDTO.setNewOnlineQuantity(availableQuantity);
                productStockChangeDTO.setGoodsType(GoodsTypeEnum.QUOTATION_TYPE.getCode());

                // 4. 初始化库存预警记录
                productStockForewarningReportService.updateStockWarn(productStockChangeDTO);
            }
        }

    }

    @Data
    public static class InitDataDTO implements Serializable {

        /**
         * 租户列表
         */
        private List<Long> tenantIdList;

        /**
         * 是否需要初始化销量表
         */
        private boolean needInitSaleQty;

        /**
         * 是否需要初始化库存表
         */
        private boolean needInitStockQty;

    }
}
