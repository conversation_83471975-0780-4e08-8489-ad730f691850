package com.cosfo.manage.common.task.jindie;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.common.context.OrderStatusEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.jindie.config.JinDieConfig;
import com.cosfo.manage.jindie.facade.JindieFacade;
import com.cosfo.manage.open.model.po.OpenDocCodeMapping;
import com.cosfo.manage.open.repository.OpenDocCodeMappingRepository;
import com.cosfo.manage.order.model.dto.OrderQueryDTO;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.kingdee.service.data.entity.SalOrderSaveReq;
import com.kingdee.service.data.entity.SaveReply;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class JinDieOrderSync extends XianMuJavaProcessorV2 {
    @Autowired
    private OrderBusinessService orderBusinessService;
    @Autowired
    private OpenDocCodeMappingRepository mappingRepository;

    @Autowired
    private JinDieConfig jinDieConfig;

    @Autowired
    private JindieFacade jindieFacade;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始进行jindie - OrderSync任务,instanceParameters={},jobParameters={}", JSON.toJSONString(context.getInstanceParameters()),JSON.toJSONString(context.getJobParameters()));

        if (ObjectUtil.isNull(context)) {
            return new ProcessResult (true);
        }

        List<String> failedOrderIds = new LinkedList<> ();

        OrderQueryDTO orderQueryDTO = new OrderQueryDTO ();
        orderQueryDTO.setTenantId (jinDieConfig.getZcwTenantId ());
        orderQueryDTO.setTimeQueryType (1);//下单时间
        orderQueryDTO.setOrderStatusList (Lists.newArrayList(OrderStatusEnum.DELIVERING.getCode(), OrderStatusEnum.OUT_OF_STORAGE.getCode(), OrderStatusEnum.FINISHED.getCode()));

        LocalDateTime start = LocalDateTime.now().minusDays(1);
        LocalDateTime end = LocalDateTime.now();
        orderQueryDTO.setStartTime (start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderQueryDTO.setEndTime (end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        orderQueryDTO.setPageSize (100);
        if (!StringUtils.isEmpty (context.getInstanceParameters())) {
            Map<String, String> map = JSON.parseObject (context.getInstanceParameters  (), Map.class);
            String orderNo = map.get ("orderNo");
            String orderNos = map.get ("orderNos");
            String startTime = map.get ("startTime");
            String endTime = map.get ("endTime");
            String status = map.get ("statusList");

            if(StringUtils.isNotBlank (orderNo)){
                orderQueryDTO.setOrderNo (orderNo);
                orderQueryDTO.setOrderStatusList (null);
            }
            if(StringUtils.isNotBlank (orderNos)){
                orderQueryDTO.setOrderNos (Splitter.on(",").splitToStream(orderNos).map(String::valueOf).collect(Collectors.toList()));
                orderQueryDTO.setOrderStatusList (null);
            }
            if(StringUtils.isNotBlank (startTime)){
                orderQueryDTO.setStartTime (startTime);
            }
            if(StringUtils.isNotBlank (endTime)){
                orderQueryDTO.setEndTime (endTime);
            }
            if(StringUtils.isNotBlank (status)){
                orderQueryDTO.setOrderStatusList (Splitter.on(",").splitToStream(status).map(Integer::valueOf).collect(Collectors.toList()));
            }
        }
        Map<String, String> unitMap = jindieFacade.unitMeasureUnitMap ();
        if (CollectionUtil.isEmpty (unitMap)){
            log.info ("金蝶订单数据传输暂停，没有单位返回");
            return new ProcessResult (true);
        }
        pageDeal(orderQueryDTO, failedOrderIds,unitMap);

        if (CollectionUtil.isEmpty (failedOrderIds)) {
            return new ProcessResult(true);
        } else {
            return new ProcessResult(InstanceStatus.PARTIAL_FAILED, String.join(",", failedOrderIds));
        }

    }
    private void pageDeal(OrderQueryDTO orderQueryDTO , List<String> failedItemIds,Map<String, String> unitMap) {
        boolean lastPage = false;
        int pageIndex = 1;
        Long total = null;

        PageInfo<OrderVO> page;
        while(!lastPage){
            orderQueryDTO.setPageIndex (pageIndex);
            page = orderBusinessService.list (orderQueryDTO, orderQueryDTO.getTenantId ());
            batchDeal(page.getList (),failedItemIds,unitMap);
            lastPage = page.isIsLastPage ();
            total  = page.getTotal ();
            pageIndex = pageIndex + 1;
        }
        log.info("JinDieOrderSync - page.size={}", total);
    }

    private void batchDeal(List<OrderVO> list, List<String> failedItemIds,Map<String, String> unitMap) {
        if(CollectionUtil.isEmpty (list)){
            return;
        }
        for (OrderVO order : list) {
            OpenDocCodeMapping mapping = mappingRepository.queryByDocCode (order.getOrderNo (), order.getTenantId (), 1, 1);
            if(mapping == null){
                mapping = new OpenDocCodeMapping();
            }else{
                if(StringUtils.isNotBlank (mapping.getOutCode ())){
                    continue;
                }
            }
            mapping.setTenantId(order.getTenantId ());
            mapping.setDocType(1);
            mapping.setOpenType(1);
            mapping.setDocCode(order.getOrderNo ());
            try {

                SalOrderSaveReq req = jindieFacade.buildSalOrderSaveReq (order,unitMap);
                mapping.setReq(JSON.toJSONString (req));
                SaveReply saveReply = jindieFacade.salOrderSaveCall (req);
                mapping.setResp(JSON.toJSONString (saveReply));
                if(CollectionUtil.isNotEmpty (saveReply.getIds ())){
                    mapping.setOutCode(saveReply.getIds ().get (0));
                }else {
                    failedItemIds.add(String.valueOf(order.getOrderId ()));
                }
            } catch (Exception exception) {
                log.error("JinDieOrderSync error, order:{}", JSON.toJSONString(order),exception);
                mapping.setResp(JSON.toJSONString (exception));
                failedItemIds.add(String.valueOf(order.getOrderId ()));
            }
            mappingRepository.saveOrUpdate (mapping);
        }
    }

}
