package com.cosfo.manage.common.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.manage.order.model.dto.BatchDeliveryNotifyDTO;
import com.cosfo.manage.order.model.vo.OrderBatchNotifyVO;
import com.cosfo.manage.order.service.OrderBusinessService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 系统自动批量通知配送
 *
 * @author: xiaowk
 * @time: 2025/5/15 上午10:51
 */
@Component
@Slf4j
public class AutoBatchDeliveryNotifyTask extends XianMuJavaProcessorV2 {

    @Resource
    private OrderBusinessService orderBusinessService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String instanceParameters = context.getInstanceParameters();
        String jobParameters = context.getJobParameters();

        log.info("AutoBatchDeliveryNotifyTask 执行任务入参：jobParameters={}, instanceParameters={}", jobParameters, instanceParameters);

        List<InitDataDTO> paramList = new ArrayList<>();
        if (!StringUtils.isBlank(instanceParameters)) {
            paramList = JSONArray.parseArray(instanceParameters, InitDataDTO.class);

        } else if (!StringUtils.isBlank(jobParameters)) {
            paramList = JSONArray.parseArray(jobParameters, InitDataDTO.class);

        } else {
            log.info("任务入参为空，不处理");
        }

        for (InitDataDTO initDataDTO : paramList) {
            if(initDataDTO.getTenantId() == null || CollectionUtils.isEmpty(initDataDTO.getWarehouseNos())){
                continue;
            }

            try {
                BatchDeliveryNotifyDTO batchDeliveryNotifyDTO = new BatchDeliveryNotifyDTO();
                batchDeliveryNotifyDTO.setWarehouseNoList(initDataDTO.getWarehouseNos());
                batchDeliveryNotifyDTO.setType(0);
                int hour = initDataDTO.getHour() != null ? initDataDTO.getHour() : 17;
                int minute = initDataDTO.getMinute() != null ? initDataDTO.getMinute() : 0;
                batchDeliveryNotifyDTO.setCutOffTime(LocalDateTime.of(LocalDate.now(), LocalTime.of(hour, minute)));

                log.info("执行请求：{}", JSONObject.toJSON(batchDeliveryNotifyDTO));
                OrderBatchNotifyVO orderBatchNotifyVO = orderBusinessService.autoBatchDeliveryNotify(batchDeliveryNotifyDTO, initDataDTO.getTenantId());
                log.info("执行结果：{}", JSONObject.toJSON(orderBatchNotifyVO));
            } catch (Exception e) {
                log.error("系统自动批量通知仓库配送异常，", e);
            }
        }


        return new ProcessResult(true);
    }


    @Data
    public static class InitDataDTO implements Serializable {
        /**
         * 租户id
         */
        private Long tenantId;

        /**
         * 自营仓列表
         */
        private List<Integer> warehouseNos;

        /**
         * 小时
         */
        private Integer hour;

        /**
         * 分钟
         */
        private Integer minute;
    }
}
