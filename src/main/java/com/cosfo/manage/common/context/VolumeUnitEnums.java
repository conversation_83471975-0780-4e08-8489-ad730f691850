package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/3
 */
@Getter
@AllArgsConstructor
public enum VolumeUnitEnums {
    /**
     * cm³
     */
    CUBIC_CENTIMETER(0,"cm³", 1L),
    /**
     * L
     */
    L(1,"L", 1000L),
    /**
     * m³
     */
    SQUARE_METER(2,"m³", 1000000L);
    private Integer type;
    private String desc;
    private Long volumeUnit;

    public static Long getType(String desc) {
        for (VolumeUnitEnums volumeUnitEnums : VolumeUnitEnums.values()) {
            if (volumeUnitEnums.desc.equals(desc)) {
                return volumeUnitEnums.getVolumeUnit();
            }
        }

        return null;
    }

    public static String getDescByVolumeUnit(Long volumeUnit) {
        if(volumeUnit == null){
            return "";
        }
        for (VolumeUnitEnums volumeUnitEnums : VolumeUnitEnums.values()) {
            if (volumeUnitEnums.volumeUnit.equals(volumeUnit)) {
                return volumeUnitEnums.getDesc();
            }
        }

        return "";
    }
}
