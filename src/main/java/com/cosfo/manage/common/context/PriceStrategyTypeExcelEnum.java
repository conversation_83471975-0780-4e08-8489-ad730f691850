package com.cosfo.manage.common.context;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collection;
import java.util.List;

@Getter
@AllArgsConstructor
public enum PriceStrategyTypeExcelEnum {

    /**
     * 按成本价百分比上浮
     */
    COST_PRICE_ADD_PERCENTAGE(0, "供应价上浮%"),

    COST_PRICE_ADD_FIXED(1,"供应价加价"),

    ASSIGN(2,"自定义固定价"),

    ;



    private Integer code;
    private String desc;


    public static PriceStrategyTypeExcelEnum getByDesc(String desc) {
        for (PriceStrategyTypeExcelEnum type : PriceStrategyTypeExcelEnum.values()) {
            if (type.desc.equals(desc)) {
                return type;
            }
        }

        return null;
    }
}
