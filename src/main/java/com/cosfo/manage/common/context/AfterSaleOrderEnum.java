package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class AfterSaleOrderEnum {
    /**
     * 售后订单状态
     */
    @Getter
    @AllArgsConstructor
    public enum Status{
        /**
         * 1-待审核
         */
        WAITTING_AUDIT(1,"待审核"),

        /**
         * 2-处理中
         */
        DEALING(2,"处理中"),
        /**
         * 3-退款中
         */
        REFUNDING(3,"退款中"),
        /**
         * 4-已成功
         */
        SUCCESS(4,"已成功"),
        /**
         * 5-已拒绝
         */
        REJECT(5,"已拒绝"),
        /**
         * 6-已取消
         */
        CANCEL(6,"已取消"),
        /**
         * 8-待退款
         */
        WAITTING_REFUND(8,"待退款");

        private Integer code;
        private String desc;

        /**
         * get status
         * @param desc
         * @return
         */
        public static Status getStatus(String desc) {
            for (Status status : Status.values()) {
                if (status.desc.equals(desc)) {
                    return status;
                }
            }

            return null;
        }

        /**
         * get desc
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (Status status : Status.values()) {
                if (status.code.equals(code)) {
                    return status.getDesc();
                }
            }

            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum SaleType{
        /**
         * 0-已到货售后
         */
        ON(0,"已到货售后"),

        /**
         * 1-未到货退款
         */
        OFF(1,"未到货退款");

        private Integer code;
        private String desc;

        /**
         * get status
         * @param desc
         * @return
         */
        public static SaleType getStatus(String desc) {
            for (SaleType type : SaleType.values()) {
                if (type.desc.equals(desc)) {
                    return type;
                }
            }

            return null;
        }

        /**
         * get desc
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (SaleType type : SaleType.values()) {
                if (type.code.equals(code)) {
                    return type.getDesc();
                }
            }

            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum SaleServiceType{
        /**
         * 1-退款
         */
        REFUND(1,"退款"),

        /**
         * 2-录入账单
         */
        ENTRY(2,"录入账单"),

        /**
         * 3-退货退款
         */
        REFUND_TM(3,"退货退款"),

        /**
         * 4-退货录入账单
         */
        REFUND_TE(4,"退货录入账单"),

        /**
         * 5-换货
         */
        EXCHANGE(5,"换货"),

        /**
         * 6-补发
         */
        REPLACE(6,"补发");

        private Integer code;
        private String desc;

        /**
         * get status
         * @param desc
         * @return
         */
        public static SaleServiceType getStatus(String desc) {
            for (SaleServiceType type : SaleServiceType.values()) {
                if (type.desc.equals(desc)) {
                    return type;
                }
            }

            return null;
        }

        /**
         * get desc
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (SaleServiceType type : SaleServiceType.values()) {
                if (type.code.equals(code)) {
                    return type.getDesc();
                }
            }

            return null;
        }
    }
}
