package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: xiaowk
 * @date: 2023/3/19 下午5:43
 */
public interface TenantConfigEnum {

    @Getter
    @AllArgsConstructor
    enum TenantConfig {
        /**
         * 无库存商品展示规则
         */
        GOODS_SHOW_RULE_OUTOFSTOCK("goods_show_rule", "无库存商品展示规则 0-收起 1-展开 2-隐藏", "0"),

        /**
         * 品牌客服电话配置
         */
        CUSTOMER_PHONE("customer_phone", "品牌客服电话配置", ""),

        /**
         * 自营无配送仓订单，配送前门店申请退款审批规则 默认自动同意
         */
        NO_WAREHOUSE_AFTER_SALE_APPROVAL_RULE("no_warehouse_after_sale_approval_rule", "自营无配送仓订单，配送前门店申请退款审批规则 0-自动同意 1-需要审核", "0"),

        /**
         * 自营有配送仓订单，配送前门店申请退款审批规则 默认自动同意
         */
        SELF_WAREHOUSE_AFTER_SALE_APPROVAL_RULE("self_warehouse_after_sale_approval_rule", "自营有配送仓订单，配送前门店申请退款审批规则 0-自动同意 1-需要审核", "0"),
        /**
         * 供应商配送员角色id
         */
        SUPPLIER_DISTRIBUTOR_ROLE_ID("supplier_distributor_role_id", "租户的供应商配送员角色id", ""),


        /**
         * 小程序注册功能 0:关闭 1开启,默认关闭
         */
        REGISTRY_SWITCH("registry_switch", "小程序注册功能配置 0:关闭 1开启", "0"),
        /**
         * 无库存商品展示规则
         */
        @Deprecated
        ITEM_PRICE_RULE("item_price_rule", "商品价格倒挂规则配置 0-以供应价售卖 1-已自定义价售卖 2-自动下架", "0"),

        /**
         * 订单通知仓库配送后，是否自动生成出库任务开关 0:关闭 1:开启,默认关闭
         */
        AUTO_OUTBOUND_TASK4ORDER_SWITCH("auto_outbound_task4order_switch", "订单通知仓库配送后，是否自动生成出库任务开关 0:关闭 1:开启", "0"),

        /**
         * 私采率
         */
        PRIVATE_PROCUREMENT("private_procurement", "私采率", "50"),

        /**
         * 配送完成回调，是否自动生成待审核售后单 0:关闭 1:开启(自动生成)
         */
        AUTO_CREATE_RESEND_AFTER_SALE_RULE("auto_create_resend_after_sale_rule", "配送完成回调，是否自动生成补发单售后单 0:关闭 1:开启(自动生成)", "1"),

        /**
         * 库存预警配置
         */
        STOCK_WARNING_CONFIG_KEY("stock_warning_config", "库存预警配置", "[{\"day\":7,\"isActive\":true,\"waringType\":1}]"),

        /**
         * 临保配置
         */
        WMS_APPROACHING_SHELF_LIFE_CONFIG("wms_approaching_shelf_life_config", "货品临保配置", "{\"type\":\"percentage\",\"value\":\"90\"}"),

        /**
         *存货周转临期配置
         */
        WMS_APPROACHING_MATURITY_CONFIG("wms_approaching_maturity_config", "存货周转临期配置", "{\"type\":\"percentage\",\"value\":\"90\"}"),

        /**
         * 租户客户经理配置
         */
        TENANT_CUSTOMER_MANAGER("tenant_customer_manager", "租户客户经理配置", "服务热线4001-619-166"),

        /**
         * 订单是否支持改单 0-不支持 1-支持
         */
        SUPPORT_CHANGE_ORDER_RULE("support_change_order_rule", "订单支持改单", "0"),

        /**
         * 租户品牌名称
         */
        TENANT_BRAND_NAME("tenant_brand_name", "租户品牌名称", ""),
        /**
         * 省心定开关0:关闭 1开启
         */
        SAVE_WORRY("save_worry", "省心定开关0:关闭 1开启", "0"),

        /**
         * 采购单报表导出格式
         */
        PURCHASE_ORDER_EXPORT_FORMAT("purchase_order_export_format", "采购单报表导出格式 0:标准格式 1:打印格式", "0"),

        /**
         * 门店下单有效期倒计时
         */
        PLACE_ORDER_PERMISSION_EXPIRY_TIME("place_order_permission_expiry_time", "门店下单有效期倒计时", "14"),

        /**
         * 代仓品修改销售库存按钮是否打开开关
         * 0:关闭 不可修改
         * 1:打开 可以修改
         */
        AGENT_WAREHOUSE_SKU_CHANGE_STOCK_SWITCH("agent_warehouse_sku_change_stock_switch", "代仓品修改销售库存按钮是否打开 0:关闭 1:打开", "0"),

        /**
         * 门店进销存是否打开
         */
        STORE_INVENTORY_SWITCH("store_inventory_switch", "门店进销存是否打开 0:关闭 1:打开", "0"),


        /**
         * 采购应付-采购入库单明细导出格式
         */
        PURCHASE_INBOUND_ORDER_DETAIL_EXPORT_FORMAT("purchase_inbound_order_detail_export_format", "采购应付-采购入库单明细导出格式 0:标准格式 1:打印格式", "0"),

        /**
         * 订单税率，百分位，默认为0
         */
        ORDER_TAX_RATE("order_tax_rate", "订单税率，百分位，默认为0", "0"),
        ;
        /**
         * 租户配置key
         */
        private String configKey;

        /**
         * 租户配置描述
         */
        private String configDesc;

        /**
         * 租户配置key默认值
         */
        private String defaultValue;


        public static TenantConfig getConfigEnum(String configKey) {
            for (TenantConfig tenantConfigEnum : TenantConfig.values()) {
                if (tenantConfigEnum.configKey.equals(configKey)) {
                    return tenantConfigEnum;
                }
            }

            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    enum GoodsShowRule {
        /**
         * 收起
         */
        FOLD("0", "收起"),
        /**
         * 展开
         */
        EXPAND("1", "展开"),

        /**
         * 隐藏
         */
        HIDE("2", "隐藏");

        /**
         * 编码
         */
        private String code;
        /**
         * 描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    enum AfterSaleApprovalRule {
        /**
         * 自动同意
         */
        AUTO_APPROVED("0", "自动同意"),
        /**
         * 需要审核
         */
        NEED_AUDIT("1", "需要审核");

        /**
         * 编码
         */
        private String code;
        /**
         * 描述
         */
        private String desc;

        public static AfterSaleApprovalRule getRule(String code) {
            for (AfterSaleApprovalRule afterSaleApprovalRule : AfterSaleApprovalRule.values()) {
                if (afterSaleApprovalRule.code.equals(code)) {
                    return afterSaleApprovalRule;
                }
            }

            return null;
        }
    }
//    @Getter
//    @AllArgsConstructor
//    enum ItemPriceRule {
//        USE_COST_PRICE("0", "以供应价售卖"),
//        USE_ITEM_PRICE("1", "以自定义价售卖"),
//        AUTO_SOLD_OUT("2", "自动下架"),
//        ;
//
//        /**
//         * 编码
//         */
//        private String code;
//        /**
//         * 描述
//         */
//        private String desc;
//
//        public static ItemPriceRule getRule(String code) {
//            for (ItemPriceRule rule : ItemPriceRule.values()) {
//                if (rule.code.equals(code)) {
//                    return rule;
//                }
//            }
//
//            return null;
//        }
//    }

}
