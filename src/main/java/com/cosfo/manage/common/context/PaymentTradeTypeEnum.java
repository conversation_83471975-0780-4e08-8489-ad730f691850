package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2022/12/19 16:54
 */
@Getter
@AllArgsConstructor
public enum PaymentTradeTypeEnum {

    /**
     * 微信小程序（直连）
     */
    JSAPI(1, "JSAPI", "微信直连"),

    /**
     * 微信公众号
     */
    T_JSAPI(1, "T_JSAPI", "汇付-公众号微信"),

    /**
     * 微信小程序
     */
    T_MINIAPP(1, "T_MINIAPP", "汇付-小程序微信"),

    /**
     * 微信小程序插件
     */
    HF_WECHAT_PLUGIN(1, "HF_WECHAT_PLUGIN", "汇付-小程序插件"),

    /**
     * 账期
     */
    BILL(2, "BILL", "账期"),

    /**
     * 余额
     */
    BALANCE(3, "BALANCE", "余额"),

    /**
     * 支付宝正扫
     */
    A_NATIVE(4, "A_NATIVE", "汇付-支付宝"),


    /**
     * 无需支付
     */
    ZERO_PRICE(5, "ZERO_PRICE", "无需支付"),

    /**
     * 线下支付
     */
    OFFLINE_PAY(6, "OFFLINE_PAY", "线下支付"),

    /**
     * 非现金支付
     */
    NON_CASH(7, "NON_CASH_PAY", "非现金支付"),

    /**
     * 组合支付
     */
    COMBINED_PAY(8, "COMBINED_PAY", "组合支付"),
    ;


    /**
     * 状态类型编码
     */
    private Integer payType;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 详细描述
     */
    private String detailDesc;


    public static PaymentTradeTypeEnum getPaymentTradeType(String desc){
        for (PaymentTradeTypeEnum payTypeEnum : PaymentTradeTypeEnum.values()){
            if(payTypeEnum.getDesc().equals(desc)){
                return payTypeEnum;
            }
        }
        return null;
    }

    /**
     * 根据tradeType返回支付类型
     * @param desc
     * @return
     */
    public static Integer getPayTypeByTradeType(String desc) {
        for (PaymentTradeTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type.getPayType();
            }
        }
        return null;
    }
    public static List<String> withoutDiscountsList(){
        List<String> list = new ArrayList<> ();
        list.add (JSAPI.getDesc ());
        list.add (T_JSAPI.getDesc ());
        list.add (T_MINIAPP.getDesc ());
        list.add (HF_WECHAT_PLUGIN.getDesc ());
        list.add (BALANCE.getDesc ());
        list.add (A_NATIVE.getDesc ());
        list.add (BILL.getDesc ());
        return list;
    }
}
