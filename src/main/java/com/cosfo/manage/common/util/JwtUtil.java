package com.cosfo.manage.common.util;

import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/27
 */
public class JwtUtil {

    private final static String key = "COSFO_MALL";
    private final static String PREFIX_FLAG = "h5mall_";

    /**
     * 根据租户生成token
     *
     * @param tenantId
     * @return
     */
    public static String createToken(Long tenantId){
        Map<String,Object> payload = new HashMap<String,Object>();
        //载荷
        payload.put("tenantId", tenantId);
        String token = JWTUtil.createToken(payload, key.getBytes());

        return PREFIX_FLAG + token;
    }

    public static Long verifyToken(String token){
        JWT jwt = JWTUtil.parseToken(token);
        Long tenantId = Long.valueOf((Integer)jwt.getPayload("tenantId"));
        return tenantId;
    }
}
