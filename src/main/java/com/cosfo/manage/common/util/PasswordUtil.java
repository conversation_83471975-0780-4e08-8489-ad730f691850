package com.cosfo.manage.common.util;

import cn.hutool.core.util.StrUtil;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/5/8 18:22
 */
public class PasswordUtil {

    public static String createPassword(String phone) {
        // 获取年份
        String year = TimeUtils.changeDate2String(new Date(), TimeUtils.FORMAT_YEAR_ONLY);
        // 获取手机后四位
        String substring = RandomStringUtils.randomNumeric(4);
        if(StrUtil.isNotBlank(phone)) {
            substring = phone.substring(phone.length() - NumberConstants.FOUR);
        }
        StringBuffer stringBuffer = new StringBuffer(year).append(StringConstants.CHARACTER).append(substring);
        // 生成随机四位大小写字符串
        String randomStr = RandomStringUtils.randomAlphabetic(4);
        while (!StringUtils.checkAccountPassword(stringBuffer.toString() + randomStr)) {
            randomStr = RandomStringUtils.randomAlphabetic(4);
        }
        stringBuffer.append(randomStr);
        return stringBuffer.toString();
    }
}
