package com.cosfo.manage.common.util.binlog.impl;

import cn.hutool.core.lang.Pair;
import com.cosfo.manage.common.context.binlog.BinlogEventEnum;
import com.cosfo.manage.common.model.bo.DtsModelBO;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.binlog.DbTableDmlService;
import com.cosfo.manage.common.util.binlog.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component("tradeDmlServiceImpl")
public class TradeDmlServiceImpl implements DbTableDmlService {

    @Override
    public String getTableDmlName() {
        return getTableName();
    }

    @Override
    public void tableDml(DtsModelBO dtsModelBo) {
        if (!Objects.equals(BinlogEventEnum.UPDATE.getEvent(), dtsModelBo.getType())) {
            return;
        }

        List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData(dtsModelBo);
        for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
            Map<String, String> dataMap = pair.getKey();
            Map<String, String> oldMap = pair.getValue();
            // 生成交易汇总
            generateTradeSummary(dataMap, oldMap);
            // 生成销量榜
            generateMarketItemOrderSummary(Long.valueOf(dataMap.get("id")));
        }
    }

    /**
     * 生成交易汇总数据
     * @param dataMap
     * @param oldMap
     */
    protected void generateTradeSummary(Map<String, String> dataMap, Map<String, String> oldMap) {
        try {
            if (dataMap == null || oldMap == null) {
                log.info("dataMap or oldMap is empty, no processing is done", new ProviderException("DTS data update event with invalid data"));
                return;
            }
            String changedFiled = getChangedFiled();
            String oldStatus = oldMap.get(changedFiled);
            if (StringUtils.isBlank(oldStatus)) {
                log.info("Status has not changed, no processing is done");
                return;
            }
            // Extract status and other required data
            Integer status = Integer.valueOf(dataMap.get(changedFiled));
            if (shouldGenerateTradeSummary(status)) {
                String id = dataMap.get("id");
                Long businessId = Long.valueOf(id);
                // Common logic for generating trade summaries
                generateTradeSummaryForBusiness(businessId);
            }
        } catch (Exception e) {
            log.error("生成交易汇总数据失败", e);
        }
    }


    // 根据条件是否生成交易数据汇总
    protected boolean shouldGenerateTradeSummary(Integer status) {
        return false;
    }

    // 生成交易数据汇总
    protected void generateTradeSummaryForBusiness(Long businessId) {

    }

    // 生成销量榜
    protected void generateMarketItemOrderSummary(Long businessId) {

    }

    // 获取表名
    protected String getTableName() {
        return null;
    }

    // 获取变更字段
    protected String getChangedFiled() {
        return null;
    }
}

