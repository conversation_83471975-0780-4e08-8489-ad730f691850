package com.cosfo.manage.common.util.transfer;

import com.cosfo.manage.market.model.dto.MarketClassificationDTO;
import com.cosfo.manage.market.model.po.MarketClassification;
import com.cosfo.manage.market.model.vo.MarketClassificationVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/10 10:23
 */
public class MarketClassificationTransferUtil {

    /**
     * VO转DTO
     * @param marketClassificationVO
     * @return
     */
    public static MarketClassificationDTO toMarketClassificationDTO(MarketClassificationVO marketClassificationVO) {
        MarketClassificationDTO marketClassificationDTO = MarketClassificationDTO.builder()
                .id(marketClassificationVO.getId())
                .name(marketClassificationVO.getName())
                .parentId(marketClassificationVO.getParentId())
                .sort(marketClassificationVO.getSort())
                .icon(marketClassificationVO.getIcon()).build();
        return marketClassificationDTO;
    }

    /**
     * VO集合转DTO集合
     * @param marketClassificationVOList
     * @return
     */
    public static List<MarketClassificationDTO> toMarketClassificationDTO(List<MarketClassificationVO> marketClassificationVOList) {
        return marketClassificationVOList.stream().map(MarketClassificationTransferUtil::toMarketClassificationDTO).collect(Collectors.toList());
    }

    /**
     * DTO转PO
     * @param marketClassificationDTO
     * @return
     */
    public static MarketClassification toMarketClassification(MarketClassificationDTO marketClassificationDTO) {
        MarketClassification marketClassification = MarketClassification.builder()
                .id(marketClassificationDTO.getId())
                .tenantId(marketClassificationDTO.getTenantId())
                .name(marketClassificationDTO.getName())
                .parentId(marketClassificationDTO.getParentId())
                .sort(marketClassificationDTO.getSort())
                .icon(marketClassificationDTO.getIcon()).build();
        return marketClassification;
    }

}
