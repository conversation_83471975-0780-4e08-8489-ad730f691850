package com.cosfo.manage.merchant.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.common.excel.easyexcel.LargeDataSetExporter;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadStatusEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.common.context.TimeTagTypeEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.NumberUtils;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.file.model.po.FileDownloadRecord;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.merchant.convert.MerchantStoreAuditConvertUtil;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderAnalysisDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderAnalysisExcelDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderOverviewDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreItemOrderAnalysis;
import com.cosfo.manage.merchant.repository.MerchantStoreItemOrderAnalysisRepository;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreItemOrderAnalysisService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.report.mapper.MerchantStoreItemOrderAnalysisMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.math.RoundingMode.HALF_UP;

/**
 * @description: 门店商品维度的订货分析业务方法
 * @author: George
 * @date: 2023-06-06
 **/
@Service
@Slf4j
public class MerchantStoreItemOrderAnalysisServiceImpl implements MerchantStoreItemOrderAnalysisService {

//    @Resource
//    private MerchantStoreMapper merchantStoreMapper;
//    @Resource
//    private MerchantStoreGroupMapper merchantStoreGroupMapper;
    @Resource
    private MerchantStoreItemOrderAnalysisRepository merchantStoreItemOrderAnalysisRepository;
    @Resource
    private MerchantStoreItemOrderAnalysisMapper merchantStoreItemOrderAnalysisMapper;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private CommonService commonService;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;

    @Override
    public MerchantStoreItemOrderOverviewDTO queryAuditStoreItemDimensionOverview(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        // 处理查询的参数
        Boolean resultFlag = processQueryParameters(merchantStoreItemOrderAnalysisQueryDTO);
        if (!resultFlag) {
            return MerchantStoreItemOrderOverviewDTO.getDefaultOverview();
        }

        // 离线数据进行sum
        MerchantStoreItemOrderOverviewDTO merchantStoreItemOrderOverviewDTO = merchantStoreItemOrderAnalysisRepository.queryStoreItemOrderSum(merchantStoreItemOrderAnalysisQueryDTO);

        // 查询上周期离线数据进行sum
        String lastTimeTag = TimeUtils.getLastRangeByTimeTagAndType(merchantStoreItemOrderAnalysisQueryDTO.getTimeTag(), merchantStoreItemOrderAnalysisQueryDTO.getType(), "yyyyMMdd");
        merchantStoreItemOrderAnalysisQueryDTO.setTimeTag(lastTimeTag);
        MerchantStoreItemOrderOverviewDTO lastMerchantStoreItemOrderOverviewDTO = merchantStoreItemOrderAnalysisRepository.queryStoreItemOrderSum(merchantStoreItemOrderAnalysisQueryDTO);

        // 再填充一些属性
        populateOverview(merchantStoreItemOrderOverviewDTO, lastMerchantStoreItemOrderOverviewDTO);

        return merchantStoreItemOrderOverviewDTO;
    }

    /**
     * 填充下需要再次计算的值
     * @param dto 概况数据
     */
    private void populateOverview(MerchantStoreItemOrderOverviewDTO dto, MerchantStoreItemOrderOverviewDTO lastDto) {
        BigDecimal averageOrderPeriod = dto.getRecordCounts() == 0 ? BigDecimal.ZERO : NumberUtil.div(dto.getAverageOrderPeriodSum(), dto.getRecordCounts(), 1, HALF_UP);
        BigDecimal lastAverageOrderPeriod = lastDto.getRecordCounts() == 0 ? BigDecimal.ZERO : NumberUtil.div(lastDto.getAverageOrderPeriodSum(), lastDto.getRecordCounts(), 1, HALF_UP);
        dto.setAverageOrderPeriod(averageOrderPeriod);
        dto.setLastAverageOrderPeriod(lastAverageOrderPeriod);
        dto.setLastOrderAmount(lastDto.getOrderAmount());
        dto.setLastOrderPrice(lastDto.getOrderPrice());
        dto.setAverageOrderPeriodUpperPeriod(NumberUtils.calculateChain(lastAverageOrderPeriod, averageOrderPeriod));
        dto.setOrderAmountUpperPeriod(NumberUtils.calculateChain(lastDto.getOrderAmount(), dto.getOrderAmount()));
        dto.setOrderPriceUpperPeriod(NumberUtils.calculateChain(lastDto.getOrderPrice(), dto.getOrderPrice()));
    }

    /**
     * 处理查询的条件
     * @param dto 查询条件
     */
    private Boolean processQueryParameters(MerchantStoreItemOrderAnalysisQueryDTO dto) {
        String timeTag = dto.getTimeTag();
        if (StringUtils.isBlank(timeTag) || Objects.isNull(dto.getType())) {
            throw new ParamsException("请选择查询的时间范围");
        }
        if (Objects.isNull(TimeTagTypeEnum.getDesc(dto.getType()))) {
            throw new ParamsException("选择搜索的时间格式不正确");
        }
        // 给定默认排序
        if (StringUtils.isBlank(dto.getAverageOrderPeriodSort()) && StringUtils.isBlank(dto.getOrderAmountSort()) && StringUtils.isBlank(dto.getOrderPriceSort())
        && StringUtils.isBlank(dto.getLastOrderTimeSort()) && StringUtils.isBlank(dto.getLastOrderAmountSort()) && StringUtils.isBlank(dto.getLastOrderPriceSort())) {
            dto.setAverageOrderPeriodSort("asc");
        }
        Long tenantId = UserLoginContextUtils.getTenantId();
        dto.setTenantId(tenantId);
        if (StringUtils.isNotBlank(dto.getStoreName()) || Objects.nonNull(dto.getStoreType()) || Objects.nonNull(dto.getStoreGroupId())) {
//            List<Long> storeIds = merchantStoreMapper.selectIdListByParam(tenantId, dto.getStoreName(), null, dto.getStoreType(), dto.getStoreGroupId());
            List<Long> storeIds = merchantStoreService.selectIdListByParam(tenantId,null, dto.getStoreName(),  dto.getStoreType(), dto.getStoreGroupId());
            if (CollectionUtils.isEmpty(storeIds)) {
                return Boolean.FALSE;
            }
            dto.setStoreIds(storeIds);
        }
        return Boolean.TRUE;
    }

    @Override
    public PageInfo<MerchantStoreItemOrderAnalysisDTO> listAuditStoreItemDimensionPage(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        // 处理查询的参数
        Boolean resultFlag = processQueryParameters(merchantStoreItemOrderAnalysisQueryDTO);
        if (!resultFlag) {
            return PageInfoHelper.createPageInfo(Lists.newArrayList(), merchantStoreItemOrderAnalysisQueryDTO.getPageSize());
        }

        // 分页查询
        Page<MerchantStoreItemOrderAnalysis> page = PageHelper.startPage(merchantStoreItemOrderAnalysisQueryDTO.getPageIndex(), merchantStoreItemOrderAnalysisQueryDTO.getPageSize());
        merchantStoreItemOrderAnalysisRepository.listByCondition(merchantStoreItemOrderAnalysisQueryDTO);

        // 转换分页参数
        PageInfo<MerchantStoreItemOrderAnalysisDTO> pageInfo = PageInfoConverter.toPageInfo(page, MerchantStoreAuditConvertUtil::toDTO);

        // 填充门店的信息
        populateStoreInfo(pageInfo.getList());
        return pageInfo;
    }

    private void populateStoreInfo(List<MerchantStoreItemOrderAnalysisDTO> merchantStoreItemOrderAnalysisList ) {
        if (CollectionUtils.isEmpty(merchantStoreItemOrderAnalysisList)) {
            return;
        }
        // 查询门店信息
        Long tenantId = UserLoginContextUtils.getTenantId();
        List<Long> storeIds = merchantStoreItemOrderAnalysisList.stream().map(MerchantStoreItemOrderAnalysisDTO::getStoreId).distinct().collect(Collectors.toList());
//        MerchantStoreQueryDTO query = MerchantStoreQueryDTO.builder().tenantId(tenantId).storeIds(storeIds).build();
//        List<MerchantStoreDTO> merchantStoreList = merchantStoreService.selectList(query);
        List<MerchantStoreDTO> merchantStoreList = merchantStoreService.selectList(tenantId, storeIds);
        Map<Long, MerchantStoreDTO> storeInfoMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));

        merchantStoreItemOrderAnalysisList.forEach(el -> {
            MerchantStoreDTO merchantStoreDTO = storeInfoMap.get(el.getStoreId());
            if (Objects.isNull(merchantStoreDTO)) {
                return;
            }
            el.setStoreName(merchantStoreDTO.getStoreName());
            el.setStoreType(merchantStoreDTO.getType());
            el.setGroupName(merchantStoreDTO.getGroupName());
        });
    }

    @Override
    public Boolean exportAuditStoreItemDimension(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        // 处理文件参数
        HashMap<String, String> paramsMap = processFileParameters(merchantStoreItemOrderAnalysisQueryDTO);

        // 处理查询条件
        Boolean resultFlag = processQueryParameters(merchantStoreItemOrderAnalysisQueryDTO);

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.MERCHANT_STORE_ITEM_DIMENSION.getType());
        recordDTO.setTenantId(UserLoginContextUtils.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.MERCHANT_STORE_ITEM_DIMENSION_AUDIT.getFileName());
        recordDTO.setParams(JSONObject.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(merchantStoreItemOrderAnalysisQueryDTO, ee -> {
            // 1、表格处理
            String filePath = null;
            if (!resultFlag) {
                filePath = commonService.exportExcel(Lists.newArrayList(), ExcelTypeEnum.MERCHANT_STORE_ITEM_DIMENSION_AUDIT.getName());
            }else {
                filePath = generateAuditStoreItemDimensionExcel(merchantStoreItemOrderAnalysisQueryDTO);
            }

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


//        // 生成下载记录
//        Long fileDownloadRecordId = processFileDownloadRecord(paramsMap);
//
//        // 处理查询条件
//        Boolean resultFlag = processQueryParameters(merchantStoreItemOrderAnalysisQueryDTO);
//        if (!resultFlag) {
//            commonService.generateAndUploadExcel(Lists.newArrayList(), ExcelTypeEnum.MERCHANT_STORE_ITEM_DIMENSION_AUDIT, fileDownloadRecordId);
//        }
//
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            generateAuditStoreItemDimensionExcel(merchantStoreItemOrderAnalysisQueryDTO, fileDownloadRecordId);
//        });
        return Boolean.TRUE;
    }

    /**
     * 处理文件下载记录
     * @param paramsMap 参数
     */
    private Long processFileDownloadRecord(HashMap<String, String> paramsMap) {
        FileDownloadRecord record = new FileDownloadRecord();
        record.setTenantId(UserLoginContextUtils.getTenantId());
        record.setParams(JSONObject.toJSONString(paramsMap));
        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
        record.setType(FileDownloadTypeEnum.MERCHANT_STORE_ITEM_DIMENSION.getType());
        return fileDownloadRecordService.generateFileDownloadRecord(record);
    }

    private HashMap<String, String> processFileParameters(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        HashMap<String, String> paramsMap = new LinkedHashMap<>(NumberConstants.TEN);
        // 根据时间标签和类型确定搜索的时间范围
        Pair<String, String> timeRangePair = TimeUtils.getRangeByTimeTagAndType(merchantStoreItemOrderAnalysisQueryDTO.getTimeTag(), merchantStoreItemOrderAnalysisQueryDTO.getType(), TimeUtils.FORMAT_STRING);
        paramsMap.put(Constants.QUERY_TIME, timeRangePair.getKey() + StringConstants.SEPARATING_IN_LINE + timeRangePair.getValue());
        // 商品名称
        if (StringUtils.isNotBlank(merchantStoreItemOrderAnalysisQueryDTO.getTitle())) {
            paramsMap.put(Constants.TITLE, merchantStoreItemOrderAnalysisQueryDTO.getTitle());
        }
        // 门店名称
        if (StringUtils.isNotBlank(merchantStoreItemOrderAnalysisQueryDTO.getStoreName())) {
            paramsMap.put(Constants.STORE_NAME, merchantStoreItemOrderAnalysisQueryDTO.getStoreName());
        }
        // 门店类型
        if (Objects.nonNull(merchantStoreItemOrderAnalysisQueryDTO.getStoreType())) {
            String storeType = MerchantStoreEnum.Type.getDesc(merchantStoreItemOrderAnalysisQueryDTO.getStoreType());
            paramsMap.put(Constants.STORE_TYPE, storeType);
        }
        // 门店分组
        if (Objects.nonNull(merchantStoreItemOrderAnalysisQueryDTO.getStoreGroupId())) {
//            MerchantStoreGroup merchantStoreGroup = merchantStoreGroupMapper.selectById(merchantStoreItemOrderAnalysisQueryDTO.getStoreGroupId());
            MerchantStoreGroupPageResultResp merchantStoreGroup = merchantStoreGroupService.queryById(merchantStoreItemOrderAnalysisQueryDTO.getStoreGroupId(),UserLoginContextUtils.getTenantId());
            paramsMap.put(Constants.STORE_GROUPS, Objects.isNull(merchantStoreGroup) ? "" : merchantStoreGroup.getMerchantStoreGroupName());
        }
        return paramsMap;
    }


    /**
     * 实际去生成excel
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     */
    private String generateAuditStoreItemDimensionExcel(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.MERCHANT_STORE_ITEM_DIMENSION_AUDIT.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        LargeDataSetExporter<MerchantStoreItemOrderAnalysis, MerchantStoreItemOrderAnalysisExcelDTO> handler = new LargeDataSetExporter<MerchantStoreItemOrderAnalysis, MerchantStoreItemOrderAnalysisExcelDTO>() {
            @Override
            protected List<MerchantStoreItemOrderAnalysisExcelDTO> convert(MerchantStoreItemOrderAnalysis data) {
                return Lists.newArrayList(MerchantStoreAuditConvertUtil.toExcelDTO(data));
            }
            @Override
            protected void flushData(List<MerchantStoreItemOrderAnalysisExcelDTO> dataList) {
                // 在刷新到excel之前填充一些需要批量查询的信息
                populateExcelDTO(dataList);
                excelWriter.fill(dataList, fillConfig, writeSheet);
            }

            private void populateExcelDTO(List<MerchantStoreItemOrderAnalysisExcelDTO> dataList) {
                List<Long> storeIds = dataList.stream().map(MerchantStoreItemOrderAnalysisExcelDTO::getStoreId).collect(Collectors.toList());
//                MerchantStoreQueryDTO query = MerchantStoreQueryDTO.builder().tenantId(merchantStoreItemOrderAnalysisQueryDTO.getTenantId()).storeIds(storeIds).build();
//                List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreMapper.selectList(query);
                List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreService.selectList(merchantStoreItemOrderAnalysisQueryDTO.getTenantId(), storeIds);
                Map<Long, MerchantStoreDTO> storeInfoMap = merchantStoreDTOS.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));
                dataList.forEach(el -> {
                    MerchantStoreDTO merchantStoreDTO = storeInfoMap.get(el.getStoreId());
                    if (Objects.isNull(merchantStoreDTO)) {
                        return;
                    }
                    el.setStoreName(merchantStoreDTO.getStoreName());
                    el.setStoreGroup(merchantStoreDTO.getGroupName());
                    el.setStoreType(MerchantStoreEnum.Type.getDesc(merchantStoreDTO.getType()));
                });
            }
        };

        merchantStoreItemOrderAnalysisMapper.exportByCondition(merchantStoreItemOrderAnalysisQueryDTO, handler);
        handler.clearData();
        excelWriter.finish();

        return filePath;
        // 上传数据到七牛云
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, ExcelTypeEnum.MERCHANT_STORE_ITEM_DIMENSION_AUDIT, fileDownloadRecordId);
    }
}
