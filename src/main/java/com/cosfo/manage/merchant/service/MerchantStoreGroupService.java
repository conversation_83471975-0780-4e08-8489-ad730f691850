package com.cosfo.manage.merchant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.merchant.model.dto.*;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroup;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroupMapping;
import com.cosfo.manage.merchant.model.vo.MerchantStoreGroupVO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/14
 */
public interface MerchantStoreGroupService {

    /**
     * 门店分组列表
     *
     * @param merchantStoreGroupQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<PageInfo<MerchantStoreGroupVO>> list(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 新增门店分组
     *
     * @param merchantStoreGroupDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult add(MerchantStoreGroupDTO merchantStoreGroupDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 更新门店分组名称
     *
     * @param merchantStoreGroupDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult update(MerchantStoreGroupDTO merchantStoreGroupDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 绑定门店
     *
     * @param merchantStoreGroupDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult bindStore(MerchantStoreGroupDTO merchantStoreGroupDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 导入门店分组
     *
     * @param file
     * @param loginContextInfoDTO
     * @return
     * @throws IOException
     */
    CommonResult input(MultipartFile file, LoginContextInfoDTO loginContextInfoDTO) throws IOException;

    /**
     * 导出门店分组列表
     *
     * @param merchantStoreGroupQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult export(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 删除门店分组
     *
     * @param merchantStoreGroupDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult delete(MerchantStoreGroupDTO merchantStoreGroupDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 门店统计
     *
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult storeStatistical(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 根据门店id批量查询
     * @param tenantId
     * @param storeIds
     * @return
     */
    Map<Long, String> queryBatchByStoreIds(Long tenantId, List<Long> storeIds);

    /**
     * 根据ids查询
     * @param groupIds
     * @return
     */
    List<MerchantStoreGroup> queryBatchByIds(List<Long> groupIds, Long tenantId);

    /**
     * 查询所有
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<List<MerchantStoreGroupVO>> listAll(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询分组的名称
     * @param tenantId
     * @param groupIds
     * @return
     */
    String queryGroupNameByIds(Long tenantId, List<Long> groupIds);


    /**
     * 更新门店分组
     *
     * @param merchantStoreGroupUpdateDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult updateMerchant(MerchantStoreGroupDTO merchantStoreGroupUpdateDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 分页查询
     * @param merchantStoreGroupQueryDto
     * @return
     */
    PageInfo<MerchantStoreGroupVO> pageList(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDto);

    /**
     * 查询
     * @param merchantStoreGroupQueryDto
     * @return
     */
    List<MerchantStoreGroup> list(MerchantStoreGroupQueryDTO merchantStoreGroupQueryDto);

    /**
     * 根据门店id批量查询
     * @param tenantId
     * @param storeIds
     * @return
     */
    List<MerchantStoreGroupInfoDTO> batchQueryByStoreIds(Long tenantId,List<Long> storeIds);

    /**
     * 查询默认分组
     * @param tenantId
     * @return
     */
    MerchantStoreGroupPageResultResp queryDefaultGroup(Long tenantId);

    /**
     * 根据id获取分组数据
     * @param groupId
     * @return
     */
    MerchantStoreGroupPageResultResp queryById(Long groupId, Long tenantId);

    /**
     * 获取门店分组信息
     * @param storeId
     * @param tenantId
     * @return
     */
    MerchantStoreGroupResultResp selectByStoreId(Long storeId, Long tenantId);

    /**
     * 批量获取门店分组信息
     * @param storeIds
     * @param tenantId
     * @return
     */
    List<MerchantStoreGroupResultResp> selectByStoreIds(List<Long> storeIds, Long tenantId);

    /**
     * 条件查询分组信息
     * @param queryDTO
     * @return
     */
    List<MerchantStoreGroupVO> listGroupByParam(MerchantStoreGroupQueryDTO queryDTO);
}
