package com.cosfo.manage.merchant.service.deliveryfeerule;

import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleEditDTO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;

/**
 * <AUTHOR>
 */
public interface DeliveryFeeRuleStrategy {

    /**
     * 运费规则仓类型
     * @return
     */
    Integer type();

    /**
     * 保存运费规则
     * @param feeRule
     * @return
     */
    boolean saveDeliveryFeeRule(MerchantDeliveryFeeRuleEditDTO feeRule);

    /**
     * 获取运费规则
     * @param tenantId
     * @return
     */
    MerchantDeliveryFeeRuleVO getRule(Long tenantId);
}
