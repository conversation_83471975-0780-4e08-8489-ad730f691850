package com.cosfo.manage.merchant.service.deliveryfeerule;

import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleEditDTO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 全局包邮规则
 *
 * @author: xiaowk
 * @date: 2025/2/27 下午1:37
 */
@Service
@Slf4j
public class GlobalDeliveryFeeRuleStrategy implements DeliveryFeeRuleStrategy {

    @Resource
    private CommonFeeRuleHandle commonFeeRuleHandle;

    @Override
    public Integer type() {
        return WarehouseTypeEnum.GLOBAL.getCode();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveDeliveryFeeRule(MerchantDeliveryFeeRuleEditDTO feeRule) {
        // 铺平规则
        List<MerchantDeliveryFeeRuleEditDTO> roleList = commonFeeRuleHandle.unFoldRule(feeRule);
        //删除例外规则
        commonFeeRuleHandle.removeSpecialRule(feeRule.getTenantId(), type(), roleList.stream().map(MerchantDeliveryFeeRuleEditDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()));
        List<Long> allItemIds = commonFeeRuleHandle.queryAllItemIds(feeRule.getTenantId(), roleList);
        for (int i = 0; i < roleList.size(); i++) {
            commonFeeRuleHandle.handleCommonRule(roleList.get(i), allItemIds, i);
        }
        return true;
    }

    @Override
    public MerchantDeliveryFeeRuleVO getRule(Long tenantId) {
        MerchantDeliveryFeeRuleVO result = commonFeeRuleHandle.handleCommonRule(tenantId, type());
        return result;
    }
}
