package com.cosfo.manage.merchant.service;

import com.cosfo.manage.merchant.model.dto.MerchantDeliveryAddressResultDTO;
import com.cosfo.manage.merchant.model.po.MerchantAddress;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantDeliveryAddressResultResp;

import java.util.List;

/**
 * @description: 门店地址服务层
 * @author: <PERSON>
 * @date: 2023-05-14
 **/
public interface MerchantAddressService {

    /**
     * 根据门店id批量查询
     * @param tenantId
     * @param storeIds
     * @return
     */
    List<MerchantDeliveryAddressResultDTO> selectByStoreIds(Long tenantId, List<Long> storeIds);

    /**
     * 根据门店id批量查询
     * @param tenantId
     * @param storeIds
     * @return
     */
    List<MerchantAddressResultResp> selectAddressListByStoreIds(Long tenantId, List<Long> storeIds);

    /**
     * 根据门店id查询
     * @param tenantId
     * @param storeId
     * @return
     */
    MerchantAddressResultResp selectByStoreId(Long tenantId, Long storeId);

    /**
     * 根据地址查询门店
     *
     * @param province
     * @param city
     * @param area
     * @return
     */
    List<MerchantAddressResultResp> queryStore(String province, String city, String area, Long tenantId);

    /**
     * 根据地址查询门店
     *
     * @param cityList
     * @param tenantId
     * @return
     */
    List<MerchantAddressResultResp> queryByCityList(List<String> cityList, Long tenantId);

    /**
     * 查询所有门店城市
     *
     * @param tenantId
     * @return
     */
    List<String> queryAllStoreCity(Long tenantId);

    /**
     * 根据租户id批量查询省市区
     * @param tenantId
     * @return
     */
    List<String> selectByTenantId(Long tenantId);

    /**
     * 根据租户id批量查询storeId , 省市区
     * @param tenantId
     * @return
     */
    List<MerchantAddressResultResp> listStoreIdAndAddress(Long tenantId);
}
