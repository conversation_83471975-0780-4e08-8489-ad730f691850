package com.cosfo.manage.merchant.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.common.excel.easyexcel.LargeDataSetExporter;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.DimensionTypeEnum;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadStatusEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.file.model.po.FileDownloadRecord;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.merchant.convert.MerchantStoreAuditConvertUtil;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportion;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionAnalysisDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionAnalysisExcelDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionPieChartDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderProportionAnalysis;
import com.cosfo.manage.merchant.repository.MerchantStoreOrderProportionAnalysisRepository;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreOrderProportionAnalysisService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.report.mapper.MerchantStoreOrderProportionAnalysisMapper;
import com.cosfo.manage.report.model.chart.PieChart;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import org.apache.commons.io.FileUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.cosfo.common.util.TimeUtils.FORMAT_STRING;

/**
 * @description: 门店订货占比分析
 * @author: George
 * @date: 2023-06-09
 **/
@Service
@Slf4j
public class MerchantStoreOrderProportionAnalysisServiceImpl implements MerchantStoreOrderProportionAnalysisService {

//    @Resource
//    private MerchantStoreMapper merchantStoreMapper;
//    @Resource
//    private MerchantStoreGroupMapper merchantStoreGroupMapper;
    @Resource
    private MerchantStoreOrderProportionAnalysisRepository merchantStoreOrderProportionAnalysisRepository;
    @Resource
    private MerchantStoreOrderProportionAnalysisMapper merchantStoreOrderProportionAnalysisMapper;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    @Resource
    private CommonService commonService;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;

    @Override
    public MerchantStoreOrderProportionPieChartDTO queryProportionPieChart(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
        // 处理查询条件
        Boolean resultFlag = processQueryParameters(merchantStoreOrderProportionQueryDTO);
        if (!resultFlag) {
            return new MerchantStoreOrderProportionPieChartDTO();
        }

        // 占比数据集合
        List<MerchantStoreOrderProportion> merchantStoreOrderProportionList = merchantStoreOrderProportionAnalysisRepository.querySumByCondition(merchantStoreOrderProportionQueryDTO);
        if (CollectionUtils.isEmpty(merchantStoreOrderProportionList)) {
            return new MerchantStoreOrderProportionPieChartDTO();
        }

        // 构建饼图数据
        return buildPieChart(merchantStoreOrderProportionList, merchantStoreOrderProportionQueryDTO);
    }

    private MerchantStoreOrderProportionPieChartDTO buildPieChart(List<MerchantStoreOrderProportion> merchantStoreOrderProportionList, MerchantStoreOrderProportionQueryDTO queryDTO) {
        MerchantStoreOrderProportionPieChartDTO pieChartDTO = new MerchantStoreOrderProportionPieChartDTO();
        List<PieChart> orderAmountPieCharts = Lists.newArrayList();
        List<PieChart> orderPricePieCharts = Lists.newArrayList();
        Integer dimension = queryDTO.getDimension();
        for (MerchantStoreOrderProportion proportion : merchantStoreOrderProportionList) {
            PieChart orderAmountPieChart = new PieChart();
            String orderAmount = proportion.getOrderAmount().toPlainString();
            orderAmountPieChart.setValue(orderAmount);
            orderAmountPieChart.setName(Objects.equals(dimension, DimensionTypeEnum.STORE_TYPE.getDimension()) ? MerchantStoreEnum.Type.getDesc(proportion.getStoreType()) : proportion.getStoreGroupName());
            orderAmountPieCharts.add(orderAmountPieChart);

            PieChart orderPricePieChart = new PieChart();
            String orderPrice = proportion.getOrderPrice().toPlainString();
            orderPricePieChart.setValue(orderPrice);
            orderPricePieChart.setName(Objects.equals(dimension, DimensionTypeEnum.STORE_TYPE.getDimension()) ? MerchantStoreEnum.Type.getDesc(proportion.getStoreType()) : proportion.getStoreGroupName());
            orderPricePieCharts.add(orderPricePieChart);
        }
        pieChartDTO.setOrderAmountPieChart(orderAmountPieCharts);
        pieChartDTO.setOrderPricePieChart(orderPricePieCharts);
        return pieChartDTO;
    }

    private Boolean processQueryParameters(MerchantStoreOrderProportionQueryDTO dto) {
        if (Objects.isNull(dto.getTimeTag()) || Objects.isNull(dto.getType())) {
            throw new ParamsException("查询参数缺失");
        }
        // 默认排序字段
        if (StringUtils.isEmpty(dto.getOrderPriceSort()) && StringUtils.isEmpty(dto.getOrderAmountSort())) {
            dto.setOrderAmountSort("desc");
        }

        dto.setTenantId(UserLoginContextUtils.getTenantId());
        // 门店信息查询
        if (Objects.nonNull(dto.getStoreType()) || Objects.nonNull(dto.getStoreGroupId())) {
//            List<Long> storeIds = merchantStoreMapper.selectIdListByParam(UserLoginContextUtils.getTenantId(), null, null, dto.getStoreType(), dto.getStoreGroupId());
            List<Long> storeIds = merchantStoreService.selectIdListByParam(UserLoginContextUtils.getTenantId(), null, null, dto.getStoreType(), dto.getStoreGroupId());
            if (CollectionUtils.isEmpty(storeIds)) {
                return Boolean.FALSE;
            }
            dto.setStoreIds(storeIds);
        }
        return Boolean.TRUE;
    }

    @Override
    public PageInfo<MerchantStoreOrderProportionAnalysisDTO> listProportion(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
        // 处理查询参数
        Boolean resultFlag = processQueryParameters(merchantStoreOrderProportionQueryDTO);
        if (!resultFlag) {
            return PageInfoHelper.createPageInfo(Lists.newArrayList(), merchantStoreOrderProportionQueryDTO.getPageSize());
        }

        Page<MerchantStoreOrderProportionAnalysis> page = PageHelper.startPage(merchantStoreOrderProportionQueryDTO.getPageIndex(), merchantStoreOrderProportionQueryDTO.getPageSize());
        merchantStoreOrderProportionAnalysisRepository.listByCondition(merchantStoreOrderProportionQueryDTO);
        // 计算占比
        calculateProportion(page.getResult());

        // 转换对象
        PageInfo<MerchantStoreOrderProportionAnalysisDTO> pageInfo = PageInfoConverter.toPageInfo(page, MerchantStoreAuditConvertUtil::toDTO);

        // 填充门店信息
        populateStoreInfo(pageInfo.getList());
        return pageInfo;
    }

    private void calculateProportion(List<MerchantStoreOrderProportionAnalysis> list) {
        for (MerchantStoreOrderProportionAnalysis item : list) {
            calculateSingleProportion(item);
        }
    }

    private void calculateSingleProportion(MerchantStoreOrderProportionAnalysis item) {
        // 计算本期订货数量占比
        calculateAndSetProportion(item.getOrderAmount(), item.getTotalOrderAmount(),
                item::setOrderAmountProportion);

        // 计算上期订货数量占比
        calculateAndSetProportion(item.getOrderAmountUpperPeriod(), item.getTotalOrderAmountUpperPeriod(),
                item::setOrderAmountProportionUpperPeriod);

        // 计算订货数量占比的环比
        calculateAndSetRatio(item.getOrderAmountProportion(), item.getOrderAmountProportionUpperPeriod(),
                item::setOrderAmountProportionUpperPeriod);

        // 计算本期金额占比
        calculateAndSetProportion(item.getOrderPrice(), item.getTotalOrderPrice(),
                item::setOrderPriceProportion);

        // 计算上期金额占比
        calculateAndSetProportion(item.getOrderPriceUpperPeriod(), item.getTotalOrderPriceUpperPeriod(),
                item::setOrderPriceProportionUpperPeriod);

        // 计算金额占比的环比
        calculateAndSetRatio(item.getOrderPriceProportion(), item.getOrderPriceProportionUpperPeriod(),
                item::setOrderPriceProportionUpperPeriod);
    }

    private void calculateAndSetProportion(Number amount, Number totalAmount, Consumer<BigDecimal> setter) {
        if (totalAmount != null && totalAmount.doubleValue() > 0) {
            BigDecimal amountBigDecimal = convertToBigDecimal(amount);
            BigDecimal totalAmountBigDecimal = convertToBigDecimal(totalAmount);
            BigDecimal proportion = amountBigDecimal.divide(totalAmountBigDecimal, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.DOWN);
            setter.accept(proportion);
        }
    }

    private void calculateAndSetRatio(BigDecimal currentProportion, BigDecimal upperProportion, Consumer<BigDecimal> setter) {
        if (currentProportion == null || upperProportion == null) {
            setter.accept(null);
            return;
        }
        if (upperProportion.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal diff = currentProportion.subtract(upperProportion);
            BigDecimal ratio = diff.divide(upperProportion, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.DOWN);
            setter.accept(ratio);
        }
    }

    private BigDecimal convertToBigDecimal(Number number) {
        if (number instanceof BigDecimal) {
            return (BigDecimal) number;
        } else if (number instanceof Integer || number instanceof Long) {
            return new BigDecimal(number.longValue());
        } else {
            return BigDecimal.valueOf(number.doubleValue());
        }
    }

    private void populateStoreInfo(List<MerchantStoreOrderProportionAnalysisDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> storeIds = list.stream().map(MerchantStoreOrderProportionAnalysisDTO::getStoreId).collect(Collectors.toList());
//        List<MerchantStoreDTO> merchantStoreList = merchantStoreMapper.batchQueryByStoreIds(UserLoginContextUtils.getTenantId(), storeIds);
        List<MerchantStoreDTO> merchantStoreList = merchantStoreService.batchQueryByStoreIds(storeIds, UserLoginContextUtils.getTenantId());
        Map<Long, MerchantStoreDTO> storeInfoMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));
        list.forEach(el -> {
            MerchantStoreDTO merchantStoreDTO = storeInfoMap.get(el.getStoreId());
            el.setStoreName(Objects.isNull(merchantStoreDTO) ? "" : merchantStoreDTO.getStoreName());
        });
    }

    @Override
    public Boolean exportProportion(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
        // 生成文件参数
        HashMap<String, String> paramsMap = processFileParameters(merchantStoreOrderProportionQueryDTO);

        // 处理导出参数
        Boolean resultFlag = processQueryParameters(merchantStoreOrderProportionQueryDTO);

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.MERCHANT_STORE_ORDER_PROPORTION.getType());
        recordDTO.setTenantId(UserLoginContextUtils.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.MERCHANT_STORE_ORDER_PROPORTION.getFileName());
        recordDTO.setParams(JSONObject.toJSONString(paramsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(merchantStoreOrderProportionQueryDTO, ee -> {
            // 1、表格处理
            String filePath = null;
            if (!resultFlag) {
                filePath = commonService.exportExcel(Lists.newArrayList(), ExcelTypeEnum.MERCHANT_STORE_ORDER_PROPORTION.getName());
            }else {
                filePath = generateProportionExcel(merchantStoreOrderProportionQueryDTO);
            }

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


//        // 生成下载记录
//        Long fileDownloadRecordId = processFileDownloadRecord(paramsMap);
//
//        // 处理导出参数
//        Boolean resultFlag = processQueryParameters(merchantStoreOrderProportionQueryDTO);
//        if(!resultFlag) {
//            commonService.generateAndUploadExcel(Lists.newArrayList(), ExcelTypeEnum.MERCHANT_STORE_ORDER_PROPORTION, fileDownloadRecordId);
//        }
//
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            generateProportionExcel(merchantStoreOrderProportionQueryDTO, fileDownloadRecordId);
//        });
        return Boolean.TRUE;
    }

    private String generateProportionExcel(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.MERCHANT_STORE_ORDER_PROPORTION.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        LargeDataSetExporter<MerchantStoreOrderProportionAnalysis, MerchantStoreOrderProportionAnalysisExcelDTO> handler = new LargeDataSetExporter<MerchantStoreOrderProportionAnalysis, MerchantStoreOrderProportionAnalysisExcelDTO>() {
            @Override
            protected List<MerchantStoreOrderProportionAnalysisExcelDTO> convert(MerchantStoreOrderProportionAnalysis data) {
                // 计算占比
                calculateSingleProportion(data);
                return Lists.newArrayList(MerchantStoreAuditConvertUtil.toExcelDTO(data));
            }
            @Override
            protected void flushData(List<MerchantStoreOrderProportionAnalysisExcelDTO> dataList) {
                // 在刷新到excel之前填充一些需要批量查询的信息
                populateExcelDTO(dataList);
                excelWriter.fill(dataList, fillConfig, writeSheet);
            }

            private void populateExcelDTO(List<MerchantStoreOrderProportionAnalysisExcelDTO> dataList) {
                List<Long> storeIds = dataList.stream().map(MerchantStoreOrderProportionAnalysisExcelDTO::getStoreId).collect(Collectors.toList());
                // 批量查询门店
                Long tenantId = merchantStoreOrderProportionQueryDTO.getTenantId();
//                MerchantStoreQueryDTO query = MerchantStoreQueryDTO.builder().tenantId(tenantId).storeIds(storeIds).build();
//                List<MerchantStoreDTO> merchantStoreList = merchantStoreMapper.selectList(query);
                List<MerchantStoreDTO> merchantStoreList = merchantStoreService.selectList(tenantId, storeIds);
                Map<Long, MerchantStoreDTO> storeInfoMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));
                dataList.forEach(el -> {
                    MerchantStoreDTO merchantStoreDTO = storeInfoMap.get(el.getStoreId());
                    if (Objects.isNull(merchantStoreDTO)) {
                        return;
                    }
                    el.setStoreGroup(merchantStoreDTO.getGroupName());
                    el.setStoreName(merchantStoreDTO.getStoreName());
                    el.setStoreType(MerchantStoreEnum.Type.getDesc(merchantStoreDTO.getType()));
                });
            }
        };

        merchantStoreOrderProportionAnalysisMapper.exportByCondition(merchantStoreOrderProportionQueryDTO, handler);
        handler.clearData();
        excelWriter.finish();

        return filePath;
        // 上传数据到七牛云
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, ExcelTypeEnum.MERCHANT_STORE_ORDER_PROPORTION, fileDownloadRecordId);
    }

    private Long processFileDownloadRecord(HashMap<String, String> paramsMap) {
        FileDownloadRecord record = new FileDownloadRecord();
        record.setTenantId(UserLoginContextUtils.getTenantId());
        record.setParams(JSONObject.toJSONString(paramsMap));
        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
        record.setType(FileDownloadTypeEnum.MERCHANT_STORE_ORDER_PROPORTION.getType());
        return fileDownloadRecordService.generateFileDownloadRecord(record);
    }

    /**
     * 生成文件参数
     * @param merchantStoreOrderProportionQueryDTO 查询条件
     * @return
     */
    private HashMap<String, String> processFileParameters(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
        HashMap<String, String> paramsMap = new LinkedHashMap<>(NumberConstants.TEN);
        // 根据时间标签和类型确定搜索的时间范围
        Pair<String, String> timeRangePair = TimeUtils.getRangeByTimeTagAndType(merchantStoreOrderProportionQueryDTO.getTimeTag(), merchantStoreOrderProportionQueryDTO.getType(), FORMAT_STRING);
        paramsMap.put(Constants.QUERY_TIME, timeRangePair.getKey() + StringConstants.SEPARATING_IN_LINE + timeRangePair.getValue());
        if (Objects.nonNull(merchantStoreOrderProportionQueryDTO.getTitle())) {
            paramsMap.put(Constants.TITLE, merchantStoreOrderProportionQueryDTO.getTitle());
        }
        // 门店分组
        if (Objects.nonNull(merchantStoreOrderProportionQueryDTO.getStoreGroupId())) {
//            MerchantStoreGroup merchantStoreGroup = merchantStoreGroupMapper.selectById(merchantStoreOrderProportionQueryDTO.getStoreGroupId());
            MerchantStoreGroupPageResultResp merchantStoreGroup = merchantStoreGroupService.queryById(merchantStoreOrderProportionQueryDTO.getStoreGroupId(), UserLoginContextUtils.getTenantId());
            paramsMap.put(Constants.STORE_GROUPS, Objects.isNull(merchantStoreGroup) ? "" : merchantStoreGroup.getMerchantStoreGroupName());
        }
        // 门店类型
        if (Objects.nonNull(merchantStoreOrderProportionQueryDTO.getStoreType())) {
            String storeType = MerchantStoreEnum.Type.getDesc(merchantStoreOrderProportionQueryDTO.getStoreType());
            paramsMap.put(Constants.STORE_TYPE, storeType);
        }
        return paramsMap;
    }
}
