package com.cosfo.manage.merchant.service;

import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionAnalysisDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionPieChartDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderProportionQueryDTO;
import com.github.pagehelper.PageInfo;

/**
 * @description: 门店订货占比分析
 * @author: George
 * @date: 2023-06-09
 **/
public interface MerchantStoreOrderProportionAnalysisService {

    /**
     * 查询占比饼状图
     * @param merchantStoreOrderProportionQueryDTO 查询条件
     * @return 饼图数据
     */
    MerchantStoreOrderProportionPieChartDTO queryProportionPieChart(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO);

    /**
     * 列表分页
     * @param merchantStoreOrderProportionQueryDTO
     * @return
     */
    PageInfo<MerchantStoreOrderProportionAnalysisDTO> listProportion(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO);

    /**
     * 导出占比
     * @param merchantStoreOrderProportionQueryDTO
     * @return
     */
    Boolean exportProportion(MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO);
}
