package com.cosfo.manage.merchant.service.impl;

import cn.hutool.core.date.DateUtil;
import com.cosfo.manage.bill.mapper.PaymentItemMapper;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.po.Payment;
import com.cosfo.manage.bill.model.po.PaymentItem;
import com.cosfo.manage.common.context.TenantEnums;
import com.cosfo.manage.common.context.TradeTypeEnum;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.merchant.convert.MerchantStoreMapperConvert;
import com.cosfo.manage.merchant.model.dto.MerchantStoreTradeSummaryInitDTO;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.model.po.MerchantStoreTradeSummary;
import com.cosfo.manage.merchant.repository.MerchantStoreTradeSummaryRepository;
import com.cosfo.manage.merchant.service.MerchantStoreTradeSummaryService;
import com.cosfo.manage.order.mapper.payment.RefundMapper;
import com.cosfo.manage.order.model.po.payment.Refund;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2023-10-31
 **/
@Service
@Slf4j
public class MerchantStoreTradeSummaryServiceImpl implements MerchantStoreTradeSummaryService {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private MerchantStoreTradeSummaryRepository merchantStoreTradeSummaryRepository;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    @Override
    public void generateStoreDimensionPaymentSummary(Long id) {
        Payment payment = paymentMapper.selectByPrimaryKey(id);
        generateStoreDimensionTradeSummaryByPayment(payment);
    }

    private void generateStoreDimensionTradeSummaryByPayment(Payment payment) {
        if (payment == null) {
            log.error("payment为空，不做处理", new ProviderException("未查询到支付信息"));
            return;
        }
        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentId(payment.getId());
        if (paymentItems == null || paymentItems.isEmpty()) {
            log.error("paymentItems为空，不做处理", new ProviderException("未查询到支付项信息"));
            return;
        }
        Long tenantId = payment.getTenantId();
        List<Long> orderIds = paymentItems.stream().map(PaymentItem::getOrderId).collect(Collectors.toList());
        List<OrderResp> orderResps = orderQueryFacade.queryByIds(orderIds);
        if (orderResps == null || orderResps.isEmpty()) {
            log.error("orderList为空，不做处理", new ProviderException("未查询到订单信息"));
            return;
        }

        OrderResp orderDTO = orderResps.get(0);
        Long storeId = orderDTO.getStoreId();
        MerchantStore store = MerchantStoreMapperConvert.INSTANCE.respToMerchantStore(userCenterMerchantStoreFacade.getMerchantStoreById(storeId));
        if (store == null) {
            log.error("未查询到门店信息", new ProviderException("未查询到门店信息"));
            return;
        }

        OrderItemQueryReq req = new OrderItemQueryReq();
        req.setTenantId(tenantId);
        req.setOrderIds(orderIds);
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotResps = orderItemQueryFacade.queryOrderItemList(req);
        Integer quantity = orderItemAndSnapshotResps.stream().mapToInt(OrderItemAndSnapshotResp::getAmount).sum();

        MerchantStoreTradeSummary summary = getMerchantStoreTradeSummary(payment, store, quantity);
        MerchantStoreTradeSummary uniqueTradeSummary = merchantStoreTradeSummaryRepository.selectByUniqueKey(payment.getPaymentNo());
        if (uniqueTradeSummary == null) {
            merchantStoreTradeSummaryRepository.save(summary);
        } else {
            log.info("交易汇总已存在，不做处理，业务单号：[{}]", payment.getPaymentNo());
        }
    }

    private static MerchantStoreTradeSummary getMerchantStoreTradeSummary(Payment payment, MerchantStore store, Integer quantity) {
        MerchantStoreTradeSummary summary = new MerchantStoreTradeSummary();
        summary.setTenantId(payment.getTenantId());
        summary.setStoreId(store.getId());
        summary.setStoreType(store.getType());
        summary.setBusinessId(payment.getId());
        summary.setBusinessNo(payment.getPaymentNo());
        summary.setTradeAmount(payment.getTotalPrice());
        summary.setTradeType(TradeTypeEnum.PAY.getType());
        summary.setTradeTime(Objects.isNull(payment.getSuccessTime())? payment.getCreateTime() : payment.getSuccessTime());
        summary.setTradeQuantity(quantity);
        return summary;
    }

    @Override
    public void initStoreDimensionTradeSummary(MerchantStoreTradeSummaryInitDTO merchantStoreTradeSummaryInitDTO) {
        log.info("开始初始化交易汇总...");
        List<Payment> successPayments = paymentMapper.selectSuccessByTime(merchantStoreTradeSummaryInitDTO.getTenantId(), merchantStoreTradeSummaryInitDTO.getStartTime(), merchantStoreTradeSummaryInitDTO.getEndTime());
        if (!CollectionUtils.isEmpty(successPayments)) {
            successPayments.forEach(this::generateStoreDimensionTradeSummaryByPayment);
        }

        List<Refund> refunds = refundMapper.selectSuccessByTime(merchantStoreTradeSummaryInitDTO.getTenantId(), merchantStoreTradeSummaryInitDTO.getStartTime(), merchantStoreTradeSummaryInitDTO.getEndTime());
        if (!CollectionUtils.isEmpty(refunds)) {
            refunds.forEach(this::generateStoreDimensionRefundSummary);
        }
        log.info("初始化交易汇总结束...");
    }

    @Override
    public void auditStoreDimensionTradeSummary(Long tenantId, String startTime, String endTime) {
        log.info("开始稽核昨日交易汇总数据...");
        // 稽核昨日数据 对比支付总金额
        List<Long> tenantIds = getTenants(tenantId);
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            startTime = DateUtil.beginOfDay(DateUtil.yesterday()).toString("yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.beginOfDay(DateUtil.date()).toString("yyyy-MM-dd HH:mm:ss");
        }
        for (Long id : tenantIds) {
            log.info("开始稽核租户:{}昨日交易汇总数据", id);
            BigDecimal successPaymentPrice = paymentMapper.selectSuccessPrice(id, startTime, endTime);
            BigDecimal successPaymentSummaryPrice = merchantStoreTradeSummaryRepository.selectSuccessPaymentPrice(id, startTime, endTime, TradeTypeEnum.PAY.getType());
            if (successPaymentPrice.compareTo(successPaymentSummaryPrice) != 0) {
                log.error("租户id：[{}], 支付金额：[{}]，支付汇总金额：[{}]", id, successPaymentPrice, successPaymentSummaryPrice);
            } else {
                log.info("租户id：[{}]稽核支付汇总数据完成", id);
            }

            BigDecimal successRefundPrice = refundMapper.selectSuccessPrice(id, startTime, endTime);
            BigDecimal successRefundSummaryPrice = merchantStoreTradeSummaryRepository.selectSuccessPaymentPrice(id, startTime, endTime, TradeTypeEnum.REFUND.getType());
            if (successRefundPrice.compareTo(successRefundSummaryPrice) != 0) {
                log.error("租户id：[{}], 退款金额：[{}]，退款汇总金额：[{}]", id, successRefundPrice, successRefundSummaryPrice);
            } else {
                log.info("租户id：[{}]稽核退款汇总数据完成", id);
            }
        }
        log.info("稽核昨日交易汇总数据结束...");
    }

    private List<Long> getTenants(Long tenantId) {
        if (tenantId != null) {
            return Lists.newArrayList(tenantId);
        }
        TenantQueryReq req = new TenantQueryReq();
        req.setType(TenantEnums.type.BRAND_PARTY.getCode());
        req.setStatus(TenantEnums.status.EFFECTIVE.getCode());
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(req);
        return tenants.stream().map(TenantResultResp::getId).collect(Collectors.toList());
    }

    @Override
    public void generateStoreDimensionRefundSummary(Long refundId) {
        Refund refund = refundMapper.selectByPrimaryKey(refundId);
        generateStoreDimensionRefundSummary(refund);
    }

    private void generateStoreDimensionRefundSummary(Refund refund) {
        if (refund == null) {
            log.error("Refund is null, no processing is done", new ProviderException("Unable to find refund information"));
            return;
        }
        Long afterSaleId = refund.getAfterSaleId();
        List<OrderAfterSaleResp> orderAfterSales = orderAfterSaleQueryFacade.queryByIds(Collections.singletonList(afterSaleId));
        if (orderAfterSales == null || orderAfterSales.isEmpty()) {
            log.error("After-sale order is empty, no processing is done");
            return;
        }
        OrderAfterSaleResp orderAfterSaleDTO = orderAfterSales.get(0);
        Long storeId = orderAfterSaleDTO.getStoreId();
        MerchantStore store = MerchantStoreMapperConvert.INSTANCE.respToMerchantStore(userCenterMerchantStoreFacade.getMerchantStoreById(storeId));
        if (store == null) {
            log.error("Unable to find store information", new ProviderException("Unable to find store information"));
            return;
        }
        // Additional logic for refund trade summaries
        MerchantStoreTradeSummary summary = getMerchantStoreTradeSummary(refund, store);
        MerchantStoreTradeSummary uniqueTradeSummary = merchantStoreTradeSummaryRepository.selectByUniqueKey(refund.getRefundNo());
        if (uniqueTradeSummary == null) {
            merchantStoreTradeSummaryRepository.save(summary);
        } else {
            log.info("交易汇总已存在，不做处理，业务单号：[{}]", refund.getRefundNo());
        }
    }

    private static MerchantStoreTradeSummary getMerchantStoreTradeSummary(Refund refund, MerchantStore store) {
        // Specific logic for creating a trade summary for refunds
        MerchantStoreTradeSummary summary = new MerchantStoreTradeSummary();
        summary.setTenantId(refund.getTenantId());
        summary.setStoreId(store.getId());
        summary.setStoreType(store.getType());
        summary.setBusinessId(refund.getId());
        summary.setBusinessNo(refund.getRefundNo());
        summary.setTradeAmount(refund.getRefundPrice());
        summary.setTradeType(TradeTypeEnum.REFUND.getType());
        summary.setTradeTime(Objects.isNull(refund.getSuccessTime()) ? refund.getCreateTime() : refund.getSuccessTime());
        return summary;
    }
}
