package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.merchant.model.dto.CommonIdDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupQueryDTO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreGroupVO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreLinkedVO;
import com.cosfo.manage.merchant.service.MerchantStoreGroupMappingService;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.report.converter.ReportConverter;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 门店分组
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/10
 */
@RestController
@RequestMapping(value = "/merchant/store/group")
public class MerchantStoreGroupController extends BaseController {
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;
    @Resource
    private MerchantStoreGroupMappingService merchantStoreGroupMappingService;
//    @Resource
//    private MerchantStoreGroupRepository merchantStoreGroupRepository;

    /**
     * 分页列表
     *
     * @param merchantStoreGroupQueryDTO
     * @return
     */
    @PostMapping("/query/list")
    public CommonResult<PageInfo<MerchantStoreGroupVO>> list(@RequestBody MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO){
        return merchantStoreGroupService.list(merchantStoreGroupQueryDTO, getMerchantInfoDTO());
    }

    /**
     * 查询所有
     *
     * @return
     */
    @PostMapping("/query/list-all")
    public CommonResult<List<MerchantStoreGroupVO>> listAll(){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return merchantStoreGroupService.listAll(loginContextInfoDTO);
    }
    /**
     * 新增门店分组
     *
     * @param merchantStoreGroupDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:customer-group:add", expireError = true)
    @PostMapping("/upsert/add")
    public CommonResult add(@RequestBody @Valid MerchantStoreGroupDTO merchantStoreGroupDTO){
        return merchantStoreGroupService.add(merchantStoreGroupDTO, getMerchantInfoDTO());
    }

    /**
     * 更新门店分组名称
     *
     * @param merchantStoreGroupDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:customer-group:update", expireError = true)
    @PostMapping("/upsert/update")
    public CommonResult update(@RequestBody @Valid MerchantStoreGroupDTO merchantStoreGroupDTO){
        return merchantStoreGroupService.update(merchantStoreGroupDTO, getMerchantInfoDTO());
    }

    /**
     * 绑定门店
     *
     * @param merchantStoreGroupDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert")
    public CommonResult bindStore(@RequestBody MerchantStoreGroupDTO merchantStoreGroupDTO){
        return merchantStoreGroupService.bindStore(merchantStoreGroupDTO, getMerchantInfoDTO());
    }

    /**
     * 导出分组
     *
     * @param merchantStoreGroupQueryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/export")
    public CommonResult export(@RequestBody MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO){
        return merchantStoreGroupService.export(merchantStoreGroupQueryDTO, getMerchantInfoDTO());
    }

    /**
     * 导入分组
     *
     * @param file
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/input")
    public CommonResult input(@RequestBody MultipartFile file) throws IOException {
        return merchantStoreGroupService.input(file, getMerchantInfoDTO());
    }

    /**
     * 分组详情
     *
     * @param merchantStoreGroupDTO
     * @return
     */
    @PostMapping("/query/detail")
    public CommonResult<MerchantStoreGroupVO> detail(@RequestBody MerchantStoreGroupDTO merchantStoreGroupDTO){
        return CommonResult.ok();
    }

    /**
     * 删除门店分组
     *
     * @param merchantStoreGroupDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:customer-group:delete", expireError = true)
    @PostMapping("/upsert/delete")
    public CommonResult delete(@RequestBody MerchantStoreGroupDTO merchantStoreGroupDTO ){
        return merchantStoreGroupService.delete(merchantStoreGroupDTO, getMerchantInfoDTO());
    }

    /**
     * 门店统计
     *
     * @return
     */
    @PostMapping(value = "/query/statistical")
    public CommonResult storeStatistical(){
        return merchantStoreGroupService.storeStatistical(getMerchantInfoDTO());
    }

    /**
     * 门店分组详情--按分组id查询该租户该分组已关联的门店
     * @param commonIdDTO 分组id接收对象
     * @return
     */
    @PostMapping(value = "/query/list-by-group-id")
    public CommonResult<List<MerchantStoreDTO>> listByTenantId(@RequestBody CommonIdDTO commonIdDTO){
        return merchantStoreGroupMappingService.listByTenantId(commonIdDTO.getId(), getMerchantInfoDTO());
    }
    /**
     * 门店分组信息更新
     * @param merchantStoreGroupUpdateDTO 分组id
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/update-by-group-id")
    public CommonResult updateMerchant(@RequestBody MerchantStoreGroupDTO merchantStoreGroupUpdateDTO){
        return merchantStoreGroupService.updateMerchant(merchantStoreGroupUpdateDTO, getMerchantInfoDTO());
    }

    /**
     * 按条件查询所有默认分组的门店
     * @param storeVO
     * @return
     */
    @PostMapping("/query/list-all-default")
    public CommonResult<PageInfo<MerchantStoreDTO>> listAllDefaultList(@RequestBody MerchantStoreLinkedVO storeVO){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        MerchantStoreGroupPageResultResp storeGroup = merchantStoreGroupService.queryDefaultGroup(loginContextInfoDTO.getTenantId());
        if (Objects.isNull(storeGroup)) {
            throw new BizException("不存在默认分组");
        }
        storeVO.setGroupId(storeGroup.getMerchantStoreGroupId());
        return merchantStoreGroupMappingService.listAll(ReportConverter.merchantStoreVO2DTO(storeVO), loginContextInfoDTO);
    }

    /**
     * 条件查询，不分页，含门店数量
     * @param merchantStoreGroupQueryDTO
     * @return
     */
    @PostMapping("/query/list/no-page")
    public CommonResult<List<MerchantStoreGroupVO>> listByParam(@RequestBody MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO){
        merchantStoreGroupQueryDTO.setTenantId(getMerchantInfoDTO().getTenantId());
        return CommonResult.ok(merchantStoreGroupService.listGroupByParam(merchantStoreGroupQueryDTO));
    }

}
