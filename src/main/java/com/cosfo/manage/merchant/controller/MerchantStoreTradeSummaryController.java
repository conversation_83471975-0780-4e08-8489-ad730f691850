package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.merchant.model.dto.MerchantStoreTradeSummaryInitDTO;
import com.cosfo.manage.merchant.service.MerchantStoreTradeSummaryService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-02
 **/
@RestController
@RequestMapping(value = "/merchant/store/trade/summary")
public class MerchantStoreTradeSummaryController {

    @Resource
    private MerchantStoreTradeSummaryService merchantStoreTradeSummaryService;
    @PostMapping("/init")
    public CommonResult<Boolean> init(@RequestBody MerchantStoreTradeSummaryInitDTO merchantStoreTradeSummaryInitDTO) {
        merchantStoreTradeSummaryService.initStoreDimensionTradeSummary(merchantStoreTradeSummaryInitDTO);
        return CommonResult.ok();
    }
}
