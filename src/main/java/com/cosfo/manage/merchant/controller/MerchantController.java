package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.merchant.model.dto.LoginDTO;
import com.cosfo.manage.merchant.model.dto.MerchantDTO;
import com.cosfo.manage.merchant.model.dto.SettleMentInfoDTO;
import com.cosfo.manage.merchant.model.vo.TenantDetailVO;
import com.cosfo.manage.merchant.service.MerchantService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 租户管理
 *
 * <AUTHOR>
 * 租户控制入口
 */
@Controller
public class MerchantController extends BaseController {
    @Resource
    private MerchantService merchantService;

    /**
     * 登陆
     *
     * @param loginDTO
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public ResultDTO login(@RequestBody LoginDTO loginDTO) {
        return merchantService.login(loginDTO);
    }

    /**
     * 获取品牌方信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public ResultDTO<TenantDetailVO> getMchMerchantInfo(){
        return merchantService.getMchMerchantInfo(getMerchantInfoDTO());
    }

    /**
     * 登出
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/loginOut", method = RequestMethod.POST)
    public ResultDTO loginOut(HttpServletRequest request){
        return merchantService.loginOut(request);
    }

    /**
     * 获取安全设置信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/get/safeInfo", method = RequestMethod.GET)
    public ResultDTO getSafeInfo(){
        return merchantService.getSafeInfo(getMerchantInfoDTO());
    }

    /**
     * 获取安全设置信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/ok", method = RequestMethod.GET)
    public ResultDTO getOk(){
        return ResultDTO.success("SUCCESS");
    }

    /**
     * 商户信息修改接口
     *
     * @param merchantDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @ResponseBody
    @RequestMapping(value = "/merchant/update", method = RequestMethod.POST)
    public ResultDTO merchantUpdate(@RequestBody MerchantDTO merchantDTO) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return merchantService.merchantUpdate(merchantDTO,contextInfoDTO);
    }

    /**
     * 获取品牌方结算信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/merchant/query/get-settlement-info", method = RequestMethod.POST)
    public CommonResult<SettleMentInfoDTO> getSettlementInfo(){
        Long tenantId = getMerchantInfoDTO().getTenantId();
        return merchantService.selectSettlementInfo(tenantId);
    }

}
