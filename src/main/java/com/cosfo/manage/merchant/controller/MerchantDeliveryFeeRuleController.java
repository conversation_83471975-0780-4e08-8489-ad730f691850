package com.cosfo.manage.merchant.controller;

import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleDTO;
import com.cosfo.manage.merchant.service.MerchantDeliveryFeeRuleService;
import com.cosfo.manage.tenant.model.dto.TenantDeliveryFeeRuleDTO;
import com.cosfo.summerfarm.model.dto.SummerfarmDeliveryFeeRuleDTO;
import com.cosfo.summerfarm.model.input.SummerfarmDeliveryFeeRuleInput;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import net.xianmu.marketing.center.client.freight.provider.DeliveryFeeRuleQueryProvider;
import net.xianmu.marketing.center.client.freight.provider.DistributionRulesProvider;
import net.xianmu.marketing.center.client.freight.req.DeliveryFeeRuleQueryReq;
import net.xianmu.marketing.center.client.freight.req.DistributionRulesInfoByAddressReq;
import net.xianmu.marketing.center.client.freight.resp.DeliveryFeeRuleInfoResp;
import net.xianmu.marketing.center.client.freight.resp.DistributionRulesInfoByAddressResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/27 15:08
 */
@RestController
@RequestMapping("/merchantDeliveryFeeRule")
public class MerchantDeliveryFeeRuleController extends BaseController {

    @Resource
    private MerchantDeliveryFeeRuleService merchantDeliveryFeeRuleService;

    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;

    @DubboReference
    private DistributionRulesProvider distributionRulesProvider;

    @DubboReference
    private DeliveryFeeRuleQueryProvider deliveryFeeRuleQueryProvider;

    /**
     * 运费配置列表
     *
     * @return
     */
    @RequestMapping(value = "/listAllRule", method = RequestMethod.GET)
    public ResultDTO<List<MerchantDeliveryFeeRuleDTO>> listAll() {
        LoginContextInfoDTO loginInfoDTO = getMerchantInfoDTO();
        Long tenantId = loginInfoDTO.getTenantId();
        return merchantDeliveryFeeRuleService.listAll(tenantId);
    }

    /**
     * 更新运费规则
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:order-delivery-fee-rule:update", expireError = true)
    @RequestMapping(value = "/updateDeliveryFeeRule", method = RequestMethod.PUT)
    public ResultDTO updateDeliveryFeeRule(@RequestBody List<MerchantDeliveryFeeRuleDTO> ruleDTOList) {
        return merchantDeliveryFeeRuleService.updateDeliveryFeeRule(ruleDTOList);
    }

    /**
     * 查询品牌方运费规则
     *
     * @return
     */
    @PostMapping("/query/tenant/delivery/rule")
    public ResultDTO<TenantDeliveryFeeRuleDTO> queryTenantDeliveryRule() {
        return merchantDeliveryFeeRuleService.queryTenantDeliveryRule(getMerchantInfoDTO());
    }

    /**
     * 根据区域查询鲜沐运费规则, 废弃
     *
     * @param summerfarmDeliveryFeeRuleInput
     * @return
     */
    @PostMapping("/query/summerfarm/delivery/rule")
    @Deprecated
    public ResultDTO<SummerfarmDeliveryFeeRuleDTO> querySummerfarmDeliveryFeeRule(@RequestBody SummerfarmDeliveryFeeRuleInput summerfarmDeliveryFeeRuleInput) {
        return merchantDeliveryFeeRuleService.querySummerfarmDeliveryFeeRule(summerfarmDeliveryFeeRuleInput);
    }

    /**
     * 根据区域查询鲜沐运费规则，包含日配、非日配
     *
     * @param req
     * @return
     */
    @PostMapping("/query/summerfarm/delivery/rule/v2")
    @Deprecated
    public CommonResult<DistributionRulesInfoByAddressResp> querySummerfarmDeliveryFeeRuleV2(@RequestBody @Valid DistributionRulesInfoByAddressReq req) {
        DistributionRulesInfoByAddressResp resp = RpcResponseUtil.handler(distributionRulesProvider.getInfoByAddress(req));
        return CommonResult.ok(resp);
    }

    /**
     * 根据区域查询鲜沐运费规则，包含日配、非日配 V3
     *
     * @param input
     * @return
     */
    @PostMapping("/query/summerfarm/delivery/rule/v3")
    public CommonResult<DeliveryFeeRuleInfoResp> querySummerfarmDeliveryFeeRuleV3(@RequestBody @Valid SummerfarmDeliveryFeeRuleInput input) {

        Long tenantId = getMerchantInfoDTO().getTenantId();

        // 获取租户信息，取adminId 鲜沐大客户id作为传参
        TenantResultResp tenantResultResp = userCenterTenantFacade.getTenantById(tenantId);

        DeliveryFeeRuleQueryReq req = new DeliveryFeeRuleQueryReq();
        req.setTenantId(tenantId);
        req.setXmAdminId(NumberUtils.toInt(String.valueOf(tenantResultResp.getAdminId()), -1));
        req.setProvince(input.getProvince());
        req.setCity(input.getCity());
        req.setArea(input.getArea());

        DeliveryFeeRuleInfoResp resp = RpcResponseUtil.handler(deliveryFeeRuleQueryProvider.queryDeliveryFeeRule(req));
        return CommonResult.ok(resp);
    }
}
