package com.cosfo.manage.merchant.controller;


import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.merchant.model.dto.*;
import com.cosfo.manage.merchant.service.MerchantStoreItemOrderAnalysisService;
import com.cosfo.manage.merchant.service.MerchantStoreOrderAnalysisService;
import com.cosfo.manage.merchant.service.MerchantStoreOrderProportionAnalysisService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * @description: 门店稽核中心控制层
 * @author: George
 * @date: 2023-06-05
 **/
@RestController
@RequestMapping("/merchant/store/audit/center")
public class MerchantStoreAuditCenterController {

    @Resource
    private MerchantStoreItemOrderAnalysisService merchantStoreItemOrderAnalysisService;
    @Resource
    private MerchantStoreOrderAnalysisService merchantStoreOrderAnalysisService;
    @Resource
    private MerchantStoreOrderProportionAnalysisService merchantStoreOrderProportionAnalysisService;

    /**
     * 稽核-门店商品维度概况
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     * @return 概况数据
     */
    @RequestMapping(value = "/query/store-item-dimension/overview", method = RequestMethod.POST)
    public CommonResult<MerchantStoreItemOrderOverviewDTO> queryAuditStoreItemDimensionOverview(@RequestBody MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        MerchantStoreItemOrderOverviewDTO merchantStoreItemOrderOverviewDTO = merchantStoreItemOrderAnalysisService.queryAuditStoreItemDimensionOverview(merchantStoreItemOrderAnalysisQueryDTO);
        return CommonResult.ok(merchantStoreItemOrderOverviewDTO);
    }

    /**
     * 稽核-门店商品维度列表
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     * @return 列表数据
     */
    @RequestMapping(value = "/query/list/store-item-dimension", method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantStoreItemOrderAnalysisDTO>> listAuditStoreItemDimensionPage(@RequestBody MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        PageInfo<MerchantStoreItemOrderAnalysisDTO> pageInfo = merchantStoreItemOrderAnalysisService.listAuditStoreItemDimensionPage(merchantStoreItemOrderAnalysisQueryDTO);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 稽核-导出门店商品维度
     * @param merchantStoreItemOrderAnalysisQueryDTO 查询条件
     * @return 列表数据
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/query/export/store-item-dimension", method = RequestMethod.POST)
    public CommonResult<Boolean> exportAuditStoreItemDimension(@RequestBody MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        Boolean exportFlag = merchantStoreItemOrderAnalysisService.exportAuditStoreItemDimension(merchantStoreItemOrderAnalysisQueryDTO);
        return CommonResult.ok(exportFlag);
    }

    /**
     * 稽核-门店维度概况
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     * @return 概况数据
     */
    @RequestMapping(value = "/query/store-dimension/overview", method = RequestMethod.POST)
    public CommonResult<MerchantStoreOrderOverviewDTO> queryAuditStoreDimensionOverview(@RequestBody MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        MerchantStoreOrderOverviewDTO  merchantStoreOrderOverviewDTO = merchantStoreOrderAnalysisService.queryAuditStoreDimensionOverview(merchantStoreOrderAnalysisQueryDTO);
        return CommonResult.ok(merchantStoreOrderOverviewDTO);
    }

    /**
     * 稽核-门店维度列表
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     * @return 列表数据
     */
    @RequestMapping(value = "/query/list/store-dimension", method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantStoreOrderAnalysisDTO>> listAuditStoreDimensionPage(@RequestBody MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        PageInfo<MerchantStoreOrderAnalysisDTO> pageInfo = merchantStoreOrderAnalysisService.listAuditStoreDimensionPage(merchantStoreOrderAnalysisQueryDTO);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 稽核-导出门店维度
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     * @return 列表数据
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/query/export/store-dimension", method = RequestMethod.POST)
    public CommonResult<Boolean> exportAuditStoreDimension(@RequestBody MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO) {
        Boolean exportFlag = merchantStoreOrderAnalysisService.exportAuditStoreDimension(merchantStoreOrderAnalysisQueryDTO);
        return CommonResult.ok(exportFlag);
    }

    /**
     * 查询饼状图分析
     * @param merchantStoreOrderProportionQueryDTO 查询条件
     * @return name、value
     */
    @RequestMapping(value = "/query/proportion/pie-chart", method = RequestMethod.POST)
    public CommonResult<MerchantStoreOrderProportionPieChartDTO> queryProportionPieChart(@RequestBody MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
        MerchantStoreOrderProportionPieChartDTO pieChartDTO = merchantStoreOrderProportionAnalysisService.queryProportionPieChart(merchantStoreOrderProportionQueryDTO);
        return CommonResult.ok(pieChartDTO);
    }

    /**
     * 稽核-占比分析列表
     * @param merchantStoreOrderProportionQueryDTO 查询条件
     * @return 列表数据
     */
    @RequestMapping(value = "/query/list/proportion", method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantStoreOrderProportionAnalysisDTO>> listProportion(@RequestBody MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
        PageInfo<MerchantStoreOrderProportionAnalysisDTO> pageInfo = merchantStoreOrderProportionAnalysisService.listProportion(merchantStoreOrderProportionQueryDTO);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 稽核-导出占比分析
     * @param merchantStoreOrderProportionQueryDTO 查询条件
     * @return true、false
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/query/export/proportion", method = RequestMethod.POST)
    public CommonResult<Boolean> exportProportion(@RequestBody MerchantStoreOrderProportionQueryDTO merchantStoreOrderProportionQueryDTO) {
       Boolean exportFlag = merchantStoreOrderProportionAnalysisService.exportProportion(merchantStoreOrderProportionQueryDTO);
        return CommonResult.ok(exportFlag);
    }
}
