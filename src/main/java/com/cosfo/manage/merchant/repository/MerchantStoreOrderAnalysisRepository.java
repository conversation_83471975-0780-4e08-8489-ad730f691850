package com.cosfo.manage.merchant.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreOrderOverviewDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreOrderAnalysis;

import java.util.List;

/**
 * <p>
 * 门店订货分析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
public interface MerchantStoreOrderAnalysisRepository extends IService<MerchantStoreOrderAnalysis> {

    /**
     * 查询门店维度的sum数据
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     * @return 门店维度的sum数据
     */
    MerchantStoreOrderOverviewDTO queryStoreOrderSum(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO);

    /**
     * 根据条件查询
     * @param merchantStoreOrderAnalysisQueryDTO 查询条件
     */
    List<MerchantStoreOrderAnalysis> listByCondition(MerchantStoreOrderAnalysisQueryDTO merchantStoreOrderAnalysisQueryDTO);

}
