package com.cosfo.manage.merchant.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryStepFee;
import com.cosfo.manage.merchant.mapper.MerchantDeliveryStepFeeMapper;
import com.cosfo.manage.merchant.repository.MerchantDeliveryStepFeeRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 阶梯运费 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Service
public class MerchantDeliveryStepFeeRepositoryImpl extends ServiceImpl<MerchantDeliveryStepFeeMapper, MerchantDeliveryStepFee> implements MerchantDeliveryStepFeeRepository {

    @Override
    public List<MerchantDeliveryStepFee> queryStepFee(Long tenantId, Long ruleId) {
        LambdaQueryWrapper<MerchantDeliveryStepFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantDeliveryStepFee::getTenantId, tenantId);
        queryWrapper.eq(MerchantDeliveryStepFee::getRuleId, ruleId);
        return list(queryWrapper);
    }

    @Override
    public int batchInsert(List<MerchantDeliveryStepFee> stepFees) {
        return baseMapper.batchInsert(stepFees);
    }

    @Override
    public Map<Long,List<MerchantDeliveryStepFee>> queryStepFee(List<Long> tenantIds) {
        LambdaQueryWrapper<MerchantDeliveryStepFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MerchantDeliveryStepFee::getTenantId, tenantIds);
        List<MerchantDeliveryStepFee> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(MerchantDeliveryStepFee::getTenantId));
    }

    @Override
    public boolean deleteStepFee(Long tenantId, Long ruleId) {
        LambdaQueryWrapper<MerchantDeliveryStepFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantDeliveryStepFee::getTenantId, tenantId);
        queryWrapper.eq(MerchantDeliveryStepFee::getRuleId, ruleId);
        return remove(queryWrapper);
    }
}
