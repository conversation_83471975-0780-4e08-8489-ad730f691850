package com.cosfo.manage.merchant.repository.impl;

import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderAnalysisQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreItemOrderOverviewDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreItemOrderAnalysis;
import com.cosfo.manage.report.mapper.MerchantStoreItemOrderAnalysisMapper;
import com.cosfo.manage.merchant.repository.MerchantStoreItemOrderAnalysisRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 门店商品订货分析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Service
public class MerchantStoreItemOrderAnalysisRepositoryImpl extends ServiceImpl<MerchantStoreItemOrderAnalysisMapper, MerchantStoreItemOrderAnalysis> implements MerchantStoreItemOrderAnalysisRepository {

    @Resource
    private MerchantStoreItemOrderAnalysisMapper merchantStoreItemOrderAnalysisMapper;

    @Override
    public MerchantStoreItemOrderOverviewDTO queryStoreItemOrderSum(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        return merchantStoreItemOrderAnalysisMapper.queryStoreItemOrderSum(merchantStoreItemOrderAnalysisQueryDTO);
    }

    @Override
    public List<MerchantStoreItemOrderAnalysis> listByCondition(MerchantStoreItemOrderAnalysisQueryDTO merchantStoreItemOrderAnalysisQueryDTO) {
        return merchantStoreItemOrderAnalysisMapper.listByCondition(merchantStoreItemOrderAnalysisQueryDTO);
    }
}
