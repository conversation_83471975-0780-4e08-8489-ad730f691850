package com.cosfo.manage.merchant.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryStepFee;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 阶梯运费 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
public interface MerchantDeliveryStepFeeRepository extends IService<MerchantDeliveryStepFee> {


    /**
     * 获取阶梯价
     * @param tenantId
     * @param ruleId
     * @return
     */
    List<MerchantDeliveryStepFee> queryStepFee(Long tenantId, Long ruleId);

    /**
     * 删除旧阶梯价
     * @param tenantId
     * @param ruleId
     * @return
     */
    boolean deleteStepFee(Long tenantId, Long ruleId);

    /**
     * 批量插入
     * @param stepFees
     * @return
     */
    int batchInsert(List<MerchantDeliveryStepFee> stepFees);

    /**
     * 获取阶梯价
     * @param tenantIds
     * @return
     */
    Map<Long,List<MerchantDeliveryStepFee>> queryStepFee(List<Long> tenantIds);

}
