package com.cosfo.manage.merchant.model.dto;


import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 门店商品维度订货分析VO
 * @author: George
 * @date: 2023-06-05
 **/
@Data
public class MerchantStoreOrderAnalysisDTO {


    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型 0、直营店 1、加盟 2、托管
     * @see com.cosfo.manage.common.context.MerchantStoreEnum.Type
     */
    private Integer storeType;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 平均订货周期
     */
    private BigDecimal averageOrderPeriod;

    /**
     * 平均订货周期较上周期
     */
    private BigDecimal averageOrderPeriodUpperPeriod;

    /**
     * 订货数量
     */
    private Integer orderAmount;

    /**
     * 订货数量较上周期
     */
    private BigDecimal orderAmountUpperPeriod;

    /**
     * 订货金额
     */
    private BigDecimal orderPrice;

    /**
     * 订货金额较上周期
     */
    private BigDecimal orderPriceUpperPeriod;

    /**
     * 最后订货日期 yyyy-MM-dd
     */
    private String lastOrderTime;

    /**
     * 最后订货数量
     */
    private Integer lastOrderAmount;

    /**
     * 最后订货金额
     */
    private BigDecimal lastOrderPrice;

    /**
     * 门店id
     */
    private Long storeId;
}
