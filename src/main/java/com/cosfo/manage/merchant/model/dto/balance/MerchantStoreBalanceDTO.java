package com.cosfo.manage.merchant.model.dto.balance;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class MerchantStoreBalanceDTO {

    /**
     * 门店ID
     */
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    /**
     * 凭证
     */
    private String proof;

    /**
     * 备注
     */
    private String remark;

    /**
     * 变动余额 （+ - 金额）
     */
    @DecimalMax(value = "*********", message = "调整额度不能大于*********")
    @NotNull(message = "调整额度不能为空")
    private BigDecimal changeBalance;

    /**
     * 资金账户ID--非现金状态下必填
     */
    private Long fundAccountId;

    /**
     * 账户类型 0=现金余额（默认），1=非现金账户
     * @see com.cosfo.manage.common.context.MerchantStoreBalanceEnums.AccountTypeEnum
     */
    private Integer accountType = 0;
}
