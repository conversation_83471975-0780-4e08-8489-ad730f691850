package com.cosfo.manage.tenant.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.model.dto.TenantQueryDTO;
import com.cosfo.manage.tenant.model.po.Tenant;
import com.cosfo.manage.tenant.model.vo.SupplierTenantVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Deprecated
@Mapper
public interface TenantMapper {
//
//    /**
//     * 删除
//     * @param id
//     * @return
//     */
//    int deleteByPrimaryKey(Long id);
//
//    /**
//     * 插入
//     * @param record
//     * @return
//     */
//    int insert(Tenant record);
//
//    /**
//     * 插入
//     * @param record
//     * @return
//     */
//    int insertSelective(Tenant record);
//
//    /**
//     * 查询
//     * @param id
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    Tenant selectByPrimaryKey(Long id);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKeySelective(Tenant record);
//
//    /**
//     * 更新
//     * @param record
//     * @return
//     */
//    int updateByPrimaryKey(Tenant record);
//
//    /**
//     * 根据手机号查询
//     *
//     * @param phone
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    Tenant selectByPhone(@Param("phone") String phone);
//
//    /**
//     * 查询供应商信息
//     *
//     * @param supplierTenantIds
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<Tenant> querySupplierInfoBySupplierTenantIds(@Param("supplierTenantIds") List<Long> supplierTenantIds);
//
//    /**
//     * 查询租户信息
//     *
//     * @param type
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    List<Tenant> queryTenantInfoByType(@Param("type")Integer type);
//
//    /**
//     * 查询所有租户
//     *
//     * @param tenantQueryDTO
//     * @return
//     */
//    List<Tenant> list(TenantQueryDTO tenantQueryDTO);
//    /**
//     * 查询所有租户信息
//     *
//     * @return
//     */
//    List<Tenant> queryEnableTenantInfos();
//
//    List<Tenant> listByIds(@Param("ids") Set<Long> ids);
//
//    /**
//     * 查询供应商信息
//     * @return
//     */
//    List<SupplierTenantVO> listSupplier();
//
//    /**
//     * 查询租户信息
//     * @param ids
//     * @return
//     */
//    List<SupplierTenantVO> listSupplierByIds(@Param("ids") Set<Long> ids);
//    /**
//     * 通过鲜沐大客户adminId查询品牌方租户信息
//     *
//     * @param adminId
//     * @return
//     */
//    Tenant selectByAdminId(@Param("adminId") Long adminId);
}
