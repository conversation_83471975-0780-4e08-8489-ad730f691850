package com.cosfo.manage.tenant.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.common.context.holder.TenantMetricsSummaryContextHolder;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.model.po.TenantMetricsSummary;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-11-13
 **/
@Service("measureItemOrderFulfillmentRate1DStrategy")
public class MeasureItemOrderFulfillmentRate1DStrategy implements TenantMeasureItemStrategy {

    @Resource
    private TenantMetricsSummaryContextHolder tenantMetricsSummaryContextHolder;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        String timeTag = DateUtil.yesterday().toString("yyyyMMdd");
        TenantMetricsSummary tenantMetricsSummary = tenantMetricsSummaryContextHolder.getTenantMetricsSummary(result.getTenantId(), timeTag);
        String orderFulfillmentRate = Objects.isNull(tenantMetricsSummary.getOrderFulfillmentRate1d()) ? StringConstants.SEPARATING_IN_LINE : tenantMetricsSummary.getOrderFulfillmentRate1d().toPlainString();
        String itemResult = String.format("%s（%s%%）", result.getItemTitle(), orderFulfillmentRate);
        result.setItemResult(itemResult);
        result.setItemResultState(TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
