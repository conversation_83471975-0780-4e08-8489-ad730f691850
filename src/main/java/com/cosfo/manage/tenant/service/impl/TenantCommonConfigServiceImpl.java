package com.cosfo.manage.tenant.service.impl;

import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.tenant.mapper.TenantCommonConfigMapper;
import com.cosfo.manage.tenant.model.input.TenantConfigInput;
import com.cosfo.manage.tenant.model.input.TenantNumberConfigInput;
import com.cosfo.manage.tenant.model.po.TenantCommonConfig;
import com.cosfo.manage.tenant.model.vo.TenantCommonConfigVO;
import com.cosfo.manage.tenant.model.vo.TenantNumberCommonConfigVO;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 租户公共配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-19
 */
@Service
@Slf4j
public class TenantCommonConfigServiceImpl implements TenantCommonConfigService {

    @Resource
    private TenantCommonConfigMapper tenantCommonConfigMapper;

    @Override
    public TenantCommonConfigVO selectTenantConfig(Long tenantId, String configKey) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户id不能为空");
        AssertCheckParams.notNull(configKey, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户配置key不能为空");
        TenantCommonConfigVO tenantCommonConfigVO = new TenantCommonConfigVO();
        TenantCommonConfig config = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, configKey);
        if (config == null) {
            TenantConfigEnum.TenantConfig tenantConfigEnum = TenantConfigEnum.TenantConfig.getConfigEnum(configKey);
            if (tenantConfigEnum == null) {
                throw new DefaultServiceException("租户配置不存在");
            }

            config = new TenantCommonConfig();
            config.setTenantId(tenantId);
            config.setConfigKey(tenantConfigEnum.getConfigKey());
            config.setConfigValue(tenantConfigEnum.getDefaultValue());
            config.setConfigDesc(tenantConfigEnum.getConfigDesc());
            tenantCommonConfigMapper.insert(config);
        }

        tenantCommonConfigVO.setConfigKey(config.getConfigKey());
        tenantCommonConfigVO.setConfigValue(config.getConfigValue());
        tenantCommonConfigVO.setConfigDesc(config.getConfigDesc());
        return tenantCommonConfigVO;
    }

    @Override
    public void updateTenantConfig(Long tenantId, TenantConfigInput tenantConfigInput) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户id不能为空");
        AssertCheckParams.notNull(tenantConfigInput, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户配置不能为空");
        AssertCheckParams.notNull(tenantConfigInput.getConfigCode(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户配置key不能为空");
        AssertCheckParams.notNull(tenantConfigInput.getConfigValue(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户配置value不能为空");
        TenantCommonConfig config = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, tenantConfigInput.getConfigCode());
        if (config == null) {
            throw new DefaultServiceException("租户配置不存在");
        }

        // 更新租户配置值
        config.setConfigValue(tenantConfigInput.getConfigValue());
        tenantCommonConfigMapper.updateConfigValueById(config);
    }

    @Override
    public TenantNumberCommonConfigVO queryTenantConfig(Long tenantId, String configKey) {
        TenantNumberCommonConfigVO tenantNumberCommonConfigVO = new TenantNumberCommonConfigVO();
        TenantCommonConfigVO tenantCommonConfigVO = selectTenantConfig(tenantId, configKey);
        tenantNumberCommonConfigVO.setConfigDesc(tenantCommonConfigVO.getConfigDesc());
        tenantNumberCommonConfigVO.setConfigValue(Integer.valueOf(tenantCommonConfigVO.getConfigValue()));
        tenantNumberCommonConfigVO.setConfigKey(tenantCommonConfigVO.getConfigKey());
        return tenantNumberCommonConfigVO;
    }

    @Override
    public void numberUpdate(Long tenantId, TenantNumberConfigInput tenantConfigInput) {
        AssertCheckParams.allNotNull(ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.getMessage(), tenantConfigInput, tenantConfigInput.getConfigCode(), tenantConfigInput.getConfigValue());
        TenantConfigInput updateInput = new TenantConfigInput();
        updateInput.setConfigCode(tenantConfigInput.getConfigCode());
        updateInput.setConfigValue(tenantConfigInput.getConfigValue().toString());
        updateTenantConfig(tenantId, updateInput);
    }

    @Override
    public TenantCommonConfig selectByTenantIdAndConfigKey(Long tenantId, String configKey) {
        return tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, configKey);
    }

    @Override
    public void insert(TenantCommonConfig tenantCommonConfig) {
        tenantCommonConfigMapper.insert(tenantCommonConfig);
    }
}
