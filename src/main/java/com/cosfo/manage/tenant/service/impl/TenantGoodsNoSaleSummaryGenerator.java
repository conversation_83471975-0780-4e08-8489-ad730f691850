package com.cosfo.manage.tenant.service.impl;


import cn.hutool.core.lang.Pair;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.Global;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.market.model.po.MarketItemCompositeMarket;
import com.cosfo.manage.market.repository.MarketItemCompositeGoodsRepository;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.report.model.dto.GoodsNoSaleSummaryExcelDTO;
import com.cosfo.manage.report.model.po.GoodsNoSaleSummary;
import com.cosfo.manage.report.repository.GoodsNoSaleSummaryRepository;
import com.cosfo.manage.report.service.ExcelGenerator;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class TenantGoodsNoSaleSummaryGenerator implements ExcelGenerator<Void> {

    @Resource
    private GoodsNoSaleSummaryRepository goodsNoSaleSummaryRepository;
    @Resource
    private CommonService commonService;
    @Resource
    private ProductFacade productFacade;
    @Resource
    private MarketItemCompositeGoodsRepository marketItemCompositeGoodsRepository;

    @Override
    public String getExcelName(Void query) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return String.format("截至昨日%s连续15天滞销货品.xlsx", yesterday.format(formatter));
    }

    @Override
    public String generateExcelAndReturnFilePath(Long tenantId, Void query) {
        String timeTag = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<GoodsNoSaleSummaryExcelDTO> goodsNoSaleSummaries = queryGoodsNoSaleSummary(tenantId, timeTag);
        return commonService.exportExcel(goodsNoSaleSummaries, ExcelTypeEnum.GOODS_NO_SALE_SUMMARY.getName());
    }

    private List<GoodsNoSaleSummaryExcelDTO> queryGoodsNoSaleSummary(Long tenantId, String timeTag) {
        List<GoodsNoSaleSummary> goodsNoSaleSummaries = goodsNoSaleSummaryRepository.listByTenantAndTimeTag(tenantId, timeTag);
        if (CollectionUtils.isEmpty(goodsNoSaleSummaries)) {
            return Collections.emptyList();
        }

        // 商品货品id
        List<Long> skuIds = goodsNoSaleSummaries.stream().map(GoodsNoSaleSummary::getSkuId).distinct().collect(Collectors.toList());
        List<Long> itemIds = goodsNoSaleSummaries.stream().map(GoodsNoSaleSummary::getItemId).distinct().collect(Collectors.toList());
        Pair<Map<Long, MarketItemCompositeMarket>, Map<Long, ProductSkuDTO>> itemSkuMapPair = marketItemCompositeGoodsRepository.queryByIds(tenantId, itemIds, skuIds);
        Map<Long, ProductSkuDTO> skuInfoMap = itemSkuMapPair.getValue();
        Map<Long, MarketItemCompositeMarket> marketInfoMap = itemSkuMapPair.getKey();

        // 组装参数
        ProductSkuDTO emptySku = new ProductSkuDTO();
        MarketItemCompositeMarket emptyMarket = new MarketItemCompositeMarket();
        return goodsNoSaleSummaries.stream().map(item -> {
            ProductSkuDTO productSkuDTO = skuInfoMap.getOrDefault(item.getSkuId(), emptySku);
            MarketItemCompositeMarket marketItem = marketInfoMap.getOrDefault(item.getItemId(), emptyMarket);
            return GoodsNoSaleSummaryExcelDTO.builder()
                    .skuId(productSkuDTO.getId())
                    .title(productSkuDTO.getTitle())
                    .specification(Global.subSpecification(productSkuDTO.getSpecification()))
                    .firstCategory(productSkuDTO.getFirstCategory())
                    .secondCategory(productSkuDTO.getSecondCategory())
                    .thirdCategory(productSkuDTO.getThirdCategory())
                    .firstClassificationName(marketItem.getFirstClassificationName())
                    .secondClassificationName(marketItem.getSecondClassificationName())
                    .itemId(marketItem.getItemId())
                    .itemTitle(marketItem.getTitle())
                    .itemSpecification(Global.subSpecification(marketItem.getSpecification()))
                    .salePrice(item.getSalePrice())
                    .build();
        }).collect(Collectors.toList());
    }
}
