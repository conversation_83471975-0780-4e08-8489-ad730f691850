package com.cosfo.manage.tenant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.context.MerchantStoreAccountStatusEnum;
import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.context.TenantEnums;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.facade.AuthUserFacade;
import com.cosfo.manage.facade.PriceFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantInfoFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantAccountFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.tenant.convert.TenantAccountMapperConvert;
import com.cosfo.manage.tenant.convert.TenantConvert;
import com.cosfo.manage.tenant.convert.TenantMapperConvert;
import com.cosfo.manage.tenant.model.dto.*;
import com.cosfo.manage.tenant.model.input.TenantConfigInput;
import com.cosfo.manage.tenant.model.input.TenantUnfairPriceStrategyInput;
import com.cosfo.manage.tenant.model.po.*;
import com.cosfo.manage.tenant.model.vo.*;
import com.cosfo.manage.tenant.service.TenantAccountService;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.cosfo.manage.tenant.service.TenantCompanyAccountService;
import com.cosfo.manage.tenant.service.TenantCompanyService;
import com.cosfo.manage.tenant.service.TenantService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthUserRoleDto;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.common.utils.AuthUserUtils;
import net.xianmu.authentication.model.DTO.ShiroUserExtendDto;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.tenant.req.TenantAccountQueryReq;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.shiro.authc.AccountException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.cosfo.manage.common.context.TenantConfigEnum.TenantConfig.SUPPLIER_DISTRIBUTOR_ROLE_ID;

@Slf4j
@Service
public class TenantServiceImpl implements TenantService {
    @Resource
    private TenantAccountService tenantAccountService;
    @Resource
    private AuthUserFacade authUserFacade;
    @Resource
    private TenantCompanyAccountService tenantCompanyAccountService;
    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;
    @Resource
    private UserCenterMerchantInfoFacade userCenterMerchantInfoFacade;
    @Resource
    private TenantCompanyService tenantCompanyService;
    @Resource
    private PriceFacade priceFacade;

    @Override
    public List<Tenant> querySupplierInfoBySupplierTenantIds(List<Long> supplierTenantIds) {
        AssertCheckParams.notNull(supplierTenantIds, ResultDTOEnum.PARAMETER_MISSING.getCode(), "supplierTenantIds不能为空");
        List<TenantResultResp> resultResps = userCenterTenantFacade.getTenantsByIds(supplierTenantIds);
        List<Tenant> tenantList = TenantConvert.respToTenantList(resultResps);
//        return tenantMapper.querySupplierInfoBySupplierTenantIds(supplierTenantIds);
        return tenantList;
    }

    @Override
    public TenantDTO queryTenantById(Long tenantId) {
//        Tenant tenant = tenantMapper.selectByPrimaryKey(tenantId);
        TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);
        TenantDTO tenantDTO = new TenantDTO();
        // 查询公司信息
        BeanUtils.copyProperties(tenant, tenantDTO);
        TenantCompany tenantCompany = tenantCompanyService.selectByTenantId(tenantId);
//        TenantCompany tenantCompany = tenantCompanyMapper.selectByTenantId(tenantId);
        if(Objects.nonNull(tenantCompany)) {
            tenantDTO.setCompanyName(tenantCompany.getCompanyName());
        }

        return tenantDTO;
    }

    @Override
    public ResultDTO<List<TenantVO>> queryTenantInfo(Integer type) {
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setType(type);
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(tenantQueryReq);
//        List<Tenant> tenants = tenantMapper.queryTenantInfoByType(type);
        List<TenantVO> tenantVOs = tenants.stream().map(item -> {
            TenantVO tenantVO = new TenantVO();
            BeanUtils.copyProperties(item, tenantVO);
            return tenantVO;
        }).collect(Collectors.toList());
        return ResultDTO.success(tenantVOs);
    }

    @Override
    public TenantInfoDTO getTenantInfo(String token) {
        // 获取缓存信息
        LoginContextInfoDTO loginContextInfoDTO = authUserFacade.getTenantInfo(token);
        if (Objects.isNull(loginContextInfoDTO)) {
            return null;
        }
        TenantResultResp tenant = userCenterTenantFacade.getTenantById(loginContextInfoDTO.getTenantId());
//        Tenant tenant = tenantMapper.selectByPrimaryKey(loginContextInfoDTO.getTenantId());
        TenantInfoDTO tenantInfoDTO = new TenantInfoDTO();
        tenantInfoDTO.setTenantId(loginContextInfoDTO.getTenantId());
        tenantInfoDTO.setAdminId(tenant.getAdminId());
        tenantInfoDTO.setName(tenant.getTenantName());
        tenantInfoDTO.setPhone(loginContextInfoDTO.getPhone());
        // 获取登录人信息
        TenantAccount tenantAccount = tenantAccountService.selectByAuthUserId(loginContextInfoDTO.getAuthUserId());
        tenantInfoDTO.setTenantAccountId(tenantAccount.getId());
        tenantInfoDTO.setNickName(tenantAccount.getNickname());
        // 增加公司名称
//        TenantCompany tenantCompany = tenantCompanyMapper.selectByTenantId(tenant.getId());
        TenantCompany tenantCompany = tenantCompanyService.selectByTenantId(tenant.getId());
        if(Objects.nonNull(tenantCompany)){
            tenantInfoDTO.setCompanyName(tenantCompany.getCompanyName());
        }
        return tenantInfoDTO;
    }

    @Override
    public List<TenantDTO> list(TenantQueryDTO tenantQueryDTO) {
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setAdminId(tenantQueryDTO.getAdminId());
        tenantQueryReq.setId(tenantQueryDTO.getTenantId());
        tenantQueryReq.setTenantIdList(tenantQueryDTO.getTenantIds());
        tenantQueryReq.setType(TenantEnums.type.BRAND_PARTY.getCode());
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(tenantQueryReq);

//        List<Tenant> tenants = tenantMapper.list(tenantQueryDTO);
        if (CollectionUtils.isEmpty(tenants)) {
            return Collections.EMPTY_LIST;
        }

        // 批量查询公司名称
        List<Long> tenantIds = tenants.stream().map(TenantResultResp::getId).collect(Collectors.toList());
        List<BusinessInformationResultResp> businessInformationResultResps = tenantCompanyService.batchQueryByTenantIds(tenantIds);
        Map<Long, BusinessInformationResultResp> tenantCompanyMap = businessInformationResultResps.stream().collect(Collectors.toMap(BusinessInformationResultResp::getTenantId, item -> item, (v1, v2) -> v1));
        List<TenantDTO> tenantDTOS = tenants.stream().map(tenant -> {
            TenantDTO tenantDTO = TenantMapperConvert.INSTANCE.respToTenantDto(tenant);
            BusinessInformationResultResp businessInformationResultResp = tenantCompanyMap.get(tenant.getId());
            if (Objects.nonNull(businessInformationResultResp)) {
                tenantDTO.setCompanyName(businessInformationResultResp.getCompanyName());
            }

            return tenantDTO;
        }).collect(Collectors.toList());
//        List<TenantCompany> tenantCompanyList = tenantCompanyMapper.batchQueryByTenantIds(tenantIds);
//        Map<Long, TenantCompany>  tenantCompanyMap = tenantCompanyList.stream().collect(Collectors.toMap(TenantCompany::getTenantId, item -> item));
//        List<TenantDTO> tenantDTOS = tenants.stream().map(tenant -> {
//            TenantDTO tenantDTO = new TenantDTO();
//            BeanUtils.copyProperties(tenant, tenantDTO);
//            TenantCompany tenantCompany = tenantCompanyMap.get(tenant.getId());
//            if(Objects.nonNull(tenantCompany)){
//                tenantDTO.setCompanyName(tenantCompany.getCompanyName());
//            }
//
//            return tenantDTO;
//        }).collect(Collectors.toList());
        return tenantDTOS;
    }

    @Override
    public PageInfo<LoginAccountTenantVO> queryBindTenantByPhone(BindTenantQueryDTO bindTenantQueryDTO) {
        if(StrUtil.isBlank(bindTenantQueryDTO.getPhone()) && StrUtil.isBlank(bindTenantQueryDTO.getEmail())) {
            throw new ParamsException("手机号和邮箱必须填写一个!");
        }

        List<TenantAccount> tenantAccounts;
        if(StrUtil.isNotBlank(bindTenantQueryDTO.getPhone())) {
            tenantAccounts = tenantAccountService.selectByPhone(bindTenantQueryDTO.getPhone(), null);
        } else {
            TenantAccountQueryReq req = new TenantAccountQueryReq ();
            req.setEmail (bindTenantQueryDTO.getEmail());
            List<TenantAccountResultResp>  tenantAccountResultResps = userCenterTenantAccountFacade.getTenantAccounts (req);
            tenantAccounts = TenantAccountMapperConvert.INSTANCE.respListToTenantAccountList(tenantAccountResultResps);
        }
        List<Long> tenantIdList = tenantAccounts.stream().map(TenantAccount::getTenantId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return PageInfoHelper.createPageInfo(Lists.newArrayList(), bindTenantQueryDTO.getPageSize());
        }

        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setTenantName(bindTenantQueryDTO.getName());
        tenantQueryReq.setTenantIdList(tenantIdList);
        tenantQueryReq.setType (TenantEnums.type.BRAND_PARTY.getCode());
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(bindTenantQueryDTO.getPageIndex());
        pageQueryReq.setPageSize(bindTenantQueryDTO.getPageSize());
        PageInfo<TenantAndBusinessInfoResultResp> tenantAndCompanyPage = userCenterTenantFacade.getTenantAndCompanyPage(tenantQueryReq, pageQueryReq);
        List<LoginAccountTenantVO> tenantVOS = TenantConvert.respConvertToLoginAccountTenantVOList(tenantAndCompanyPage.getList());
        fillMerchantName(tenantVOS);
        fillRole(tenantAccounts,tenantVOS);
        return PageInfoHelper.pageInfoCopy(tenantAndCompanyPage, tenantVOS);
    }

    private void fillRole(List<TenantAccount> tenantAccounts,List<LoginAccountTenantVO> tenantVOS) {
        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(tenantAccounts.stream ().map (TenantAccount::getAuthUserId).collect(Collectors.toList()));
        if(CollectionUtil.isEmpty (authUserRoleDtos)){
            return;
        }
        Map<Long, List<AuthRole>> roleMap = authUserRoleDtos.stream()
                .filter(e -> CollectionUtil.isNotEmpty(e.getRoles()))
                .flatMap(e -> e.getRoles().stream())
                .collect(Collectors.groupingBy(AuthRole::getTenantId));
        if(CollectionUtil.isEmpty (roleMap)){
            return;
        }
        tenantVOS.forEach (e->{
            Long id = e.getId ();
            List<AuthRole> list = roleMap.get (id);
            if(CollectionUtil.isNotEmpty (list)){
                String roleName = list.stream ().map (AuthRole::getRolename).collect (Collectors.joining (","));
                e.setRoleName (roleName);
            }
        });
    }

    private void fillMerchantName(List<LoginAccountTenantVO> tenantVOS) {
        if(CollectionUtil.isNotEmpty (tenantVOS)){
            List<Long> tenantIds = tenantVOS.stream ().map (TenantVO::getId).collect (Collectors.toList ());
            List<MerchantResultResp> merchantResultResps = userCenterMerchantInfoFacade.getMerchantByTenantIds (tenantIds);
            Map<Long, String> merchartMap = merchantResultResps.stream ().collect (Collectors.toMap (MerchantResultResp::getTenantId, MerchantResultResp::getMerchantName));
            tenantVOS.forEach (e->{
                Long tenantId = e.getId ();
                e.setMerchantName (merchartMap.get (tenantId));
            });
        }
    }

    @Override
    public List<TenantResultResp> listByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.EMPTY_LIST;
        }
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByIds(Lists.newArrayList(ids));
//        List<Tenant> tenants = tenantMapper.listByIds(ids);
        if (CollectionUtils.isEmpty(tenants)) {
            return Collections.EMPTY_LIST;
        }
        return tenants;
    }

    @Override
    public List<SupplierTenantVO> querySupplierList() {
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setType(TenantEnums.type.SUPPLIER.getCode());
        List<TenantAndBusinessInfoResultResp> resultRespList = userCenterTenantFacade.getTenantAndCompanyByQuery(tenantQueryReq);
        List<SupplierTenantVO> supplierTenantVOS = TenantConvert.respToSupplierTenantVoList(resultRespList);
//        List<SupplierTenantVO> supplierTenantVOS = tenantMapper.listSupplier();
        if (CollectionUtils.isEmpty(supplierTenantVOS)) {
            return new ArrayList<>();
        }
        Map<Long, TenantCompanyAccount> accountMap = tenantCompanyAccountService.queryAccountMap(supplierTenantVOS.stream().map(SupplierTenantVO::getTenantId).collect(Collectors.toSet()), UserLoginContextUtils.getTenantId());
        if (accountMap == null) {
            return supplierTenantVOS;
        }
        supplierTenantVOS.forEach(s -> {
            TenantCompanyAccount tenantCompanyAccount = accountMap.get(s.getTenantId());
            if (tenantCompanyAccount != null) {
                s.setAccountName(tenantCompanyAccount.getAccountName());
                s.setAccountNumber(tenantCompanyAccount.getAccountNumber());
                s.setOpeningBank(tenantCompanyAccount.getOpeningBank());
            }
        });
        return supplierTenantVOS;
    }

    @Override
    public TenantCompanyDetailVO queryTenantCompanyDetail(Long tenantId) {
//        Tenant tenant = tenantDao.getById(tenantId);
        TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);
//        TenantCompany tenantCompany = tenantCompanyMapper.selectByTenantId(tenantId);
        TenantCompany tenantCompany = tenantCompanyService.selectByTenantId(tenantId);
        TenantCompanyDetailVO detailVO = new TenantCompanyDetailVO();
        detailVO.setTenantName(tenant.getTenantName());
        detailVO.setCompanyName(tenantCompany.getCompanyName());
        detailVO.setTenantId(tenantId);
        return detailVO;
    }

    /**
     * 获取供应商map
     *
     * @param ids
     * @return
     */
    @Override
    public Map<Long, SupplierTenantVO> querySupplierMap(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.EMPTY_MAP;
        }
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setTenantIdList(Lists.newArrayList(ids));
        List<TenantAndBusinessInfoResultResp> tenantAndBusinessInfoResultRespList = userCenterTenantFacade.getTenantAndCompanyByQuery(tenantQueryReq);
        List<SupplierTenantVO> tenants = TenantConvert.respToSupplierTenantVoList(tenantAndBusinessInfoResultRespList);
//        List<SupplierTenantVO> tenants = tenantMapper.listSupplierByIds(ids);
        return tenants.stream().collect(Collectors.toMap(SupplierTenantVO::getTenantId, t -> t));
    }

    @Override
    public List<TenantCustomerPhoneVO> queryTenantCustomerPhone(Long tenantId) {
        TenantCommonConfigVO commonConfig = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfigEnum.TenantConfig.CUSTOMER_PHONE.getConfigKey());
        if (commonConfig == null || StringUtil.isBlank(commonConfig.getConfigValue())) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(commonConfig.getConfigValue(), TenantCustomerPhoneVO.class);
    }

    @Override
    public Boolean modifyTenantCustomerPhone(CustomerPhoneModifyDTO customerPhoneModifyDTO) {
        TenantConfigInput configInput = new TenantConfigInput();
        configInput.setConfigCode(TenantConfigEnum.TenantConfig.CUSTOMER_PHONE.getConfigKey());
        configInput.setConfigValue(JSON.toJSONString(customerPhoneModifyDTO.getCustomerPhoneList()));
        tenantCommonConfigService.updateTenantConfig(customerPhoneModifyDTO.getTenantId(), configInput);
        return true;
    }

    @Override
    public TenantDTO selectByAdminId(Long adminId) {
        AssertCheckParams.notNull(adminId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "adminId不能为空");
        List<TenantResultResp> tenants = userCenterTenantFacade.getTenantsByQuery(TenantQueryReq.builder().adminId(adminId).build());
        if(CollectionUtils.isEmpty(tenants)){
            return null;
        }
        TenantResultResp tenant = tenants.get(NumberConstant.ZERO);
//        Tenant tenant = tenantMapper.selectByAdminId(adminId);
//        if(Objects.isNull(tenant)){
//            return null;
//        }

        TenantDTO tenantDTO = new TenantDTO();
        BeanUtils.copyProperties(tenant, tenantDTO);
        return tenantDTO;
    }

    @Override
    public void upsertUnfairPriceStrategy(Long tenantId, TenantUnfairPriceStrategyInput input) {
        priceFacade.upsertDefaultUnfairPriceStrategy (tenantId,input.getStrategyType ());
    }

    @Override
    public Integer queryUnfairPriceStrateg(Long tenantId) {
        return priceFacade.queryDefaultUnfairPriceStrategy(tenantId).getCode ();
    }

    @Override
    public String queryPrivateProcurement(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfig = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfigEnum.TenantConfig.PRIVATE_PROCUREMENT.getConfigKey());
        return tenantCommonConfig.getConfigValue ();
    }

    @Override
    public void saveOrUpdatePrivateProcurement(Long tenantId,TenantConfigInput tenantConfigInput) {
        tenantConfigInput.setConfigCode (TenantConfigEnum.TenantConfig.PRIVATE_PROCUREMENT.getConfigKey());
        tenantCommonConfigService.updateTenantConfig(tenantId, tenantConfigInput );
    }

    @Override
    public PageInfo<TenantDTO> queryTenantInfoByPage(TenantAccountQueryDTO queryDTO) {
        TenantQueryReq queryReq = new TenantQueryReq();
        queryReq.setType(TenantEnums.type.BRAND_PARTY.getCode());
        queryReq.setTenantName(queryDTO.getName());

        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(queryDTO.getPageIndex());
        pageQueryReq.setPageSize(queryDTO.getPageSize());
        PageInfo<TenantAndBusinessInfoResultResp> tenantAndCompanyPage = userCenterTenantFacade.getTenantAndCompanyPage(queryReq, pageQueryReq);

        return TenantMapperConvert.INSTANCE.respToTenantDtoPageInfo(tenantAndCompanyPage);
    }
    @Override
    public TenantDTO getTenantBaseInfo(Long tenantId) {
        AssertCheckParams.notNull(tenantId, ResultStatusEnum.SERVER_ERROR.getStatus(), "tenantId不能为空");
        TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);
        if (Objects.isNull(tenant)) {
            return null;
        }
        TenantDTO tenantDTO = TenantMapperConvert.INSTANCE.respToTenantDto(tenant);
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigService.selectByTenantIdAndConfigKey(tenantId, TenantConfigEnum.TenantConfig.TENANT_BRAND_NAME.getConfigKey());
        String tenantBrandName = tenant.getTenantName();
        if (Objects.nonNull(tenantCommonConfig)) {
            tenantBrandName = tenantCommonConfig.getConfigValue();
        }
        tenantDTO.setTenantBrandName(tenantBrandName);
        return tenantDTO;
    }

}
