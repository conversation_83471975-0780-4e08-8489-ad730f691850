package com.cosfo.manage.open.repository;

import com.cosfo.manage.open.model.po.OpenDocCodeMapping;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 外部单据映射关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface OpenDocCodeMappingRepository extends IService<OpenDocCodeMapping> {

    OpenDocCodeMapping queryByDocCode(String orderNo, Long tenantId, Integer docType,Integer openType);
}
