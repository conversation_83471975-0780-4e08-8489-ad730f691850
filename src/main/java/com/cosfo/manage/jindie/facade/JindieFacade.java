package com.cosfo.manage.jindie.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.bill.model.dto.OrderItemPriceDetailDTO;
import com.cosfo.manage.bill.service.PaymentService;
import com.cosfo.manage.cache.InMemoryCache;
import com.cosfo.manage.jindie.service.JindieMaterialService;
import com.kingdee.service.data.api.*;
import com.cosfo.manage.bill.model.dto.PaymentQueryDTO;
import com.cosfo.manage.jindie.config.JinDieConfig;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.kingdee.service.ApiClient;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.kingdee.service.ApiException;
import com.kingdee.service.Configuration;
import com.kingdee.service.data.entity.*;
import com.kingdee.service.unit.SHAUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.security.ProviderException;
import java.time.Duration;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
/**
 * 金蝶API调用门面
 */
@Service
@Slf4j
public class JindieFacade {

    @Autowired
    private JinDieConfig jinDieConfig;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private JindieMaterialService jindieMaterialService;


    // Token缓存，避免频繁获取token
    private final LoadingCache<String, String> TOKEN_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1)
            .recordStats()
            .expireAfterWrite(Duration.ofMinutes(10)) // 10分钟过期
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) {
                    return getTokenInternal();
                }
            });

    /**
     * 获取金蝶API Token
     *
     * @return Token字符串
     */
    public String getToken() {
        try {
            log.info("从缓存中获取金蝶token");
            return TOKEN_CACHE.get("jindieToken");
        } catch (Exception e) {
            log.warn("从缓存中获取金蝶token失败", e);
            return getTokenInternal();
        }
    }

    /**
     * 内部方法，直接调用API获取Token
     *
     * @return Token字符串
     */
    private String getTokenInternal() {
        try {
            AppTokenApi appTokenApi = new AppTokenApi();
            String appSignature = getApiSignature();
            AsterAppTokenRes asterAppTokenRes = appTokenApi.asterAppToken(jinDieConfig.getZcwAppKey(), appSignature, null);
            if(asterAppTokenRes != null) {
                return asterAppTokenRes.getAppToken ();
            }else{
                log.info ("从缓存中获取金蝶token失败,asterAppTokenRes={}", asterAppTokenRes);
                return "";
            }
        } catch (ApiException e) {
            log.error("获取金蝶token失败", e);
            throw new ProviderException("获取金蝶token失败", e);
        }
    }

    private String getApiSignature() {
        String appSignature = SHAUtil.SHA256HMAC(jinDieConfig.getZcwAppKey(), jinDieConfig.getZcwAppSecret());
        appSignature = Base64.getEncoder().encodeToString(appSignature.getBytes());
        return appSignature;
    }

    public SaveReply salOrderSaveCall(SalOrderSaveReq req) throws ApiException {
        ApiClient defaultApiClient = Configuration.getDefaultApiClient();
        defaultApiClient.setAppToken(getToken());
        SalOrderApi salOrderApi = new SalOrderApi ();

        return salOrderApi.salOrderSave (req);
    }
    public SalOrderSaveReq buildSalOrderSaveReq(OrderVO order,Map<String, String> unitMap){
        SalOrderSaveReq req = new SalOrderSaveReq();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //预计配送日期
        if (Objects.nonNull(order.getOrderTime ())) {
            req.setBillDate (formatter.format(order.getOrderTime ()));
        }
        //门店编码
        req.setCustomerId (customerCustomerDetail(order.getStoreNo ()));
        req.setAllocateRule ("1");
        req.setBillNo (order.getOrderNo ());
        //地址
        if (Objects.nonNull(order.getOrderAddressVO ())) {
            req.setContactAddress (order.getOrderAddressVO ().getAddress ());
        }
        List<OrderItemVO> orderItemVOS = order.getOrderItemVOS ();
        //查询订单项支付明细
        PaymentQueryDTO queryDTO = new PaymentQueryDTO ();
        queryDTO.setOrderId (order.getOrderId ());
        queryDTO.setTenantId (order.getTenantId ());
        List<OrderItemPriceDetailDTO> paymentItemDTOS = paymentService.queryPayDetail (queryDTO,order);

        Map<Long, Map<Integer, BigDecimal>> itemPriceMap = paymentItemDTOS.stream ().collect (Collectors.toMap (OrderItemPriceDetailDTO::getOrderItemId, OrderItemPriceDetailDTO::getPriceMap));
        List<SalOrderSaveReqMaterialEntity> materialEntityList = new ArrayList<> ();
        if(CollectionUtil.isNotEmpty (orderItemVOS)){
            materialEntityList = orderItemVOS.stream().map ( item->{
                SalOrderSaveReqMaterialEntity materialEntity = new SalOrderSaveReqMaterialEntity ();
                //是否赠品
                materialEntity.setIsFree (false);
                //商品自有编码
                MaterialDetailReply materialDetail = jindieMaterialService.getMaterialDetail (null, item.getItemCode ());

                materialEntity.setMaterialId (materialDetail == null? item.getItemCode () :materialDetail.getId () );

                materialEntity.setMaterialNumber (item.getItemCode ());
                //单价
                materialEntity.setTaxPrice (item.getPrice ().doubleValue ());
                //数量
                materialEntity.setQty (item.getAmount ().doubleValue ());
                //单位
                materialEntity.setUnitId (unitMap.getOrDefault (item.getSpecificationUnit (), item.getSpecificationUnit ()));
//                价税合计
//                materialEntity.setTaxAmount (item.getTotalPrice ().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//                整单折前价税合计	整单折前
//                materialEntity.setBillDisBeforeAmount (item.getTotalPrice ().doubleValue ());
                //折扣金额
//                materialEntity.setDisAmount (itemPriceMap.getOrDefault (item.getId (), new HashMap<> ()).getOrDefault (1, BigDecimal.ZERO).doubleValue ());
                //折前
                materialEntity.setPreDisAmount (item.getTotalPrice ().doubleValue ());
//                    //含税单价（根据设置与price二选一）
//                    materialEntity.setTaxPrice ();
                return materialEntity;
            }).collect(Collectors.toList());
            //商品
            req.setMaterialEntity (materialEntityList);
        }
//        req.setTotalAmount (order.getTotalPrice ().doubleValue ());
        BigDecimal deliveryFee = order.getDeliveryFee ();
        if(deliveryFee != null && deliveryFee.compareTo(BigDecimal.ZERO) > 0){
            SalOrderSaveReqCusBearFeeEntry bearFeeEntry = new SalOrderSaveReqCusBearFeeEntry ();
            String deliveryFeeConfigId = getDeliveryFeeConfigId ();
            if(StringUtils.isNotBlank (deliveryFeeConfigId)){
                bearFeeEntry.setIncomeNameId(deliveryFeeConfigId);
                bearFeeEntry.setIncomeAmount(deliveryFee.doubleValue ()+"");
                req.setCusBearFeeEntry (Collections.singletonList (bearFeeEntry));
            }
        }
        //整单折扣金额
//        req.setBillDisAmount (paymentService.queryWithDiscountByOrder(order).doubleValue ());
        req.setBillDisAmount (orderItemVOS.stream().map(item->itemPriceMap.getOrDefault (item.getId (), new HashMap<> ()).getOrDefault (1, BigDecimal.ZERO).doubleValue ()).reduce(0.0, Double::sum));
        return req;
    }

    /**
     * 查询商品单位id
     * @return
     */
    @InMemoryCache
    public Map<String,String> unitMeasureUnitMap() throws ApiException {
        ApiClient defaultApiClient = Configuration.getDefaultApiClient();
        defaultApiClient.setAppToken(getToken());

        UnitApi unitApi = new UnitApi ();
        UnitMeasureUnitListReq body = new UnitMeasureUnitListReq ();
        body.setPageSize ("200");
        MeasureUnitListRes measureUnitListRes = unitApi.unitMeasureUnitList (body);
        if(measureUnitListRes != null){
            return measureUnitListRes.getRows ().stream ().collect (Collectors.toMap (MeasureUnitListResRow::getName, MeasureUnitListResRow::getId));
        }
        return Collections.emptyMap ();
    }
    /**
     * 查询运费收入项id
     * @return
     */
    @InMemoryCache
    public String getDeliveryFeeConfigId()  {
        ApiClient defaultApiClient = Configuration.getDefaultApiClient();
        defaultApiClient.setAppToken(getToken());

        IncomeApi api = new IncomeApi ();
        IncomePacctTypeListReq body = new IncomePacctTypeListReq ();
        body.setSearch ("运费");
        body.setPageSize ("200");
        try {
            PacctTypeListRes resp = api.incomePacctTypeList (body);
            if(resp != null && CollectionUtil.isNotEmpty (resp.getRows ())){
                return resp.getRows ().stream ().filter (e->"运费".equals (e.getName ())).findFirst ().orElse (new PacctTypeListResRow ()).getId ();
            }
        } catch (ApiException e) {
            log.error ("查询运费收入项id，",e);
            return "";
        }
        return "";
    }
    /**
     * 查询客户id
     * @return
     */
    public String customerCustomerDetail(String code){
        ApiClient defaultApiClient = Configuration.getDefaultApiClient();
        defaultApiClient.setAppToken(getToken());

        CustomerApi customerApi = new CustomerApi();
        CustomerDetailRes customerDetailRes;
        try {
            customerDetailRes = customerApi.customerCustomerDetail (null, code, "false", "false", "false", "false");
        } catch (ApiException e) {
            log.error ("查询客户id异常，",e);
            return null;
        }
        if(customerDetailRes != null){
            return customerDetailRes.getId ();
        }else{
            log.info ("金蝶客户id查询结果为空，mycode={}",code);
        }
        return null;
    }

//    public void salOrderSaveCall(Payment payment,) {
//        ArCreditApi arCreditApi = new ArCreditApi ();
//        ArCreditSaveRequest body = new ArCreditSaveRequest ();
//        body.setBillDate (payment.getTradeState ());
//        //门店编码
//        body.setCustomerId ();
//        List<ArCreditSaveRequestPayEntry> payEntryList = new ArrayList<> ();
//        ArCreditSaveRequestPayEntry payEntry = new ArCreditSaveRequestPayEntry ();
//        payEntry.setAmount ();
//        payEntry.setSettleAccountId ();
//        payEntryList.add (payEntry)
//        body.setPayEntryList (payEntryList);
//
//        List<ArCreditSaveRequestSourceBillEntry> sourceBillEntryList = new ArrayList<> ();
//        body.setSourceBillEntryList ();
//        try {
//            arCreditApi.arCreditSave (body);
//        } catch (ApiException e) {
//            log.error ("调用金蝶salOrderSave失败，",e);
//        }
//    }
}