package com.cosfo.manage.jindie.service;

import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.kingdee.service.data.entity.SaveReply;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 金蝶销售退货单服务接口
 */
public interface JindieSalesReturnService {

    /**
     * 推送售后单到金蝶
     *
     * @param afterSaleResp 售后单
     * @return 推送结果
     */
    SaveReply pushSalesReturnToJindie(OrderAfterSaleResp afterSaleResp);

    /**
     * 批量推送售后单到金蝶
     *
     * @param afterSaleOrders 售后单列表
     * @return 推送成功的售后单数量
     */
    int batchPushSalesReturnToJindie(List<OrderAfterSaleResp> afterSaleOrders);
}
