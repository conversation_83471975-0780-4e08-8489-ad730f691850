package com.cosfo.manage.jindie.service.impl;

import cn.hutool.core.util.StrUtil;
import com.cosfo.manage.jindie.service.JindieMaterialService;
import com.cosfo.manage.jindie.util.JindieApiClientUtil;
import com.kingdee.service.ApiClient;
import com.kingdee.service.ApiException;
import com.kingdee.service.data.api.MaterialApi;
import com.kingdee.service.data.entity.MaterialDetailReply;
import com.kingdee.service.data.entity.MaterialListReply;
import com.kingdee.service.data.entity.MaterialMaterialListReq;
import com.kingdee.service.data.entity.MaterialSaveRequest;
import com.kingdee.service.data.entity.SaveReply;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.ProviderException;

/**
 * 金蝶物料服务实现
 */
@Service
@Slf4j
public class JindieMaterialServiceImpl implements JindieMaterialService {

    @Resource
    private JindieApiClientUtil jindieApiClientUtil;

    @Override
    public MaterialDetailReply getMaterialDetail(String id, String number) {
        try {
            // 参数校验，至少需要一个参数不为空
            if (StrUtil.isEmpty(id) && StrUtil.isEmpty(number)) {
                throw new IllegalArgumentException("物料ID和物料编码不能同时为空");
            }

            // 获取配置好的API客户端
            ApiClient apiClient = jindieApiClientUtil.getApiClient();
            MaterialApi materialApi = new MaterialApi(apiClient);

            // 调用API获取物料详情
            MaterialDetailReply detailReply = materialApi.materialMaterialDetail(id, number);

            if (id != null) {
                log.info("通过ID获取物料详情成功, id: {}", id);
            } else {
                log.info("通过编码获取物料详情成功, number: {}", number);
            }

            return detailReply;

        } catch (ApiException e) {
            String paramInfo = id != null ? "id: " + id : "number: " + number;
            log.error("获取物料详情异常, {}, 错误码: {}, 错误信息: {}",
                    paramInfo, e.getCode(), e.getMessage(), e);
            throw new ProviderException("获取物料详情异常: " + e.getMessage(), e);
        } catch (Exception e) {
            String paramInfo = id != null ? "id: " + id : "number: " + number;
            log.error("获取物料详情异常, {}", paramInfo, e);
            throw new ProviderException("获取物料详情异常: " + e.getMessage(), e);
        }
    }

    @Override
    public MaterialListReply getMaterialList(MaterialMaterialListReq request) {
        try {
            // 获取配置好的API客户端
            ApiClient apiClient = jindieApiClientUtil.getApiClient();
            MaterialApi materialApi = new MaterialApi(apiClient);

            // 调用API获取物料列表
            return materialApi.materialMaterialList(request);

        } catch (ApiException e) {
            log.error("获取物料列表异常, 错误码: {}, 错误信息: {}",
                    e.getCode(), e.getMessage(), e);
            throw new ProviderException("获取物料列表异常: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取物料列表异常", e);
            throw new ProviderException("获取物料列表异常: " + e.getMessage(), e);
        }
    }
}
