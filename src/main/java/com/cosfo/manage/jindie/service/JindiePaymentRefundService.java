package com.cosfo.manage.jindie.service;

import com.cosfo.manage.order.model.po.payment.Refund;
import com.kingdee.service.data.entity.SaveReply;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 金蝶收款退款单服务接口
 */
public interface JindiePaymentRefundService {

    /**
     * 推送退款单到金蝶
     *
     * @param refund 退款单
     * @return 推送结果
     */
    SaveReply pushPaymentRefundToJindie(Refund refund);

    /**
     * 批量推送退款单到金蝶
     *
     * @param refunds 退款单列表
     * @return 推送成功的退款单数量
     */
    int batchPushPaymentRefundToJindie(List<Refund> refunds);

}
