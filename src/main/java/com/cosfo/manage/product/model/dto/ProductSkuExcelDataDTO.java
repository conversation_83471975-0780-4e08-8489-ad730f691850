package com.cosfo.manage.product.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cosfo.manage.common.easy.excel.model.RowIndex;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/26 23:01
 */
@Data
public class ProductSkuExcelDataDTO extends RowIndex {

    /**
     * 序号
     */
    @ExcelProperty(index = 0)
    private String serialNumber;

    /**
     * spu编号
     */
    @ExcelProperty(index = 1)
    private String spuId;

    /**
     * 主标题
     */
    @ExcelProperty(index = 2)
    private String title;

    /**
     * 副标题
     */
    @ExcelProperty(index = 3)
    private String subTitle;

    /**
     * 一级类目
     */
    @ExcelProperty(index = 4)
    private String firstCategoryName;

    /**
     * 二级类目
     */
    @ExcelProperty(index = 5)
    private String secondCategoryName;

    /**
     * 三级类目
     */
    @ExcelProperty(index = 6)
    private String thirdCategoryName;

    /**
     * 品牌名
     */
    @ExcelProperty(index = 7)
    private String brandName;

    /**
     * 存储区域描述
     */
    @ExcelProperty(index = 8)
    private String storageLocationDesc;

    /**
     * 存储温度
     */
    @ExcelProperty(index = 9)
    private String storageTemperature;

    /**
     * 产地
     */
    @ExcelProperty(index = 10)
    private String origin;

    /**
     * 保质期
     */
    @ExcelProperty(index = 11)
    private String guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    @ExcelProperty(index = 12)
    private String guaranteeUnitDesc;

    /**
     * 规格
     */
    @ExcelProperty(index = 13)
    private String specification;

    /**
     * 规格单位
     */
    @ExcelProperty(index = 14)
    private String specificationUnit;

    /**
     * 分类
     */
    @ExcelProperty(index = 15)
    private String classification;

    /**
     * 上下架
     */
    @ExcelProperty(index = 16)
    private String onSale;

    /**
     * 经营方式
     */
    @ExcelProperty(index = 17)
    private String operationMode;

    /**
     * 库存
     */
    @ExcelProperty(index = 18)
    private String stock;

    /**
     * 价格
     */
    @ExcelProperty(index = 19)
    private String price;

    /**
     * 错误信息
     */
    @ExcelProperty(index = 20)
    private String errorMessage;

    /**
     * skuId
     */
    @ExcelIgnore
    private Long id;

    /**
     * 类目Id
     */
    @ExcelIgnore
    private Long categoryId;

    /**
     * 品牌id
     */
    @ExcelIgnore
    private Long brandId;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    @ExcelIgnore
    private Integer storageLocation;

    /**
     * 0 天 1 月 2 年
     */
    @ExcelIgnore
    private Integer guaranteeUnit;

    /**
     * 分类id
     */
    @ExcelIgnore
    private Long classificationId;

    /**
     * uId
     */
    @ExcelIgnore
    private String uId;

    /**
     * 一级分类
     */
    @ExcelIgnore
    private String firstClassificationName;

    /**
     * 二级分类
     */
    @ExcelIgnore
    private String secondClassificationName;
}
