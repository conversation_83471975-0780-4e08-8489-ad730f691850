package com.cosfo.manage.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * sku 单位转换表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Getter
@Setter
@TableName("product_sku_unit_multiple")
public class ProductSkuUnitMultiple implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * skuId
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * skucode
     */
    @TableField("sku_code")
    private String skuCode;

    /**
     * 租户Id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 规格单位
     */
    @TableField("specification_unit")
    private String specificationUnit;

    /**
     * 转换单位
     */
    @TableField("target_specification_unit")
    private String targetSpecificationUnit;

    /**
     * 转换倍数
     */
    @TableField("target_multipl")
    private Integer targetMultipl;


}
