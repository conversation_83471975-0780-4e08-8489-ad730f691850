package com.cosfo.manage.product.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/10 9:18
 */
@Data
public class ProductSalesOverviewQueryDTO {

    /**
     * 时间标签
     */
    private String timeTag;

    /**
     * 时间标签集合
     */
    private List<String> timeTags;

    /**
     * 1、日 2、周 3、月
     */
    private Integer type;

    /**
     * 类目ids
     */
    private List<Long> categoryIds;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private List<Integer> storeTypes;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 城市
     */
    private List<String> addressList;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 商品类型0自营1经销
     */
    @Deprecated
    private Integer warehouseType;

    /**
     * 配送方式0三方配送1品牌方配送
     */
    @Deprecated
    private Integer deliveryType;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品项
     */
    private Long itemId;

    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     * @see com.cosfo.manage.common.context.GoodsTypeEnum
     */
    private Integer goodsType;

}
