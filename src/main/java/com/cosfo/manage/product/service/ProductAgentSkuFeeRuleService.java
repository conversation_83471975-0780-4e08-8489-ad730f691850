package com.cosfo.manage.product.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.product.model.dto.ProductAgentSkuFeeDTO;
import com.cosfo.manage.product.model.dto.ProductAgentSkuFeeRuleDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuFeeRule;
import com.cosfo.manage.product.model.vo.ProductAgentSkuFeeRuleVO;
import net.xianmu.common.result.CommonResult;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public interface ProductAgentSkuFeeRuleService {

    /**
     * 查询
     * @param tenantId
     * @return
     */
    CommonResult<ProductAgentSkuFeeRuleVO> queryRule(Long tenantId);

    /**
     * 根据租户查询
     * @param tenantId
     * @return
     */
    ProductAgentSkuFeeRule queryByTenantId(Long tenantId);

    /**
     * 更新是否开启自动加价标识
     * @param productAgentSkuFeeRuleDto
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult updateAutomaticIncreasePriceFlag(ProductAgentSkuFeeRuleDTO productAgentSkuFeeRuleDto, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 计算加价后的金额
     * @param tenantId 租户
     * @param basePrice 商品基础价
     * @return
     */
    ProductAgentSkuFeeDTO buildAgentSkuFeeRuleList(Long tenantId, BigDecimal basePrice);
}
