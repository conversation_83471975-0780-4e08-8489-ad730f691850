package com.cosfo.manage.product.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;

import java.util.Collection;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/16
 */
public interface ProductAgentSkuMappingDao extends IService<ProductAgentSkuMapping> {

    /**
     * 根据货品Id查询映射关系
     *
     * @param skuIds
     * @param tenantId
     * @return
     */
    List<ProductAgentSkuMapping> selectBySkuIds(Collection<Long> skuIds, Long tenantId);

    List<ProductAgentSkuMapping> selectByAgentSkuId(Long supplierSkuId);
    List<ProductAgentSkuMapping> selectByAgentSkuIds(List<Long> supplierSkuId, Long tenantId);

    /**
     * 根据sku删除
     * @param skuId
     */
    void deleteBySkuId(Long skuId);

    void deleteById(Long id);

    /**
     * 根据租户查询所有代仓品
     *
     * @param tenantId
     * @return
     */
    List<ProductAgentSkuMapping> selectByTenantId(Long tenantId);
}
