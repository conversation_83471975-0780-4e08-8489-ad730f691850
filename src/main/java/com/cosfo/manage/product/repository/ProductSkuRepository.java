package com.cosfo.manage.product.repository;

import com.cosfo.manage.good.model.dto.ProductSkuQueryInput;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.product.model.po.ProductSku;

import java.util.List;

/**
 * <p>
 * sku 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
public interface ProductSkuRepository extends IService<ProductSku> {

    List<ProductSku> listBySpuIds(List<Long> spuIds,Long tenantId);

    List<ProductSku> listBySpuIds(List<Long> spuIds, Long tenantId, Integer associated,Integer useFlag);

    ProductSku getByIdAndTenantId(Long skuId, Long tenantId);

    List<ProductSku> listByCondition(ProductSkuQueryInput skuQuery, Long tenantId);

    void saveBatch(List<ProductSku> skuList);

}
