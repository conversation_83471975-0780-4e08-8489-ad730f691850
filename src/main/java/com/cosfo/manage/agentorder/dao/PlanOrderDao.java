package com.cosfo.manage.agentorder.dao;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.common.util.DateUtil;
import com.cosfo.manage.agentorder.mapper.PlanOrderMapper;
import com.cosfo.manage.agentorder.model.dto.PlanOrderCancelInfoDto;
import com.cosfo.manage.agentorder.model.dto.PlanOrderCreateOrderFailDto;
import com.cosfo.manage.agentorder.model.dto.PlanOrderCreateOrderSuccessDto;
import com.cosfo.manage.agentorder.model.param.PlanOrderQueryParam;
import com.cosfo.manage.agentorder.model.po.PlanOrder;
import com.cosfo.manage.common.context.AgentOrderEnum;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 计划订单表(PlanOrder)表Dao查询服务
 *
 * <AUTHOR>
 * @since 2024-02-06 11:47:27
 */
@Service
@Slf4j
public class PlanOrderDao extends ServiceImpl<PlanOrderMapper, PlanOrder> {


    /**
     * 分页查询计划单列表
     *
     * @param queryParam
     * @return
     */
    public Page<PlanOrder> queryPage(PlanOrderQueryParam queryParam) {
        return getBaseMapper().queryPage(new Page<>(queryParam.getPageIndex(), queryParam.getPageSize()), queryParam);
    }

    /**
     * 查询租户下，所有可以提醒的计划单
     * 1.计划单状态=待确认
     * 2.下单方式=生成计划单
     * 3.上一次通知时间is null  or 前一天（0点刷新）
     *
     * @param tenantId
     * @return
     */
    public List<PlanOrder> listNotifyPlanOrders(Long tenantId) {
        LocalDateTime now = LocalDateTime.now();
        now = DateUtil.startOfDay(now);
        return getBaseMapper().listNotifyPlanOrders(tenantId, AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue(), AgentOrderEnum.CREATE_PLAN_ORDER_LIST, now);
    }

    /**
     * 查询所有待生成订单的计划单
     *
     * @return
     */
    public List<PlanOrder> listWaitCreateOrders(List<String> planOrderNos) {
        return getBaseMapper().listWaitCreateOrders(AgentOrderEnum.Status.SYSTEM_CREATING_ORDER.getValue(), AgentOrderEnum.PlanTypeEnum.CREATE_ORDER.name(), planOrderNos);
    }

    /**
     * 根据计划单编号查询信息
     *
     * @param planOrderNo
     * @return
     */
    public PlanOrder getByPlanOrderNo(String planOrderNo) {
        if (Objects.isNull(planOrderNo)) {
            throw new BizException("查询计划单详情，计划单编号不可为空");
        }
        LambdaQueryWrapper<PlanOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanOrder::getPlanOrderNo, planOrderNo);
        return getOne(queryWrapper);
    }

    /**
     * 根据计划单编号查询信息
     *
     * @param planOrderNos
     * @return
     */
    public List<PlanOrder> queryByPlanOrderNos(List<String> planOrderNos) {
        if (CollectionUtils.isEmpty(planOrderNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PlanOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PlanOrder::getPlanOrderNo, planOrderNos);
        return list(queryWrapper);
    }


    /**
     * 根据计划单编号查询待取消的计划单（需要自动取消的计划单）
     *
     * @param agentOrderNo
     * @return
     */
    public List<PlanOrder> listCancelPlanOrderByAgentOrder(String agentOrderNo) {
        if (Objects.isNull(agentOrderNo)) {
            throw new BizException("查询计划单详情，代下单编号不可为空");
        }
        LambdaQueryWrapper<PlanOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanOrder::getAgentOrderNo, agentOrderNo);
        // 铺货单不需要自动取消
        queryWrapper.eq(PlanOrder::getPlanType, AgentOrderEnum.PlanTypeEnum.CREATE_PLAN_ORDER.name());
        queryWrapper.eq(PlanOrder::getStatus, AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue());
        return list(queryWrapper);
    }

    /**
     * 更新计划单的最近提醒时间
     *
     * @param planOrderId
     */
    public void updateNotifyTime(Long planOrderId,Long authUserId) {
        PlanOrder planOrder = getById(planOrderId);
        if (Objects.isNull(planOrder)) {
            throw new BizException("未找到计划单！");
        }
        planOrder.setPlanConfirmNotifyTime(LocalDateTime.now());
        planOrder.setUpdateTime(LocalDateTime.now());
        planOrder.setUpdateUserId(authUserId);
        updateById(planOrder);
    }

    /**
     * 批量更新计划单的最近提醒时间
     *
     * @param tenantId
     * @param planOrderIds
     * @return
     */
    public boolean batchUpdateNotifyTime(Long tenantId, List<Long> planOrderIds) {
        LambdaUpdateWrapper<PlanOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PlanOrder::getTenantId, tenantId);
        updateWrapper.in(PlanOrder::getId, planOrderIds);
        updateWrapper.set(PlanOrder::getPlanConfirmNotifyTime, LocalDateTime.now());
        updateWrapper.set(PlanOrder::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(PlanOrder::getUpdateUserId, UserLoginContextUtils.getAuthUserId());
        return update(updateWrapper);
    }

    /**
     * 更新取消状态
     *
     * @param cancelInfoDto
     * @return
     */
    public boolean updateCancel(PlanOrderCancelInfoDto cancelInfoDto) {
        LambdaUpdateWrapper<PlanOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PlanOrder::getStatus, AgentOrderEnum.Status.PLAN_ORDER_CANCEL.getValue());
        updateWrapper.set(PlanOrder::getCancelRemark, cancelInfoDto.getCancelRemark());
        updateWrapper.set(PlanOrder::getCancelTime, LocalDateTime.now());
        updateWrapper.set(PlanOrder::getCancelUserId, cancelInfoDto.getOperatorId());
        updateWrapper.set(PlanOrder::getCancelType, cancelInfoDto.getOperatorSource().name());
        updateWrapper.set(PlanOrder::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(PlanOrder::getUpdateUserId, cancelInfoDto.getOperatorId());
        updateWrapper.set(PlanOrder::getItemInfoSnapshot, cancelInfoDto.getItemSnapshot());

        updateWrapper.eq(PlanOrder::getId, cancelInfoDto.getPlanOrderId());
        updateWrapper.eq(PlanOrder::getStatus, AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue());
        updateWrapper.in(PlanOrder::getPlanType, AgentOrderEnum.CREATE_PLAN_ORDER_LIST);

        return update(updateWrapper);
    }

    /**
     * 根据状态统计计划单数量
     *
     * @param tenantId
     * @param storeIds
     * @param statusList
     * @return
     */
    public long countByParam(Long tenantId, List<Long> storeIds, List<Integer> statusList) {
        LambdaQueryWrapper<PlanOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(tenantId != null, PlanOrder::getTenantId, tenantId);
        queryWrapper.in(!CollectionUtils.isEmpty(storeIds), PlanOrder::getStoreId, storeIds);
        queryWrapper.in(!CollectionUtils.isEmpty(statusList), PlanOrder::getStatus, statusList);
        return count(queryWrapper);
    }


    /**
     * 更新创建订单失败
     *
     * @param createOrderFailDto
     * @return
     */
    public boolean updateCreateOrderFail(PlanOrderCreateOrderFailDto createOrderFailDto) {
        LambdaUpdateWrapper<PlanOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PlanOrder::getStatus, AgentOrderEnum.Status.CREATE_ORDER_FAIL.getValue());
        updateWrapper.set(PlanOrder::getFailReason, createOrderFailDto.getFailReason());
        updateWrapper.set(PlanOrder::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(PlanOrder::getFinishTime, createOrderFailDto.getOrderTime());
        updateWrapper.set(PlanOrder::getItemInfoSnapshot, createOrderFailDto.getItemSnapshot());

        updateWrapper.eq(PlanOrder::getId, createOrderFailDto.getPlanOrderId());
        if (AgentOrderEnum.PlanTypeEnum.isPlanTypeCreatePlanOrder(createOrderFailDto.getPlanTypeEnum())) {
            updateWrapper.eq(PlanOrder::getStatus, AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue());
        } else {
            updateWrapper.eq(PlanOrder::getStatus, AgentOrderEnum.Status.SYSTEM_CREATING_ORDER.getValue());
        }

        return update(updateWrapper);
    }

    /**
     * 更新创建订单成功
     *
     * @param createOrderSuccessDto
     * @return
     */
    public boolean updateCreateOrderSuccess(PlanOrderCreateOrderSuccessDto createOrderSuccessDto) {
        LambdaUpdateWrapper<PlanOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PlanOrder::getStatus, AgentOrderEnum.Status.CREATE_ORDER_SUCCESS.getValue());
        updateWrapper.set(PlanOrder::getFailReason, createOrderSuccessDto.getFailReason());
        updateWrapper.set(PlanOrder::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(PlanOrder::getFinishTime, LocalDateTime.now());
        updateWrapper.set(PlanOrder::getItemInfoSnapshot, createOrderSuccessDto.getItemSnapshot());
        updateWrapper.set(PlanOrder::getSuccessCreateOrderNo, JSON.toJSONString(createOrderSuccessDto.getOrderNoList()));

        updateWrapper.eq(PlanOrder::getId, createOrderSuccessDto.getPlanOrderId());
        if (AgentOrderEnum.PlanTypeEnum.isPlanTypeCreatePlanOrder(createOrderSuccessDto.getPlanTypeEnum())) {
            updateWrapper.eq(PlanOrder::getStatus, AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue());
        } else {
            updateWrapper.eq(PlanOrder::getStatus, AgentOrderEnum.Status.SYSTEM_CREATING_ORDER.getValue());
        }

        return update(updateWrapper);
    }
}

