package com.cosfo.manage.agentorder.model.po;

import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 代门店下单表(AgentOrder)实体类
 *
 * <AUTHOR>
 * @since 2024-02-06 15:33:49
 */
@Data
@TableName("agent_order")
public class AgentOrder implements Serializable {
    private static final long serialVersionUID = -66038583353670984L;
    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 代下单编号
     */
    private String agentOrderNo;
    /**
     * 代下单选择门店
     */
    private String storeInfo;
    /**
     * 推荐理由
     */
    private String recommendReason;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    private Long creatorUserId;
    /**
     * 更新人
     */
    private Long updateUserId;

}

