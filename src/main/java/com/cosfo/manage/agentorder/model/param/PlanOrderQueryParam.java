package com.cosfo.manage.agentorder.model.param;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/2/22 下午5:18
 */
@Data
public class PlanOrderQueryParam {

    private Integer pageIndex;
    private Integer pageSize;
    /**
     * 自定义排序
     */
    private String orderBy;


    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Collection<Long> storeIds;

    /**
     * 计划单状态 100-待门店确认; 105-下单中; 200-下单成功; 300-下单失败; 400-已取消
     */
    private Integer planOrderStatus;

    private List<Integer> planOrderStatusList;

    /**
     * 代下单编号
     */
    private String agentOrderNo;

    /**
     * 计划单编号
     */
    private String planOrderNo;

    /**
     * 商品编码
     */
    private List<Long> itemIds;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 创建人
     */
    private List<Long> creatorUserIds;

    /**
     * 计划单创建开始时间
     */
    private LocalDateTime planCreateStartTime;

    /**
     * 计划单创建结束时间
     */
    private LocalDateTime planCreateEndTime;

    /**
     * 订单创建开始时间
     */
    private LocalDateTime orderCreateStartTime;

    /**
     * 订单创建结束时间
     */
    private LocalDateTime orderCreateEndTime;

    /**
     * 计划单取消开始时间
     */
    private LocalDateTime planCancelStartTime;

    /**
     * 计划单取消结束时间
     */
    private LocalDateTime planCancelEndTime;

    /**
     * 计划下单方式 create_plan_order-生成计划单(配货单) create_order-创建订单 create_force_plan_order-计划单强制下单(铺货单)
     */
    private String planType;

}
