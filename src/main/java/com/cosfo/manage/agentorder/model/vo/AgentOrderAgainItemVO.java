package com.cosfo.manage.agentorder.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2024/2/5 16:24
 * @Description:
 */
@Data
public class AgentOrderAgainItemVO implements Serializable {
    private static final long serialVersionUID = -7872899475554207625L;

    /**
     * 商品编码
     */
    private Long itemId;

    /**
     * 商品标题
     */
    private String spuTitle;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 商品图片
     */
    private String mainPicture;

    /**
     * 商品分类
     */
    private String marketItemClassificationStr;

    /**
     * 一级分类Id
     */
    private Long firstClassificationId;

    /**
     * 一级分类名称
     */
    private String firstClassificationName;

    /**
     * 二级分类Id
     */
    private Long secondClassificationId;

    /**
     * 二级分类
     */
    private String secondClassificationName;

    /**
     * 后台类目
     */
    private String categoryStr;

    /**
     * 类目Id
     */
    private Long firstCategoryId;

    /**
     * 一级类目
     */
    private String firstCategory;

    /**
     * 二级类目Id
     */
    private Long secondCategoryId;

    /**
     * 二级类目
     */
    private String secondCategory;

    /**
     * 三级类目Id
     */
    private Long thirdCategoryId;

    /**
     * 三级类目
     */
    private String thirdCategory;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 价格区间
     */
    private String priceRange;

    /**
     * 最大价格
     */
    private BigDecimal minPrice;

    /**
     * 最小价格
     */
    private BigDecimal maxPrice;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 数量
     */
    private Integer itemAmount;


    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;

    /**
     * 限购数量
     */
    private Integer saleLimitQuantity;

    /**
     * 限购规则 0不限制,1每次,2每自然日，3每自然周，4每自然月
     */
    private Integer saleLimitRule;
}
