package com.cosfo.manage.agentorder.model.po;

import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 代门店下单表(AgentOrderItem)实体类
 *
 * <AUTHOR>
 * @since 2024-02-06 15:33:52
 */
@Data
@TableName("agent_order_item")
public class AgentOrderItem implements Serializable {
    private static final long serialVersionUID = 170798566844404735L;
    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 代下单编号
     */
    private String agentOrderNo;
    /**
     * 代下单选择商品id
     */
    private Long itemId;
    /**
     * 代下单选择商品数量
     */
    private Integer itemQuantity;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    private Long creatorUserId;
    /**
     * 更新人
     */
    private Long updateUserId;

}

