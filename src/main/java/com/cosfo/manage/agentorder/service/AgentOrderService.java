package com.cosfo.manage.agentorder.service;

import com.cosfo.manage.agentorder.model.input.command.AgentOrderAddInput;
import com.cosfo.manage.agentorder.model.vo.AgentOrderAgainDetailVO;
import com.cosfo.manage.agentorder.model.vo.AgentOrderCheckVO;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/6 11:53
 * @Description:
 */
public interface AgentOrderService {

    // 代下单列表

    /**
     * 代门店下单
     * 新增代下单+计划单
     *
     * @param agentOrderAddInput
     * @param tenantId
     * @return 代下单编号
     */
    String addPlanOrder(AgentOrderAddInput agentOrderAddInput, Long tenantId);

    /**
     * 代下单提交前校验商品
     * @param agentOrderAddInput
     * @param tenantId
     * @return
     */
    List<AgentOrderCheckVO> checkPlanOrder(AgentOrderAddInput agentOrderAddInput, Long tenantId);

    /**
     * 快速下单 根据代下单编号查询详情
     *
     * @param agentOrderNo
     * @return
     */
    AgentOrderAgainDetailVO agentOrderAgain(String agentOrderNo);

    /**
     * 快速下单- 校验单号是否正常
     *
     * @param agentOrderNo
     * @return
     */
    boolean checkAgentOrderAgain(String agentOrderNo);


    // 取消订单

    // 计划单详情
}
