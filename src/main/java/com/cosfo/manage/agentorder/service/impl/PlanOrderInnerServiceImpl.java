package com.cosfo.manage.agentorder.service.impl;

import com.cosfo.manage.agentorder.dao.PlanOrderDao;
import com.cosfo.manage.agentorder.service.PlanOrderInnerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/20 16:10
 * @Description:
 */
@Service
@Slf4j
public class PlanOrderInnerServiceImpl implements PlanOrderInnerService {
    @Resource
    private PlanOrderDao planOrderDao;


    /**
     * 批量更新计划单提醒时间
     * @param tenantId
     * @param planOrderIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateNotifyTime(Long tenantId, List<Long> planOrderIds) {
        return planOrderDao.batchUpdateNotifyTime(tenantId, planOrderIds);
    }
}
