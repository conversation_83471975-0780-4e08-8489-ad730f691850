package com.cosfo.manage.agentorder.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.agentorder.dao.AgentOrderDao;
import com.cosfo.manage.agentorder.dao.AgentOrderItemDao;
import com.cosfo.manage.agentorder.dao.PlanOrderDao;
import com.cosfo.manage.agentorder.model.dto.AddPlanOrderReturnDto;
import com.cosfo.manage.agentorder.model.dto.AgentOrderStoreInfoDto;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderAddInput;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderItemInput;
import com.cosfo.manage.agentorder.model.input.command.AgentOrderStoreInput;
import com.cosfo.manage.agentorder.model.po.AgentOrder;
import com.cosfo.manage.agentorder.model.po.AgentOrderItem;
import com.cosfo.manage.agentorder.model.po.PlanOrder;
import com.cosfo.manage.agentorder.service.AgentOrderInnerService;
import com.cosfo.manage.common.constant.AgentOrderConstant;
import com.cosfo.manage.common.constant.MqTagConstant;
import com.cosfo.manage.common.constant.MqTopicConstant;
import com.cosfo.manage.common.context.AgentOrderEnum;
import com.cosfo.manage.common.util.Global;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2024/2/6 15:15
 * @Description:
 */
@Service
public class AgentOrderInnerServiceImpl implements AgentOrderInnerService {
    @Resource
    private AgentOrderDao agentOrderDao;
    @Resource
    private AgentOrderItemDao agentOrderItemDao;
    @Resource
    private PlanOrderDao planOrderDao;
    @Resource
    private MqProducer mqProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AddPlanOrderReturnDto saveAgentPlanOrder(AgentOrderAddInput agentOrderAddInput) {
        // 保存代下单
        String agentOrderNo = saveAgentOrder(agentOrderAddInput);
        // 保存代下单商品
        saveAgentOrderItem(agentOrderAddInput, agentOrderNo);
        // 保存计划单
        List<Long> planOrderIds = savePlanOrder(agentOrderAddInput, agentOrderNo);

        return AddPlanOrderReturnDto.builder()
            .agentOrderNo(agentOrderNo)
            .planOrderIds(planOrderIds)
            .build();
    }

    /**
     * 保存代下单
     *
     * @param agentOrderAddInput
     * @return
     */
    private String saveAgentOrder(AgentOrderAddInput agentOrderAddInput) {
        // 代下单编号
        String agentOrderNo = Global.generateTimeCode(AgentOrderConstant.AGENT_ORDER_PREFIX, AgentOrderConstant.AGENT_ORDER_DIGITS);
        AgentOrder agentOrder = new AgentOrder();
        agentOrder.setTenantId(UserLoginContextUtils.getTenantId());
        agentOrder.setAgentOrderNo(agentOrderNo);
        List<AgentOrderStoreInfoDto> storeInfoDtoList = agentOrderAddInput.getStoreInputs().stream()
            .map(s -> AgentOrderStoreInfoDto.builder()
                .storeId(s.getStoreId())
                .planType(s.getPlanType())
                .build()).collect(Collectors.toList());
        agentOrder.setStoreInfo(JSONObject.toJSONString(storeInfoDtoList));
        agentOrder.setRecommendReason(agentOrderAddInput.getRecommendReason());
        agentOrder.setCreatorUserId(UserLoginContextUtils.getAuthUserId());
        agentOrder.setUpdateUserId(UserLoginContextUtils.getAuthUserId());
        agentOrder.setCreateTime(LocalDateTime.now());
        agentOrder.setUpdateTime(LocalDateTime.now());
        agentOrderDao.save(agentOrder);
        return agentOrderNo;
    }

    /**
     * 保存代下单商品
     *
     * @param agentOrderAddInput
     * @param agentOrderNo
     */
    private void saveAgentOrderItem(AgentOrderAddInput agentOrderAddInput, String agentOrderNo) {
        List<AgentOrderItem> agentOrderItems = new ArrayList<>();
        for (AgentOrderItemInput itemInput : agentOrderAddInput.getItemInputs()) {
            AgentOrderItem agentOrderItem = new AgentOrderItem();
            agentOrderItem.setTenantId(UserLoginContextUtils.getTenantId());
            agentOrderItem.setAgentOrderNo(agentOrderNo);
            agentOrderItem.setItemId(itemInput.getItemId());
            agentOrderItem.setItemQuantity(itemInput.getItemAmount());
            agentOrderItem.setCreatorUserId(UserLoginContextUtils.getAuthUserId());
            agentOrderItem.setUpdateUserId(UserLoginContextUtils.getAuthUserId());
            agentOrderItem.setCreateTime(LocalDateTime.now());
            agentOrderItem.setUpdateTime(LocalDateTime.now());
            agentOrderItems.add(agentOrderItem);
        }
        agentOrderItemDao.saveBatch(agentOrderItems);
    }

    /**
     * 保存计划单
     *
     * @param input
     * @param agentOrderNo
     */
    private List<Long> savePlanOrder(AgentOrderAddInput input, String agentOrderNo) {
        boolean sendMsg = false;
        List<PlanOrder> planOrderList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        // 生成一批随机数
        List<String> planOrderNos = Global.generateBatchTimeCode(AgentOrderConstant.PLAN_ORDER_PREFIX, AgentOrderConstant.AGENT_ORDER_DIGITS, input.getStoreInputs().size());
        if (CollectionUtils.isEmpty(planOrderNos) || planOrderNos.size() != input.getStoreInputs().size()) {
            throw new BizException("计划单编号生成失败！");
        }

        int i = 0;
        for (AgentOrderStoreInput storeInput : input.getStoreInputs()) {
            PlanOrder planOrder = new PlanOrder();
            planOrder.setTenantId(UserLoginContextUtils.getTenantId());
            planOrder.setStoreId(storeInput.getStoreId());
            planOrder.setAgentOrderNo(agentOrderNo);
            planOrder.setPlanOrderNo(planOrderNos.get(i++));
            planOrder.setPlanType(storeInput.getPlanType());
            if (AgentOrderEnum.PlanTypeEnum.isPlanTypeCreatePlanOrder(storeInput.getPlanType())) {
                planOrder.setStatus(AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue());
                sendMsg = true;
            } else if (AgentOrderEnum.PlanTypeEnum.CREATE_ORDER.name().equals(storeInput.getPlanType())) {
                planOrder.setStatus(AgentOrderEnum.Status.SYSTEM_CREATING_ORDER.getValue());
            }
            planOrder.setAutoCancelTimeout(AgentOrderConstant.AUTO_CANCEL_MINUTE);
            planOrder.setAutoCancelTime(now.plusMinutes(AgentOrderConstant.AUTO_CANCEL_MINUTE));
            planOrder.setRecommendReason(input.getRecommendReason());
            planOrder.setCreatorUserId(UserLoginContextUtils.getAuthUserId());
            planOrder.setUpdateUserId(UserLoginContextUtils.getAuthUserId());
            planOrder.setCreateTime(LocalDateTime.now());
            planOrder.setUpdateTime(LocalDateTime.now());
            planOrderList.add(planOrder);
        }
        planOrderDao.saveBatch(planOrderList);

        if (sendMsg) {
            SummerfarmMsgModel msgModel = new SummerfarmMsgModel();
            msgModel.setMsgType(MqTagConstant.AGENT_ORDER_CANCEL);
            msgModel.setMsgData(agentOrderNo);
            mqProducer.sendDelay(MqTopicConstant.SAAS_MANAGE_DELAY, MqTagConstant.AGENT_ORDER_CANCEL, msgModel, AgentOrderConstant.AUTO_CANCEL_MINUTE * 60L * 1000L);
        }

        // 只有创建计划单，才需要返回，发送短信
        return planOrderList.stream()
            .filter(item -> AgentOrderEnum.PlanTypeEnum.isPlanTypeCreatePlanOrder(item.getPlanType()))
            .map(PlanOrder::getId)
            .collect(Collectors.toList());
    }
}
