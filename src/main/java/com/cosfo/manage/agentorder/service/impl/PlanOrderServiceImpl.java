package com.cosfo.manage.agentorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.resp.MarketItemClassificationResp;
import com.cosfo.mall.client.planorder.req.PlanOrderCreateOrderReq;
import com.cosfo.mall.client.stock.req.StockQueryReq;
import com.cosfo.manage.agentorder.convert.AgentOrderConvert;
import com.cosfo.manage.agentorder.dao.AgentOrderItemDao;
import com.cosfo.manage.agentorder.dao.PlanOrderDao;
import com.cosfo.manage.agentorder.model.dto.*;
import com.cosfo.manage.agentorder.model.input.query.PlanOrderListQueryInput;
import com.cosfo.manage.agentorder.model.input.query.PlanOrderQueryInput;
import com.cosfo.manage.agentorder.model.param.PlanOrderQueryParam;
import com.cosfo.manage.agentorder.model.po.AgentOrderItem;
import com.cosfo.manage.agentorder.model.po.PlanOrder;
import com.cosfo.manage.agentorder.model.vo.*;
import com.cosfo.manage.agentorder.service.PlanOrderService;
import com.cosfo.manage.client.planorder.req.CreateOrderFailReq;
import com.cosfo.manage.client.planorder.req.CreateOrderSuccessReq;
import com.cosfo.manage.client.planorder.req.PlanOrderQueryReq;
import com.cosfo.manage.client.planorder.resp.PlanOrderDetailResp;
import com.cosfo.manage.client.planorder.resp.PlanOrderItemResp;
import com.cosfo.manage.client.planorder.resp.PlanOrderResp;
import com.cosfo.manage.common.constant.*;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.context.AgentOrderEnum.PlanTypeEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.executor.ThreadPoolHelper;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.sms.model.Sms;
import com.cosfo.manage.common.sms.model.SmsSenderFactory;
import com.cosfo.manage.common.util.*;
import com.cosfo.manage.facade.MarketClassificationFacade;
import com.cosfo.manage.facade.MarketItemFacade;
import com.cosfo.manage.facade.PriceFacade;
import com.cosfo.manage.facade.category.CategoryServiceFacade;
import com.cosfo.manage.facade.dto.StockInfoDTO;
import com.cosfo.manage.facade.ordercenter.OrderItemQueryFacade;
import com.cosfo.manage.facade.ordercenter.OrderQueryFacade;
import com.cosfo.manage.facade.planorder.PlanOrderFacade;
import com.cosfo.manage.facade.stock.StockFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantAccountFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.market.model.dto.MarketItemInfoDTO;
import com.cosfo.manage.market.model.dto.MarketItemQueryDTO;
import com.cosfo.manage.market.model.vo.PriceDetailVO;
import com.cosfo.manage.marketing.model.dto.ItemSaleLimitConfigDTO;
import com.cosfo.manage.marketing.service.ItemSaleLimitConfigService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.service.MerchantStoreAccountService;
import com.cosfo.manage.order.model.vo.OrderItemVO;
import com.cosfo.manage.product.model.dto.ProductCategoryDTO;
import com.cosfo.manage.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.manage.tenant.model.po.TenantAuthConnection;
import com.cosfo.manage.wechat.api.WxaAPI;
import com.cosfo.manage.wechat.bean.wxa.urllink.*;
import com.cosfo.manage.wechat.mapper.WechatAuthorizerMapper;
import com.cosfo.manage.wechat.model.po.WechatAuthorizer;
import com.cosfo.ordercenter.client.common.OrderSourceEnum;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2024/2/19 11:15
 * @Description:
 */
@Service
@Slf4j
public class PlanOrderServiceImpl implements PlanOrderService {
    @Resource
    private PlanOrderDao planOrderDao;
    @Resource
    private AgentOrderItemDao agentOrderItemDao;
    @Resource
    private WechatAuthorizerMapper wechatAuthorizerMapper;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;

    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private SmsSenderFactory smsSenderFactory;
    @Resource
    private MqProducer mqProducer;

    @Resource
    private UserCenterMerchantStoreFacade storeFacade;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;
    @Resource
    private MarketItemFacade marketItemFacade;
    @Resource
    private MarketClassificationFacade marketClassificationFacade;
    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Resource
    private PriceFacade priceFacade;
    @Resource
    private PlanOrderFacade planOrderFacade;
    @Resource
    private StockFacade stockFacade;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;
    @Resource
    private ItemSaleLimitConfigService itemSaleLimitConfigService;


    /**
     * 再来一单 根据计划单ID查询详情
     *
     * @param planOrderNo
     * @return
     */
    @Override
    public AgentOrderAgainDetailVO planOrderAgain(String planOrderNo) {
        // 查询代下单信息
        PlanOrder planOrder = planOrderDao.getByPlanOrderNo(planOrderNo);
        if (Objects.isNull(planOrder)) {
            throw new BizException("未找到计划单编号信息！");
        }
        Long tenantId = UserLoginContextUtils.getTenantId();
        if (Objects.isNull(tenantId)) {
            throw new BizException("请重新登录!");
        }
        if (!tenantId.equals(planOrder.getTenantId())) {
            log.error("计划单不属于当前租户！当前登录租户:{} 代下单号：{} 代下单租户:{}", tenantId, planOrderNo, planOrder.getTenantId());
            throw new BizException("代下单不属于当前租户！");
        }
        // 查询代下单商品信息
        List<AgentOrderItem> agentOrderItems = agentOrderItemDao.listByAgentOrderNo(planOrder.getAgentOrderNo());
        if (CollectionUtils.isEmpty(agentOrderItems)) {
            log.error("代下单编号异常！未找到商品记录！,代下单编号:{}", planOrder.getAgentOrderNo());
            throw new BizException("代下单编号异常！未找到商品记录！");
        }
        if (agentOrderItems.stream().anyMatch(i -> !tenantId.equals(i.getTenantId()))) {
            log.error("代下单不属于当前租户！当前登录租户:{} 代下单号：{}", tenantId, planOrder.getAgentOrderNo());
            throw new BizException("代下单商品信息不属于当前租户！");
        }
        List<Long> itemIds = agentOrderItems.stream().map(AgentOrderItem::getItemId).collect(Collectors.toList());
        // 查询商品详情
        Map<Long, MarketItemInfoDTO> itemMap = marketItemFacade.queryMarketItemMap(MarketItemQueryDTO.builder()
            .tenantId(tenantId)
            .itemIds(itemIds)
            .deleteFlag(MarketDeleteFlagEnum.NORMAL.getFlag())
            .build());
        // 查询分类信息
        Set<Long> categoryIds = itemMap.values().stream()
            .map(MarketItemInfoDTO::getCategoryId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        Map<Long, ProductCategoryDTO> categoryDTOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            categoryDTOMap = categoryServiceFacade.selectWholeCategoryNewBatch(categoryIds);
        }

        // 查询类目信息
        Set<Long> marketIds = itemMap.values().stream().map(MarketItemInfoDTO::getMarketId).collect(Collectors.toSet());
        Map<Long, MarketItemClassificationResp> classificationRespMap = marketClassificationFacade.queryClassificationByMarketIds(tenantId, new ArrayList<>(marketIds));


        // 查询限售信息
        Map<Long, ItemSaleLimitConfigDTO>  itemSaleLimitConfigMap = itemSaleLimitConfigService.queryItemSaleLimitConfigMap(tenantId, itemIds);


        // 查询门店详情
        List<MerchantStoreResultResp> merchantStoreList = storeFacade.getMerchantStoreList(Collections.singletonList(planOrder.getStoreId()));
        AgentOrderStoreInfoDto storeInfoDto = new AgentOrderStoreInfoDto();
        storeInfoDto.setStoreId(planOrder.getStoreId());
        storeInfoDto.setPlanType(planOrder.getPlanType());

        // 拼接参数
        AgentOrderAgainDetailVO againDetailVO = new AgentOrderAgainDetailVO();
        againDetailVO.setRecommendReason(planOrder.getRecommendReason());
        againDetailVO.setItemVOList(AgentOrderConvert.convert2AgentItemVos(agentOrderItems, itemMap, classificationRespMap, categoryDTOMap, itemSaleLimitConfigMap));
        againDetailVO.setStoreVOList(AgentOrderConvert.convert2AgentStoreVos(Collections.singletonList(storeInfoDto), merchantStoreList));
        return againDetailVO;
    }

    /**
     * 一键提醒
     */
    @Override
    public void notifyAllStore() {
        // 查询所有应提醒的计划单
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        if (Objects.isNull(tenantId)) {
            throw new BizException("请先登录");
        }
        String redisKey = RedisKeyEnum.CM00014.join(tenantId);
        RLock lock = redissonClient.getLock(redisKey);
        // 未获取到锁，退出
        if (!lock.tryLock()) {
            throw new BizException("其他管理员正在执行一键提醒，请稍后再试");
        }
        try {
            List<PlanOrder> planOrders = planOrderDao.listNotifyPlanOrders(tenantId);
            log.info("准备为租户：{},一键提醒计划单，共{}条", tenantId, planOrders.size());
            if (CollectionUtils.isEmpty(planOrders)) {
                log.info("没有需要发送短信提醒的计划单");
                return;
            }

            TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);
            // 查询店长
            Set<Long> storeIds = planOrders.stream().map(PlanOrder::getStoreId).collect(Collectors.toSet());
            Map<Long, MerchantStoreAccountDTO> storeManageMap = merchantStoreAccountService.selectManagerMap(tenantId, storeIds);
            ConcurrentHashMap<Long, MerchantStoreAccountDTO> storeManageThreadMap = new ConcurrentHashMap<>(storeManageMap);

            StringBuffer sb = new StringBuffer();
            Set<Object> failStoreIds = new CopyOnWriteArraySet<>();

            // 获取链接
            Sms senceSms = new Sms();
            String notifyUrl = getNotifyUrl(tenantId, senceSms);
            List<Runnable> tasks = new ArrayList<>();
            for (PlanOrder planOrder : planOrders) {
                tasks.add(() -> {
                    try {
                        log.info("为计划单号：{}发送短信", planOrder.getPlanOrderNo());
                        MerchantStoreAccountDTO storeInfo = storeManageThreadMap.get(planOrder.getStoreId());
                        if (Objects.isNull(storeInfo) || StringUtils.isEmpty(storeInfo.getPhone())) {
                            failStoreIds.add(planOrder.getStoreId());
                            sb.append("计划单号：").append(planOrder.getPlanOrderNo()).append("门店ID：").append(planOrder.getStoreId()).append("未找到店长手机号，无法发送提醒\\n");
                            return;
                        }
                        // 发送短信
                        Sms sendSms = new Sms();
                        sendSms.setPhone(storeInfo.getPhone());
                        sendSms.setSceneId(senceSms.getSceneId());
                        sendSms.setArgs(Arrays.asList(tenant.getTenantName(), notifyUrl));
                        boolean success = smsSenderFactory.getSmsSender().sendSms(sendSms);
                        if (success) {
                            log.info("为计划单号：{}发送短信成功", planOrder.getPlanOrderNo());
                            planOrderDao.updateNotifyTime(planOrder.getId(), authUserId);
                        } else {
                            failStoreIds.add(planOrder.getStoreId());
                            log.error("计划单号：{}，发送短信失败！", planOrder.getPlanOrderNo());
                            sb.append("计划单号：").append(planOrder.getPlanOrderNo()).append("门店ID：").append(planOrder.getStoreId()).append("发送短信失败！\\n");
                        }
                    } catch (Exception e) {
                        failStoreIds.add(planOrder.getStoreId());
                        log.error("为计划单发送短信失败！计划单号：{}", planOrder.getPlanOrderNo(), e);
                        sb.append("计划单号：").append(planOrder.getPlanOrderNo()).append("门店ID：").append(planOrder.getStoreId()).append("发送短信失败！\\n");
                    }
                });
            }

            try {
                ThreadPoolHelper.build(ExecutorFactory.AGENT_ORDER_SMS_EXECUTOR).batchTaskWithTimeout(tasks, 1, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("发送短信线程池失败！", e);
                throw new RuntimeException("发送短信线程池失败!", e);
            }

            if (failStoreIds.size() > 0) {
                log.error("有部分门店提醒短信未发送成功！{}", sb.toString());
                String errMsg = StringUtils.joinWith(",", failStoreIds);
                throw new BizException("门店ID：" + errMsg + " 提醒短信未发送成功！");
            }
        } finally {
            lock.unlock();
        }
    }

    /**
     * 提醒门店
     *
     * @param planOrderId
     * @param loginInfo
     */
    @Override
    public void notifyStore(Long planOrderId, LoginContextInfoDTO loginInfo) {
        Long tenantId = loginInfo.getTenantId();
        if (Objects.isNull(tenantId)) {
            throw new BizException("请重新登录!");
        }

        PlanOrder planOrder = planOrderDao.getById(planOrderId);
        if (Objects.isNull(planOrder)) {
            throw new BizException("未找到计划单！");
        }
        if (!tenantId.equals(planOrder.getTenantId())) {
            log.error("计划单不属于当前租户！当前登录租户:{} 代下单Id：{} 代下单租户:{}", tenantId, planOrderId, planOrder.getTenantId());
            throw new BizException("代下单不属于当前租户！");
        }
        // 是否可以发送短信校验
        if (!checkPlanOrderNotifyStatus(planOrder)) {
            log.error("未通过提醒门店校验！计划单信息：{}", JSONArray.toJSONString(planOrder));
            throw new BizException("当前计划单不可发送短信！");
        }
        // 查询门店-店长手机号
        MerchantStoreAccountDTO merchantStoreAccountDTO = merchantStoreAccountService.selectManager(tenantId, planOrder.getStoreId());
        if (Objects.isNull(merchantStoreAccountDTO)) {
            log.error("未找到店长信息！无法发送短信！租户ID:{} 计划单ID:{}", tenantId, planOrderId);
            throw new BizException("未找到店长信息！无法发送短信！");
        }
        TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);

        // 发送短信
        boolean success = smsSenderFactory.getSmsSender().sendSms(buildSmsInfo(planOrder.getTenantId(), tenant.getTenantName(), merchantStoreAccountDTO.getPhone()));
        if (!success) {
            throw new BizException(ResultDTOEnum.SEND_CODE_FAIL.getMessage());
        }

        // 更新提醒确认的时间
        planOrderDao.updateNotifyTime(planOrderId, loginInfo.getAuthUserId());
    }

    /**
     * 校验当前计划单，是否可以发送短信
     * 可以提醒的条件：
     * 1.计划单状态=待确认
     * 2.下单方式=生成计划单
     * 3.上一次通知时间is null  or 前一天（0点刷新）
     *
     * @param planOrder
     * @return
     */
    private boolean checkPlanOrderNotifyStatus(PlanOrder planOrder) {
        if (!AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue().equals(planOrder.getStatus())) {
            throw new BizException("计划单状态不为待确认，不可发送短信提醒");
        }
        if (!AgentOrderEnum.PlanTypeEnum.isPlanTypeCreatePlanOrder(planOrder.getPlanType())) {
            throw new BizException("计划单类型不是生成计划单，不可发送短信提醒");
        }
        if (Objects.nonNull(planOrder.getPlanConfirmNotifyTime())) {
            LocalDate notifyDate = planOrder.getPlanConfirmNotifyTime().toLocalDate();
            LocalDate today = LocalDate.now();
            if (notifyDate.isBefore(today)) {
                return true;
            } else {
                throw new BizException("计划单已发送过短信，请第二天再尝试");
            }
        }
        return true;
    }

    /**
     * 查询微信小程序的信息是否存在
     *
     * @return
     */
    private WechatAuthorizer getWxToken(Long tenantId) {
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMapper.selectByTenantId(tenantId);
        if (Objects.isNull(tenantAuthConnection)) {
            return null;
        }
        if (!LiteConfigPublicConsts.NORMAL_CONFIG_STATE.getCode().equals(tenantAuthConnection.getStatus())) {
            return null;
        }
        return wechatAuthorizerMapper.selectByAppId(tenantAuthConnection.getAppId());
    }

    /**
     * 构造短信内容
     *
     * @param tenantId
     * @param tenantName
     * @param phone
     * @return
     */
    private Sms buildSmsInfo(Long tenantId, String tenantName, String phone) {
        // 优先发送小程序链接
        Sms sms = new Sms();
        String notifyUrl = getNotifyUrl(tenantId, sms);
        sms.setPhone(phone);
        sms.setArgs(Arrays.asList(tenantName, notifyUrl));
        return sms;
    }

    /**
     * 根据租户ID，获取短信发送的链接
     * 域名固定配置在创蓝平台，不用返回
     * H5: https://mall.cosfo.cn/ index.html#/pages/loading/index?token=h5mall_F2GqGw20CaxcgNwbeJOt2g%3D%3D
     * 小程序： https://wxaurl.cn/ LzAvyiGlJlj
     *
     * @param tenantId
     * @return
     */
    private String getNotifyUrl(Long tenantId, Sms sms) {
        WechatAuthorizer wxToken = getWxToken(tenantId);
        // 不存在小程序，发送h5链接
        if (Objects.isNull(wxToken)) {
            sms.setSceneId(SmsConstants.PLAN_ORDER_NOTIFY_H5);

            String token = AESUtils.encrypt(tenantId.toString());
            try {
                token = URLEncoder.encode(token, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                log.error("生成H5链接token失败！", e);
                throw new RuntimeException("生成H5链接token失败!");
            }
            String urlContent = AgentOrderConstant.SMS_H5_URL_PREFIX + token + AgentOrderConstant.SMS_H5_URL_SUFFIX;
            log.info("租户ID：{}，发送H5短信，短信链接内容：{}", tenantId, urlContent);
            return urlContent;
        }
        // redis中是否存在
        sms.setSceneId(SmsConstants.PLAN_ORDER_NOTIFY_WX);

        String urlContent = null;
        String redisKey = AgentOrderConstant.URL_REDIS_KEY_PREFIX + tenantId;
        Object urlValue = redisUtils.getNoPrefix(redisKey);
        if (Objects.isNull(urlValue)) {
            urlContent = generateUrlLinkRedis(tenantId, wxToken, redisKey);
            log.info("租户ID：{}，生成小程序链接，短信链接内容：{}", tenantId, urlContent);
        } else {
            String urlLink = urlValue.toString();
            if (checkUrlLinkExpire(wxToken.getAccessToken(), urlLink)) {
                urlContent = AgentOrderConstant.getUrlContent(urlLink);
                log.info("租户ID：{}，从redis中获取小程序链接,urlLink:{} 短信链接内容：{}", tenantId, urlLink, urlContent);
            } else {
                urlContent = generateUrlLinkRedis(tenantId, wxToken, redisKey);
                log.info("租户ID：{}，redis内容失效，重新生成小程序链接，短信链接内容：{}", tenantId, urlContent);
            }
        }
        if (Objects.isNull(urlContent)) {
            throw new BizException("未获取到正常的小程序链接！");
        }
        return urlContent;
    }

    /**
     * 生成小程序链接，并放入缓存
     * 返回短信中的小程序链接
     *
     * @param tenantId
     * @param wxToken
     * @param redisKey
     * @return
     */
    private String generateUrlLinkRedis(Long tenantId, WechatAuthorizer wxToken, String redisKey) {
        String urlContent;
        String urlLink = generateUrlLink(wxToken.getAccessToken());
        if (StringUtils.isEmpty(urlLink)) {
            log.error("生成小程序链接失败,租户ID：{}", tenantId);
            throw new BizException("生成小程序链接失败！");
        }
        urlContent = AgentOrderConstant.getUrlContent(urlLink);
        if (Objects.isNull(urlContent)) {
            log.error("生成小程序链接失败,租户ID：{},链接：{}", tenantId, urlLink);
            throw new BizException("生成小程序链接失败！");
        }
        // 存入redis
        redisUtils.setIfAbsent(redisKey, urlLink, AgentOrderConstant.URL_REDIS_TTL_DEFAULT, TimeUnit.DAYS);
        return urlContent;
    }

    /**
     * 生成小程序链接
     *
     * @param accessToken
     * @return
     */
    private String generateUrlLink(String accessToken) {
        GenerateUrlLinkReq generateUrlLinkReq = new GenerateUrlLinkReq();
        generateUrlLinkReq.setPath(AgentOrderConstant.SMS_WXA_URL);
        generateUrlLinkReq.setExpire_type(AgentOrderConstant.EXPIRE_TYPE_DEFAULT);
        generateUrlLinkReq.setExpire_interval(AgentOrderConstant.EXPIRE_INTERVAL_DEFAULT);
        GenerateUrlLinkResult generateUrlLinkResult = WxaAPI.generate_urllink(accessToken, generateUrlLinkReq);
        log.info("生成小程序链接返回：{}", JSONArray.toJSONString(generateUrlLinkResult));
        return generateUrlLinkResult.getUrl_link();
    }

    /**
     * 校验链接是否有效
     *
     * @param accessToken
     * @param urlLink
     * @return
     */
    private Boolean checkUrlLinkExpire(String accessToken, String urlLink) {
        QueryUrlLinkReq req = new QueryUrlLinkReq();
        req.setUrl_link(urlLink);
        QueryUrlLinkResult queryUrlLinkResult = WxaAPI.query_urllink(accessToken, req);
        log.info("查询小程序链接query_urllink返回：{}", JSONArray.toJSONString(queryUrlLinkResult));
        QueryUrlLinkInfoResult urlLinkInfo = queryUrlLinkResult.getUrl_link_info();
        if (Objects.isNull(urlLinkInfo)) {
            return false;
        }
        if (Objects.isNull(urlLinkInfo.getExpire_time())) {
            return false;
        }
        LocalDateTime expireTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(urlLinkInfo.getExpire_time()), ZoneId.systemDefault());
        if (expireTime.isAfter(LocalDateTime.now())) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 取消订单
     *
     * @param planOrderCancelDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelPlanOrder(PlanOrderCancelDto planOrderCancelDto) {
        // 校验数据
        checkCancelPlanOrderParam(planOrderCancelDto);
        // 查询计划单
        PlanOrder planOrder = null;
        if (Objects.nonNull(planOrderCancelDto.getPlanOrderId())) {
            planOrder = planOrderDao.getById(planOrderCancelDto.getPlanOrderId());
        } else if (Objects.nonNull(planOrderCancelDto.getPlanOrderNo())) {
            planOrder = planOrderDao.getByPlanOrderNo(planOrderCancelDto.getPlanOrderNo());
        }
        if (Objects.isNull(planOrder)) {
            throw new BizException("未找到计划单！");
        }
        // 校验计划单是否可以取消
        checkCancelPlanOrderValid(planOrderCancelDto, planOrder);

        // 取消计划单
        PlanOrderCancelInfoDto cancelInfoDto = AgentOrderConvert.convert2CancelInfo(planOrderCancelDto);
        cancelInfoDto.setPlanOrderId(planOrder.getId());
        // 查询商品快照
        Map<Long, String> itemSnapshotStores = getItemSnapshotStores(planOrderCancelDto.getTenantId(), Collections.singletonList(planOrder.getStoreId()), planOrder.getAgentOrderNo());
        cancelInfoDto.setItemSnapshot(itemSnapshotStores.get(planOrder.getStoreId()));
        boolean success = planOrderDao.updateCancel(cancelInfoDto);
        if (!success) {
            throw new BizException("取消计划单失败！");
        }
    }

    /**
     * 校验计划单是否可以取消
     *
     * @param planOrderCancelDto
     * @param planOrder
     */
    private void checkCancelPlanOrderValid(PlanOrderCancelDto planOrderCancelDto, PlanOrder planOrder) {
        Long tenantId = planOrderCancelDto.getTenantId();
        if (!tenantId.equals(planOrder.getTenantId())) {
            throw new BizException("只能取消当前租户下的计划单");
        }
        if (!AgentOrderEnum.PlanTypeEnum.isPlanTypeCreatePlanOrder(planOrder.getPlanType())) {
            throw new BizException("只有下单方式为创建计划单时，才可取消计划单");
        }
        if (!AgentOrderEnum.Status.WAIT_STORE_CONFIRM.getValue().equals(planOrder.getStatus())) {
            throw new BizException("只有待门店确认状态的计划单，才可取消");
        }
    }

    /**
     * 校验取消订单数据
     *
     * @param cancelDto
     */
    private void checkCancelPlanOrderParam(PlanOrderCancelDto cancelDto) {
        if (Objects.isNull(cancelDto.getPlanOrderId()) && Objects.isNull(cancelDto.getPlanOrderNo())) {
            throw new ParamsException("计划单ID和单号至少有一个必填！");
        }
        AssertCheckParams.notNull(cancelDto.getTenantId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户ID不可为空");
        AssertCheckParams.notNull(cancelDto.getOperatorId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "操作人ID不可为空");
        AssertCheckParams.notNull(cancelDto.getOperatorSource(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "操作来源不可为空");
        if (Objects.nonNull(cancelDto.getCancelRemark())) {
            cancelDto.setCancelRemark(cancelDto.getCancelRemark().trim());
            if (cancelDto.getCancelRemark().length() > AgentOrderConstant.CANCEL_REMARK_LENGTH) {
                throw new ParamsException("取消原因最长255个字符！");
            }
        }
    }


    /**
     * 获取当前商品快照
     *
     * @param tenantId
     * @param storeId
     * @param agentOrderItemMap
     * @param itemIds
     * @return
     */
    private List<PlanOrderDetailItemVO> getItemSnapshot(Long tenantId, Long storeId, List<Long> itemIds, Map<Long, AgentOrderItem> agentOrderItemMap) {

        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyList();
        }
        // 查询商品详情
        MarketItemQueryDTO queryDTO = new MarketItemQueryDTO();
        queryDTO.setTenantId(tenantId);
        queryDTO.setItemIds(itemIds);
        PageInfo<MarketItemInfoDTO> marketItemInfoDTOPageInfo = marketItemFacade.pageMarketItemAllInfo(queryDTO);
        List<MarketItemInfoDTO> itemList = marketItemInfoDTOPageInfo.getList();
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }
        // 查询商品价格
        Map<Long, PriceDetailVO> priceDetailMap = priceFacade.listItemPriceDetailByItemIds(tenantId, storeId, itemIds);
        // 查询库存
        StockQueryReq stockQueryReq = new StockQueryReq();
        stockQueryReq.setTenantId(tenantId);
        stockQueryReq.setStoreId(storeId);
        stockQueryReq.setItemIds(itemIds);
        Map<Long, StockInfoDTO> stockInfoMap = stockFacade.queryStock(stockQueryReq);
        // 生成快照信息
        List<PlanOrderDetailItemVO> itemSnapshotList = new ArrayList<>();
        for (MarketItemInfoDTO itemInfo : itemList) {
            PlanOrderDetailItemVO itemSnapshot = AgentOrderConvert.convert2ItemSnapshot(itemInfo);
            PriceDetailVO itemPrice = priceDetailMap.getOrDefault(itemInfo.getItemId(), new PriceDetailVO());
            AgentOrderItem agentOrderItem = agentOrderItemMap.getOrDefault(itemInfo.getItemId(), new AgentOrderItem());
            StockInfoDTO stockInfo = stockInfoMap.getOrDefault(itemInfo.getItemId(), new StockInfoDTO());

            itemSnapshot.setItemPrice(Optional.ofNullable(itemPrice.getPrice()).orElse(BigDecimal.ZERO));
            itemSnapshot.setItemAmount(Optional.ofNullable(agentOrderItem.getItemQuantity()).orElse(0));
            itemSnapshot.setItemTotalAmount(itemSnapshot.getItemPrice().multiply(BigDecimal.valueOf(itemSnapshot.getItemAmount())));
            itemSnapshot.setStockAmount(Optional.ofNullable(stockInfo.getAmount()).orElse(0));

            // 判断门店端状态
            if (!itemSnapshot.getOnSale().equals(OnSaleTypeEnum.ON_SALE.getCode()) || Objects.isNull(itemPrice.getPrice())) {
                itemSnapshot.setStoreShowStatus(AgentOrderConstant.ITEM_STORE_STATUS_SOLD_SALE);
            } else if (itemSnapshot.getStockAmount() <= NumberConstants.ZERO ||
                (Objects.nonNull(itemSnapshot.getMiniOrderQuantity()) && itemSnapshot.getItemAmount() < itemSnapshot.getMiniOrderQuantity()) ||
                (Objects.nonNull(itemSnapshot.getBuyMultiple()) && itemSnapshot.getItemAmount() < itemSnapshot.getBuyMultiple())) {
                itemSnapshot.setStoreShowStatus(AgentOrderConstant.ITEM_STORE_STATUS_OFF_STOCK);
            } else if (itemSnapshot.getDeleteFlag().equals(DeleteFlagEnum.DELETED.getFlag())) {
                itemSnapshot.setStoreShowStatus(AgentOrderConstant.ITEM_STORE_STATUS_DELETE);
            } else {
                itemSnapshot.setStoreShowStatus(AgentOrderConstant.ITEM_STORE_STATUS_ON_SALE);
            }
            itemSnapshotList.add(itemSnapshot);
        }
        return itemSnapshotList;
    }

    /**
     * 返回多家店铺的商品快照
     * key-storeId value-商品快照
     *
     * @param tenantId
     * @param storeIds
     * @param agentOrderNo
     * @return
     */
    private Map<Long, String> getItemSnapshotStores(Long tenantId, Collection<Long> storeIds, String agentOrderNo) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Collections.emptyMap();
        }
        // 查询商品列表
        List<AgentOrderItem> agentOrderItems = agentOrderItemDao.listByAgentOrderNo(agentOrderNo);
        if (CollectionUtils.isEmpty(agentOrderItems)) {
            return Collections.emptyMap();
        }
        Map<Long, AgentOrderItem> agentOrderItemMap = agentOrderItems.stream().collect(Collectors.toMap(AgentOrderItem::getItemId, Function.identity(), (k1, k2) -> k1));
        List<Long> itemIds = agentOrderItemMap.values().stream().map(AgentOrderItem::getItemId).collect(Collectors.toList());

        Map<Long, String> itemSnapshotMap = new HashMap<>();
        for (Long storeId : storeIds) {
            List<PlanOrderDetailItemVO> itemSnapshot = getItemSnapshot(tenantId, storeId, itemIds, agentOrderItemMap);
            itemSnapshotMap.put(storeId, JSON.toJSONString(itemSnapshot));
        }
        return itemSnapshotMap;
    }

    /**
     * 根据代下单编号，批量取消计划单
     *
     * @param agentOrderNo
     */
    @Override
    public void autoCancelPanOrder(String agentOrderNo) {
        log.info("系统自动取消超时的计划单，代下单号：{}", agentOrderNo);
        Long cancelRetryMillis = null;
        LocalDateTime cancelRetryTime = null;
        List<PlanOrder> planOrderList = planOrderDao.listCancelPlanOrderByAgentOrder(agentOrderNo);
        Set<Long> cancelStoreIds = new TreeSet<>();
        List<PlanOrder> cancelPlanOrder = new ArrayList<>();
        log.info("代下单号，待取消的计划单数量。代下单号：{},数量：{}", agentOrderNo, planOrderList.size());

        for (PlanOrder planOrder : planOrderList) {
            if (Objects.isNull(planOrder.getAutoCancelTime())) {
                log.error("计划单号：{}没有到期时间！", planOrder.getPlanOrderNo());
            } else if (planOrder.getAutoCancelTime().isAfter(LocalDateTime.now())) {
                // 还未到期，再次发送延时消息
                long betweenMillis = Duration.between(LocalDateTime.now(), planOrder.getAutoCancelTime()).toMillis();
                if (Objects.isNull(cancelRetryMillis) || cancelRetryMillis >= betweenMillis) {
                    cancelRetryMillis = betweenMillis;
                    cancelRetryTime = planOrder.getAutoCancelTime();
                }
            } else {
                // 已到期，执行自动取消
                cancelStoreIds.add(planOrder.getStoreId());
                cancelPlanOrder.add(planOrder);
            }
        }

        // 存在还未到期的计划单，需要再次发送延时消息
        if (Objects.nonNull(cancelRetryMillis)) {
            log.info("代下单号：{},在{}执行自动取消计划单。仍存在未到期的计划单，再次发送延时消息，延时毫秒：{},预计下次执行时间：{}"
                , agentOrderNo, LocalDateTime.now(), cancelRetryMillis, cancelRetryTime);
            SummerfarmMsgModel msgModel = new SummerfarmMsgModel();
            msgModel.setMsgType(MqTagConstant.AGENT_ORDER_CANCEL);
            msgModel.setMsgData(agentOrderNo);
            mqProducer.sendDelay(MqTopicConstant.SAAS_MANAGE_DELAY, MqTagConstant.AGENT_ORDER_CANCEL, msgModel, cancelRetryMillis);
        }

        // 批量取消计划单
        if (CollectionUtils.isNotEmpty(cancelPlanOrder)) {
            Map<Long, String> itemSnapshotStores = getItemSnapshotStores(cancelPlanOrder.get(0).getTenantId(), cancelStoreIds, agentOrderNo);

            for (PlanOrder planOrder : cancelPlanOrder) {
                PlanOrderCancelInfoDto cancelDto = new PlanOrderCancelInfoDto();
                cancelDto.setPlanOrderId(planOrder.getId());
                cancelDto.setCancelRemark(AgentOrderConstant.SYSTEM_CANCEL_REMARK);
                cancelDto.setOperatorId(AgentOrderConstant.SYSTEM_CANCEL_ID);
                cancelDto.setOperatorSource(AgentOrderEnum.CancelOrderRoleEnum.SYSTEM);
                cancelDto.setItemSnapshot(itemSnapshotStores.get(planOrder.getStoreId()));

                planOrderDao.updateCancel(cancelDto);
            }
        }
    }

    @Override
    public PageInfo<PlanOrderResp> queryPlanOrderPage(PlanOrderQueryReq planOrderQueryReq) {
        PlanOrderQueryParam queryParam = new PlanOrderQueryParam();
        queryParam.setPageIndex(planOrderQueryReq.getPageNum());
        queryParam.setPageSize(planOrderQueryReq.getPageSize());
        queryParam.setTenantId(planOrderQueryReq.getTenantId());
        queryParam.setStoreIds(planOrderQueryReq.getStoreIds());
        queryParam.setPlanOrderStatusList(planOrderQueryReq.getPlanOrderStatusList());
        queryParam.setPlanType(planOrderQueryReq.getPlanType());
        queryParam.setPlanOrderNo(planOrderQueryReq.getPlanOrderNo());

        Page<PlanOrder> planOrderPage = planOrderDao.queryPage(queryParam);

        List<String> agentOrderNos = planOrderPage.getRecords().stream().map(PlanOrder::getAgentOrderNo).distinct().collect(Collectors.toList());
        Map<String, List<AgentOrderItem>> agentOrderItemMap = agentOrderItemDao.listByAgentOrderNos(agentOrderNos);

        return PageInfoConverter.toPageInfo(planOrderPage, e -> convertPlanOrderResp(e, agentOrderItemMap));
    }

    private PlanOrderResp convertPlanOrderResp(PlanOrder planOrder, Map<String, List<AgentOrderItem>> agentOrderItemMap) {
        List<AgentOrderItem> agentOrderItemList = agentOrderItemMap.get(planOrder.getAgentOrderNo());
        List<PlanOrderItemResp> planOrderItems = agentOrderItemList.stream().map(e -> {
            PlanOrderItemResp planOrderItemResp = new PlanOrderItemResp();
            planOrderItemResp.setItemId(e.getItemId());
            planOrderItemResp.setItemAmount(e.getItemQuantity());
            return planOrderItemResp;
        }).collect(Collectors.toList());

        PlanOrderResp planOrderResp = new PlanOrderResp();
        planOrderResp.setPlanOrderId(planOrder.getId());
        planOrderResp.setPlanOrderNo(planOrder.getPlanOrderNo());
        planOrderResp.setAgentOrderNo(planOrder.getAgentOrderNo());
        planOrderResp.setPlanOrderStatus(planOrder.getStatus());
        planOrderResp.setItemCount(planOrderItems.size());
        planOrderResp.setItemTotalAmount(planOrderItems.stream().mapToInt(PlanOrderItemResp::getItemAmount).sum());
        planOrderResp.setPlanOrderCreateTime(planOrder.getCreateTime());
        planOrderResp.setRecommendReason(planOrder.getRecommendReason());
        planOrderResp.setPlanOrderItems(planOrderItems);
        planOrderResp.setItemInfoSnapshot(planOrder.getItemInfoSnapshot());
        planOrderResp.setPlanType(planOrder.getPlanType());

        return planOrderResp;
    }

    /**
     * manage后台分页查询计划单
     *
     * @param queryInput
     * @return
     */
    @Override
    public PageInfo<PlanOrderQueryListVO> queryManageOrderPage(PlanOrderListQueryInput queryInput) {
        PlanOrderQueryParam planOrderQueryParam = buildPlanOrderQueryParam(queryInput);
        if (Objects.isNull(planOrderQueryParam)) {
            return PageInfo.emptyPageInfo();
        }

        Page<PlanOrder> planOrderPage = planOrderDao.queryPage(planOrderQueryParam);
        if (CollectionUtils.isEmpty(planOrderPage.getRecords())) {
            return PageInfo.emptyPageInfo();
        }
        return buildManageOrderPageList(planOrderPage);
    }

    /**
     * 构造计划单分页查询的查询参数
     *
     * @param queryInput
     * @return
     */
    private PlanOrderQueryParam buildPlanOrderQueryParam(PlanOrderListQueryInput queryInput) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        PlanOrderQueryParam planOrderQueryParam = AgentOrderConvert.convert2PlanOrderQueryParam(queryInput);
        planOrderQueryParam.setTenantId(tenantId);
        // 查询门店
        if (Objects.nonNull(queryInput.getStoreNo()) || Objects.nonNull(queryInput.getStoreName()) || Objects.nonNull(queryInput.getStoreManagerPhone())) {
            Collection<Long> storeIds = buildPlanOrderQueryParamStoreIds(queryInput);
            if (CollectionUtils.isEmpty(storeIds)) {
                return null;
            }
            planOrderQueryParam.setStoreIds(storeIds);
        }
        // 查询操作人
        if (Objects.nonNull(queryInput.getAgentOperatorPhone())) {
            List<Long> authUserIds = buildPlanOrderQueryParamUserIds(queryInput);
            if (CollectionUtils.isEmpty(authUserIds)) {
                return null;
            }
            planOrderQueryParam.setCreatorUserIds(authUserIds);
        }
        // 查询订单号
        if (Objects.nonNull(queryInput.getOrderNo())) {
            OrderResp orderResp = orderQueryFacade.queryByNo(queryInput.getOrderNo());
            if (Objects.isNull(orderResp) || Objects.isNull(orderResp.getPlanOrderNo()) || !OrderSourceEnum.AGENT_ORDER.getValue().equals(orderResp.getOrderSource())) {
                return null;
            }
            if (Objects.nonNull(queryInput.getPlanOrderNo()) && !queryInput.getPlanOrderNo().equals(orderResp.getPlanOrderNo())) {
                return null;
            }
            planOrderQueryParam.setPlanOrderNo(orderResp.getPlanOrderNo());
        }

        return planOrderQueryParam;
    }

    /**
     * 查询符合条件的门店列表
     *
     * @param queryInput
     * @return
     */
    private Collection<Long> buildPlanOrderQueryParamStoreIds(PlanOrderListQueryInput queryInput) {
        MerchantStorePageQueryReq queryReq = new MerchantStorePageQueryReq();
        queryReq.setStoreNo(queryInput.getStoreNo());
        queryReq.setStoreName(queryInput.getStoreName());
        queryReq.setPhone(queryInput.getStoreManagerPhone());
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageSize(NumberConstants.ONE_THOUSAND);
        PageInfo<MerchantStorePageResultResp> merchantStorePage = storeFacade.getMerchantStorePage(queryReq, pageQueryReq);
        List<MerchantStorePageResultResp> storeList = merchantStorePage.getList();
        if (CollectionUtils.isEmpty(storeList)) {
            return null;
        }
        return storeList.stream().map(MerchantStorePageResultResp::getId).collect(Collectors.toSet());
    }

    /**
     * 查询符合条件的创建人
     *
     * @param queryInput
     * @return
     */
    private List<Long> buildPlanOrderQueryParamUserIds(PlanOrderListQueryInput queryInput) {
        List<TenantAccountResultResp> tenantAccountList = userCenterTenantAccountFacade.getTenantAccountByTenantIdsAndPhone(UserLoginContextUtils.getTenantId(), queryInput.getAgentOperatorPhone());
        if (CollectionUtils.isEmpty(tenantAccountList)) {
            return null;
        }
        return tenantAccountList.stream().map(TenantAccountResultResp::getAuthUserId).collect(Collectors.toList());
    }


    /**
     * 构造计划单分页查询的返回对象
     *
     * @param planOrderPage
     */
    private PageInfo<PlanOrderQueryListVO> buildManageOrderPageList(Page<PlanOrder> planOrderPage) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        List<PlanOrder> records = planOrderPage.getRecords();

        // 关联对象信息
        Set<Long> storeIds = new TreeSet<>();
        Set<String> agentOrderNos = new TreeSet<>();
        Set<Long> authUserIds = new TreeSet<>();
        for (PlanOrder record : records) {
            storeIds.add(record.getStoreId());
            authUserIds.add(record.getCreatorUserId());
            agentOrderNos.add(record.getAgentOrderNo());
        }

        // 每一个计划单的商品总价
        Map<Long, BigDecimal> itemTotalPriceMap = new HashMap<>();
        Map<String, List<AgentOrderItem>> itemMap = agentOrderItemDao.listByAgentOrderNos(agentOrderNos);
        for (PlanOrder record : records) {
            List<AgentOrderItem> agentOrderItems = itemMap.get(record.getAgentOrderNo());
            if (CollectionUtils.isEmpty(agentOrderItems)) {
                continue;
            }
            Set<Long> itemIds = agentOrderItems.stream().map(AgentOrderItem::getItemId).collect(Collectors.toSet());
            Map<Long, PriceDetailVO> priceMap = priceFacade.listItemPriceDetailByItemIds(tenantId, record.getStoreId(), new ArrayList<>(itemIds));
            BigDecimal totalPrice = agentOrderItems.stream().map(i -> {
                PriceDetailVO priceDetailVO = priceMap.get(i.getItemId());
                if (Objects.isNull(priceDetailVO)) {
                    return BigDecimal.ZERO;
                } else {
                    return priceDetailVO.getPrice().multiply(BigDecimal.valueOf(i.getItemQuantity()));
                }
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            itemTotalPriceMap.put(record.getId(), totalPrice);
        }

        // 门店信息
        MerchantStorePageQueryReq queryReq = new MerchantStorePageQueryReq();
        queryReq.setStoreIds(new ArrayList<>(storeIds));
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageSize(storeIds.size());
        PageInfo<MerchantStorePageResultResp> merchantStorePage = storeFacade.getMerchantStorePage(queryReq, pageQueryReq);
        List<MerchantStorePageResultResp> storeList = Optional.ofNullable(merchantStorePage.getList()).orElse(Collections.emptyList());
        Map<Long, MerchantStorePageResultResp> storeMap = storeList.stream().collect(Collectors.toMap(MerchantStorePageResultResp::getId, Function.identity(), (k1, k2) -> k1));


        // 创建人信息
        List<TenantAccountResultResp> tenantAccountByUserIds = userCenterTenantAccountFacade.getTenantAccountByUserIds(new ArrayList<>(authUserIds));
        Map<Long, TenantAccountResultResp> userMap = tenantAccountByUserIds.stream().collect(Collectors.toMap(TenantAccountResultResp::getAuthUserId, Function.identity(), (k1, k2) -> k1));

        return PageInfoConverter.toPageInfo(planOrderPage, (PlanOrder planOrder) -> {
            PlanOrderQueryListVO planOrderVo = AgentOrderConvert.convert2PlanOrderPageList(planOrder);
            // 门店信息
            MerchantStorePageResultResp storeInfo = storeMap.getOrDefault(planOrder.getStoreId(), new MerchantStorePageResultResp());
            planOrderVo.setStoreNo(storeInfo.getStoreNo());
            planOrderVo.setStoreName(storeInfo.getStoreName());
            planOrderVo.setStoreManagerPhone(storeInfo.getPhone());
            // 商品信息
            List<AgentOrderItem> itemList = itemMap.getOrDefault(planOrder.getAgentOrderNo(), Collections.emptyList());
            planOrderVo.setItemCount(itemList.size());
            planOrderVo.setItemTotalAmount(itemList.stream().map(AgentOrderItem::getItemQuantity).reduce(0, Integer::sum));
            BigDecimal totalPrice = itemTotalPriceMap.getOrDefault(planOrder.getId(), BigDecimal.ZERO);
            planOrderVo.setItemTotalPrice(totalPrice);
            // 创建人
            TenantAccountResultResp createUser = userMap.getOrDefault(planOrder.getCreatorUserId(), new TenantAccountResultResp());
            planOrderVo.setAgentOperatorAuthId(planOrder.getCreatorUserId());
            planOrderVo.setAgentOperatorName(createUser.getNickname());
            planOrderVo.setAgentOperatorPhone(createUser.getPhone());
            // 是否可提醒
            try {
                checkPlanOrderNotifyStatus(planOrder);
                planOrderVo.setEnableNotify(true);
            } catch (BizException bizEx) {
                planOrderVo.setEnableNotify(false);
            }

            return planOrderVo;
        });
    }


    /**
     * 查询计划单详情
     *
     * @param queryInput
     * @return
     */
    @Override
    public PlanOrderDetailTotalVO planOrderDetail(PlanOrderQueryInput queryInput) {
        PlanOrder planOrder = planOrderDao.getByPlanOrderNo(queryInput.getPlanOrderNo());
        if (Objects.isNull(planOrder)) {
            throw new BizException("计划单详情不存在");
        }
        Long tenantId = UserLoginContextUtils.getTenantId();
        if (Objects.isNull(tenantId)) {
            throw new BizException("请先登录后再查询计划单详情！");
        }
        if (!tenantId.equals(planOrder.getTenantId())) {
            throw new BizException("只能看到当前租户的计划单详情！");
        }

        // 返回总对象
        PlanOrderDetailTotalVO detailTotalVO = new PlanOrderDetailTotalVO();

        // 门店信息
        MerchantStoreResultResp store = Optional.ofNullable(storeFacade.getMerchantStoreById(planOrder.getStoreId())).orElse(new MerchantStoreResultResp());
        MerchantStoreAccountDTO storeManageAccount = Optional.ofNullable(merchantStoreAccountService.selectManager(tenantId, planOrder.getStoreId())).orElse(new MerchantStoreAccountDTO());
        PlanOrderDetailStoreVO storeVO = new PlanOrderDetailStoreVO();
        storeVO.setStoreId(store.getId());
        storeVO.setStoreNo(store.getStoreNo());
        storeVO.setStoreName(store.getStoreName());
        storeVO.setStoreManagerPhone(storeManageAccount.getPhone());
        detailTotalVO.setPlanOrderDetailStoreVO(storeVO);

        // 商品信息
        List<AgentOrderItem> agentOrderItems = agentOrderItemDao.listByAgentOrderNo(planOrder.getAgentOrderNo());
        Map<Long, AgentOrderItem> agentOrderItemMap = agentOrderItems.stream().collect(Collectors.toMap(AgentOrderItem::getItemId, Function.identity(), (k1, k2) -> k1));
        List<Long> itemIds = agentOrderItems.stream().map(AgentOrderItem::getItemId).collect(Collectors.toList());
        Integer itemTotalAmount = agentOrderItems.stream().map(AgentOrderItem::getItemQuantity).reduce(0, Integer::sum);

        // 构建计划单详情
        buildPlanOrderDetailInfo(planOrder, detailTotalVO, agentOrderItems.size(), itemTotalAmount);
        // 构造订单信息
        buildPlanOrderDetailOrderInfo(planOrder, detailTotalVO, agentOrderItems.size(), storeManageAccount.getPhone());
        // 取消订单角色
        PlanOrderDetailCreateVO planOrderDetailCreateVO = detailTotalVO.getPlanOrderDetailCreateVO();
        if (AgentOrderEnum.Status.PLAN_ORDER_CANCEL.getValue().equals(planOrder.getStatus())) {
            planOrderDetailCreateVO.setCancelOrderRole(AgentOrderEnum.CancelOrderRoleEnum.valueOf(planOrder.getCancelType()).getContent());
            planOrderDetailCreateVO.setCancelOrderTime(planOrder.getCancelTime());
        }
        // 商品详情
        buildPlanOrderItemInfo(planOrder, detailTotalVO, agentOrderItemMap, itemIds);

        // 人员信息
        buildPlanOrderUserInfo(planOrder, detailTotalVO);

        return detailTotalVO;
    }

    /**
     * 构造人员信息
     *
     * @param planOrder
     * @param detailTotalVO
     */
    private void buildPlanOrderUserInfo(PlanOrder planOrder, PlanOrderDetailTotalVO detailTotalVO) {
        Set<Long> authUserIds = new TreeSet<>();
        authUserIds.add(planOrder.getCreatorUserId());
        Set<Long> accountIds = new TreeSet<>();
        if (AgentOrderEnum.Status.PLAN_ORDER_CANCEL.getValue().equals(planOrder.getStatus()) && !AgentOrderConstant.SYSTEM_CANCEL_ID.equals(planOrder.getCancelUserId())) {
            authUserIds.add(planOrder.getCancelUserId());
            accountIds.add(planOrder.getCancelUserId());
        }

        Long storeOperatorAuthId = detailTotalVO.getPlanOrderDetailCreateVO().getStoreOperatorAuthId();
        if (Objects.nonNull(storeOperatorAuthId)) {
            accountIds.add(storeOperatorAuthId);
        }
        List<TenantAccountResultResp> accountByUserIds = userCenterTenantAccountFacade.getTenantAccountByUserIds(new ArrayList<>(authUserIds));
        Map<Long, TenantAccountResultResp> authUserMap = accountByUserIds.stream().collect(Collectors.toMap(TenantAccountResultResp::getAuthUserId, Function.identity(), (k1, k2) -> k1));
        Map<Long, MerchantStoreAccountResultResp> accountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(accountIds)) {
            List<MerchantStoreAccountResultResp> tenantAccounts = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(new ArrayList<>(accountIds));
            accountMap = tenantAccounts.stream().collect(Collectors.toMap(MerchantStoreAccountResultResp::getId, Function.identity(), (k1, k2) -> k1));
        }
        // 计划单创建人
        PlanOrderDetailVO planOrderDetailVO = detailTotalVO.getPlanOrderDetailVO();
        TenantAccountResultResp authUserInfo = authUserMap.getOrDefault(planOrderDetailVO.getAgentOperatorAuthId(), new TenantAccountResultResp());
        planOrderDetailVO.setAgentOperatorName(authUserInfo.getNickname());
        planOrderDetailVO.setAgentOperatorPhone(authUserInfo.getPhone());

        PlanOrderDetailCreateVO planOrderDetailCreateVO = detailTotalVO.getPlanOrderDetailCreateVO();
        // 门店下单人
        if (Objects.nonNull(storeOperatorAuthId)) {
            MerchantStoreAccountResultResp orderUser = accountMap.getOrDefault(storeOperatorAuthId, new MerchantStoreAccountResultResp());
            planOrderDetailCreateVO.setStoreOperatorAuthName(orderUser.getAccountName());
            planOrderDetailCreateVO.setStoreOperatorAuthPhone(orderUser.getPhone());
        }
        if (PlanTypeEnum.CREATE_ORDER.name().equals(planOrder.getPlanType())) {
            planOrderDetailCreateVO.setStoreOperatorAuthName(AgentOrderEnum.CreateOrderRoleEnum.TENANT.getContent());
        }
        // 取消人
        if (AgentOrderEnum.Status.PLAN_ORDER_CANCEL.getValue().equals(planOrder.getStatus())) {
            if (AgentOrderEnum.CancelOrderRoleEnum.STORE.name().equals(planOrder.getCancelType())) {
                MerchantStoreAccountResultResp accountInfo = accountMap.getOrDefault(planOrder.getCancelUserId(), new MerchantStoreAccountResultResp());
                planOrderDetailCreateVO.setStoreOperatorAuthName(accountInfo.getAccountName());
                planOrderDetailCreateVO.setStoreOperatorAuthPhone(accountInfo.getPhone());
            } else if (AgentOrderEnum.CancelOrderRoleEnum.TENANT.name().equals(planOrder.getCancelType())) {
                TenantAccountResultResp cancelUser = authUserMap.getOrDefault(planOrder.getCancelUserId(), new TenantAccountResultResp());
                planOrderDetailCreateVO.setStoreOperatorAuthName(cancelUser.getNickname());
                planOrderDetailCreateVO.setStoreOperatorAuthPhone(cancelUser.getPhone());
            }
        }
    }

    /**
     * 构造商品详情
     *
     * @param planOrder
     * @param detailTotalVO
     * @param agentOrderItemMap
     * @param itemIds
     * @return
     */
    private void buildPlanOrderItemInfo(PlanOrder planOrder, PlanOrderDetailTotalVO detailTotalVO, Map<Long, AgentOrderItem> agentOrderItemMap, List<Long> itemIds) {
        List<PlanOrderDetailItemVO> itemVOList = new ArrayList<>();
        if (AgentOrderConstant.REAL_TIME_ITEM_LIST.contains(planOrder.getStatus())) {
            itemVOList = getItemSnapshot(planOrder.getTenantId(), planOrder.getStoreId(), itemIds, agentOrderItemMap);
        } else {
            List<PlanOrderDetailItemVO> allItemVOList = JSONArray.parseArray(planOrder.getItemInfoSnapshot(), PlanOrderDetailItemVO.class);
            itemVOList = Optional.ofNullable(allItemVOList).orElse(Collections.emptyList());
        }
        detailTotalVO.setAllItemList(itemVOList);
        detailTotalVO.setUnOrderItemTotalPrice(itemVOList.stream()
            .map(PlanOrderDetailItemVO::getItemTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add));

        // 仅在下单成功和下单失败两个状态，需要展示下单结果
        if (AgentOrderConstant.ORDER_STATUS_LIST.contains(planOrder.getStatus())) {
            // 已生成订单的商品
            List<PlanOrderDetailOrderVO> orderDetailList = Optional.ofNullable(detailTotalVO.getOrderDetailList()).orElse(Collections.emptyList());
            // 下单成功的商品
            List<Long> successOrderItemIds = orderDetailList.stream()
                .filter(o -> StringUtils.isEmpty(o.getFailReason()))
                .flatMap(order -> order.getItemVOList().stream())
                .map(PlanOrderDetailItemVO::getItemId)
                .collect(Collectors.toList());
            // 订单上的失败原因
            Map<Long, String> orderFailResult = orderDetailList.stream()
                .filter(o -> StringUtils.isNotEmpty(o.getFailReason()))
                .flatMap(detailOrderVO -> detailOrderVO.getItemVOList().stream()
                    .map(itemVO -> new AbstractMap.SimpleEntry<>(itemVO.getItemId(), detailOrderVO.getFailReason())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            ArrayList<Long> orderFailItemIds = new ArrayList<>(orderFailResult.keySet());
            // 系统失败原因
            String failReason = Optional.ofNullable(detailTotalVO.getPlanOrderDetailVO()).orElse(new PlanOrderDetailVO()).getFailReason();
            // 商品失败原因
            List<PlanOrderFailReasonDto> failReasonList = JSONArray.parseArray(planOrder.getFailReason(), PlanOrderFailReasonDto.class);
            failReasonList = Optional.ofNullable(failReasonList).orElse(Collections.emptyList());
            Map<Long, PlanOrderFailReasonDto> failReasonDtoMap = failReasonList.stream()
                .filter(i -> Objects.nonNull(i.getItemId()))
                .collect(Collectors.toMap(PlanOrderFailReasonDto::getItemId, Function.identity(), (k1, k2) -> k1));

            // 排序 先放订单成功，再放订单失败，再放未生成订单
            List<PlanOrderDetailItemVO> sortedItemList = itemVOList.stream()
                .sorted(Comparator.comparing(item -> {
                    if (successOrderItemIds.contains(item.getItemId())) {
                        return 0;
                    } else if (orderFailItemIds.contains(item.getItemId())) {
                        return 1;
                    } else {
                        return 2;
                    }
                }))
                .collect(Collectors.toList());

            for (PlanOrderDetailItemVO itemVO : sortedItemList) {
                if (successOrderItemIds.contains(itemVO.getItemId())) {
                    itemVO.setOrderResult(AgentOrderConstant.ITEM_STORE_STATUS_ORDER);
                } else {
                    String errMsg = AgentOrderConstant.ITEM_STORE_STATUS_UNORDER;
                    String orderFailReason = orderFailResult.get(itemVO.getItemId());
                    PlanOrderFailReasonDto itemFailReason = failReasonDtoMap.getOrDefault(itemVO.getItemId(), new PlanOrderFailReasonDto());
                    if (StringUtils.isNotEmpty(failReason)) {
                        errMsg = errMsg + failReason;
                    }
                    if (StringUtils.isNotEmpty(orderFailReason)) {
                        errMsg = errMsg + orderFailReason;
                    }
                    if (StringUtils.isNotEmpty(itemFailReason.getFailReason())) {
                        errMsg = errMsg + itemFailReason.getFailReason();
                    }
                    itemVO.setOrderResult(errMsg);
                }
            }
            detailTotalVO.setAllItemList(sortedItemList);
        }

    }


    /**
     * 构建计划单详情
     *
     * @param planOrder
     * @param detailTotalVO
     * @param itemCount
     * @param itemTotalAmount
     */
    private void buildPlanOrderDetailInfo(PlanOrder planOrder, PlanOrderDetailTotalVO detailTotalVO, Integer itemCount, Integer itemTotalAmount) {
        // 基础信息
        PlanOrderDetailVO detailVO = AgentOrderConvert.convert2PlanOrderDetailVo(planOrder);
        detailVO.setItemCount(itemCount);
        detailVO.setItemTotalAmount(itemTotalAmount);
        // 是否可提醒
        try {
            checkPlanOrderNotifyStatus(planOrder);
            detailVO.setEnableNotify(true);
        } catch (BizException bizEx) {
            detailVO.setEnableNotify(false);
        }
        // 失败原因
        List<PlanOrderFailReasonDto> failReasonList = JSONArray.parseArray(planOrder.getFailReason(), PlanOrderFailReasonDto.class);
        failReasonList = Optional.ofNullable(failReasonList).orElse(Collections.emptyList());
        String failReason = failReasonList.stream()
            .filter(i -> AgentOrderEnum.CreateOrderFailTypeEnum.SYSTEM.name().equals(i.getFailType()))
            .map(PlanOrderFailReasonDto::getFailReason)
            .filter(Objects::nonNull)
            .collect(Collectors.joining(";"));
        detailVO.setFailReason(failReason);

        detailTotalVO.setPlanOrderDetailVO(detailVO);
    }

    /**
     * 构造订单详情
     *
     * @param planOrder
     * @param detailTotalVO
     */
    private void buildPlanOrderDetailOrderInfo(PlanOrder planOrder, PlanOrderDetailTotalVO detailTotalVO, Integer itemCount, String storeManagePhone) {
        PlanOrderDetailCreateVO planOrderDetailCreateVO = detailTotalVO.getPlanOrderDetailCreateVO();
        if (Objects.isNull(planOrderDetailCreateVO)) {
            planOrderDetailCreateVO = new PlanOrderDetailCreateVO();
            detailTotalVO.setPlanOrderDetailCreateVO(planOrderDetailCreateVO);
        }

        if (AgentOrderConstant.NO_ORDER_LIST.contains(planOrder.getStatus())) {
            return;
        }
        // 已取消-没有订单信息
        List<PlanOrderFailReasonDto> failReasonList = JSONArray.parseArray(planOrder.getFailReason(), PlanOrderFailReasonDto.class);
        failReasonList = Optional.ofNullable(failReasonList).orElse(Collections.emptyList());
        Map<String, PlanOrderFailReasonDto> failReasonDtoMap = failReasonList.stream()
            .filter(i -> Objects.nonNull(i.getOrderNo()))
            .collect(Collectors.toMap(PlanOrderFailReasonDto::getOrderNo, Function.identity(), (k1, k2) -> k1));

        List<String> successOrderNos = JSONArray.parseArray(planOrder.getSuccessCreateOrderNo(), String.class);
        successOrderNos = Optional.ofNullable(successOrderNos).orElse(Collections.emptyList());

        List<String> orderNos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(failReasonDtoMap.keySet())) {
            orderNos.addAll(failReasonDtoMap.keySet());
        }
        if (CollectionUtils.isNotEmpty(successOrderNos)) {
            orderNos.addAll(successOrderNos);
        }
        planOrderDetailCreateVO.setCreateOrderCount(orderNos.size());
        if (CollectionUtils.isEmpty(orderNos)) {
            planOrderDetailCreateVO.setFailItemCount(itemCount);
            return;
        }

        // 订单信息
        List<OrderResp> orderResps = orderQueryFacade.queryByNos(orderNos);
        if (CollectionUtils.isEmpty(orderResps)) {
            return;
        }
        // 创建订单角色
        if (AgentOrderEnum.PlanTypeEnum.CREATE_ORDER.name().equals(planOrder.getPlanType())) {
            planOrderDetailCreateVO.setCreateOrderRole(AgentOrderEnum.CreateOrderRoleEnum.TENANT.getContent());
        } else {
            planOrderDetailCreateVO.setCreateOrderRole(AgentOrderEnum.CreateOrderRoleEnum.STORE.getContent());
            planOrderDetailCreateVO.setStoreOperatorAuthId(orderResps.get(0).getAccountId());
        }

        List<Long> orderIds = orderResps.stream().map(OrderResp::getId).collect(Collectors.toList());
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(orderIds);
        List<OrderItemVO> orderItemVOS = orderItemQueryFacade.queryOrderItemListVO(orderItemQueryReq);
        planOrderDetailCreateVO.setCreateItemCount(orderItemVOS.size());
        planOrderDetailCreateVO.setCreateItemTotalAmount(orderItemVOS.stream().map(OrderItemVO::getAmount).reduce(0, Integer::sum));
        planOrderDetailCreateVO.setFailItemCount(itemCount - planOrderDetailCreateVO.getCreateItemCount());
        detailTotalVO.setOrderTotalPrice(orderResps.stream().map(OrderResp::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));

        // 订单商品列表
        Map<Long, List<OrderItemVO>> orderItemMap = orderItemVOS.stream().collect(Collectors.groupingBy(OrderItemVO::getOrderId));
        // 构造订单列表
        List<PlanOrderDetailOrderVO> orderDetailList = new ArrayList<>();
        for (OrderResp orderDTO : orderResps) {
            PlanOrderDetailOrderVO detailOrderVO = new PlanOrderDetailOrderVO();
            detailOrderVO.setOrderId(orderDTO.getId());
            detailOrderVO.setOrderNo(orderDTO.getOrderNo());
            detailOrderVO.setOrderCreateTime(orderDTO.getCreateTime());
            detailOrderVO.setStoreManagerPhone(storeManagePhone);
            detailOrderVO.setOrderPayTime(orderDTO.getPayTime());
            detailOrderVO.setOrderStatusOriEnum(orderDTO.getStatus());
            detailOrderVO.setOrderStatusEnum(OrderStatusEnum.transferOrderStatus(orderDTO.getStatus()));
            detailOrderVO.setOrderStatusStr(OrderStatusEnum.getDesc(detailOrderVO.getOrderStatusEnum()));
            PlanOrderFailReasonDto failReasonDto = failReasonDtoMap.getOrDefault(orderDTO.getOrderNo(), new PlanOrderFailReasonDto());
            detailOrderVO.setFailReason(failReasonDto.getFailReason());
            if (StringUtils.isNotEmpty(detailOrderVO.getFailReason())) {
                detailOrderVO.setOrderStatus(AgentOrderEnum.CreateOrderStatusEnum.FAIL.name());
            } else {
                detailOrderVO.setOrderStatus(AgentOrderEnum.CreateOrderStatusEnum.SUCCESS.name());
            }
            // 商品列表
            List<OrderItemVO> orderItemList = orderItemMap.get(orderDTO.getId());
            detailOrderVO.setItemVOList(orderItemList.stream().map(AgentOrderConvert::convert2ItemVo).collect(Collectors.toList()));
            orderDetailList.add(detailOrderVO);
        }
        detailTotalVO.setOrderDetailList(orderDetailList);
    }

    @Override
    public PlanOrderDetailResp planOrderDetail(String planOrderNo) {
        PlanOrder planOrder = planOrderDao.getByPlanOrderNo(planOrderNo);
        if (planOrder == null) {
            return null;
        }

        // 返回总对象
        PlanOrderDetailResp planOrderDetailResp = new PlanOrderDetailResp();
        planOrderDetailResp.setPlanOrderId(planOrder.getId());
        planOrderDetailResp.setPlanOrderNo(planOrder.getPlanOrderNo());
        planOrderDetailResp.setAgentOrderNo(planOrder.getAgentOrderNo());
        planOrderDetailResp.setPlanOrderStatus(planOrder.getStatus());
        planOrderDetailResp.setPlanOrderCreateTime(planOrder.getCreateTime());
        planOrderDetailResp.setRecommendReason(planOrder.getRecommendReason());
        planOrderDetailResp.setPlanType(planOrder.getPlanType());
        planOrderDetailResp.setItemInfoSnapshot(planOrder.getItemInfoSnapshot());

        // 计划单取消信息
        planOrderDetailResp.setCancelReason(planOrder.getCancelRemark());
        planOrderDetailResp.setAutoCancelTime(planOrder.getAutoCancelTime());
        planOrderDetailResp.setCancelType(planOrder.getCancelType());
        planOrderDetailResp.setCancelTime(planOrder.getCancelTime());
        if (AgentOrderEnum.CancelOrderRoleEnum.STORE.name().equalsIgnoreCase(planOrder.getCancelType())) {
            MerchantStoreAccountResultResp accountResultResp = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(planOrder.getCancelUserId());
            if (accountResultResp != null) {
                planOrderDetailResp.setCancelUserName(accountResultResp.getAccountName());
                planOrderDetailResp.setCancelUserPhone(accountResultResp.getPhone());
            }
        }

        planOrderDetailResp.setOrderCreateTime(planOrder.getFinishTime());

        if (StringUtils.isNotBlank(planOrder.getSuccessCreateOrderNo())) {
            List<String> orderNos = JSONArray.parseArray(planOrder.getSuccessCreateOrderNo(), String.class);
            planOrderDetailResp.setOrderNoList(orderNos);
        }

        // 商品信息
        List<AgentOrderItem> agentOrderItems = agentOrderItemDao.listByAgentOrderNo(planOrder.getAgentOrderNo());
        Integer itemTotalAmount = agentOrderItems.stream().map(AgentOrderItem::getItemQuantity).reduce(0, Integer::sum);

        planOrderDetailResp.setItemCount(agentOrderItems.size());
        planOrderDetailResp.setItemTotalAmount(itemTotalAmount);

        List<PlanOrderItemResp> planOrderItems = agentOrderItems.stream().map(e -> {
            PlanOrderItemResp planOrderItemResp = new PlanOrderItemResp();
            planOrderItemResp.setItemId(e.getItemId());
            planOrderItemResp.setItemAmount(e.getItemQuantity());
            return planOrderItemResp;
        }).collect(Collectors.toList());
        planOrderDetailResp.setPlanOrderItems(planOrderItems);

        return planOrderDetailResp;
    }

    @Override
    public boolean updateCreateOrderFail(CreateOrderFailReq createOrderFailReq) {
        PlanOrder planOrder = planOrderDao.getByPlanOrderNo(createOrderFailReq.getPlanOrderNo());
        if (planOrder == null) {
            throw new BizException("计划单记录不存在" + createOrderFailReq.getPlanOrderNo());
        }

        PlanOrderCreateOrderFailDto createOrderFailDto = new PlanOrderCreateOrderFailDto();
        createOrderFailDto.setPlanOrderId(planOrder.getId());
        createOrderFailDto.setTenantId(planOrder.getTenantId());
        createOrderFailDto.setPlanTypeEnum(PlanTypeEnum.getByPlanType(planOrder.getPlanType()));
        createOrderFailDto.setFailReason(createOrderFailReq.getFailReason());

        Map<Long, String> itemSnapshotStores = getItemSnapshotStores(planOrder.getTenantId(), Collections.singletonList(planOrder.getStoreId()), planOrder.getAgentOrderNo());
        createOrderFailDto.setItemSnapshot(itemSnapshotStores.get(planOrder.getStoreId()));

        OrderQueryReq queryReq = new OrderQueryReq();
        queryReq.setTenantId(planOrder.getTenantId());
        queryReq.setPlanOrderNo(planOrder.getPlanOrderNo());
        List<OrderResp> orderResps = orderQueryFacade.queryOrderList(queryReq);
        // 计划单创建订单不为空，更新下单时间
        if (!CollectionUtils.isEmpty(orderResps)) {
            createOrderFailDto.setOrderTime(LocalDateTime.now());
        }

        // 创建订单失败，短信通知
        if (PlanTypeEnum.CREATE_ORDER.name().equals(planOrder.getPlanType())) {
            ((PlanOrderServiceImpl) AopContext.currentProxy()).createOrderFailSendSms(planOrder);
        }

        return planOrderDao.updateCreateOrderFail(createOrderFailDto);
    }

    @Async
    public void createOrderFailSendSms(PlanOrder planOrder){
        log.info("代下单失败短信提醒，planOrder={}", JSON.toJSONString(planOrder));

        if(isRepeatRequest(planOrder.getAgentOrderNo())){
            log.info("代下单失败短信提醒，同一个代下单号重复请求 agentOrderNo={}，planOrder={}", planOrder.getAgentOrderNo(), JSON.toJSONString(planOrder));
            return;
        }

        List<TenantAccountResultResp> accountByUserIds = userCenterTenantAccountFacade.getTenantAccountByUserIds(Lists.newArrayList(planOrder.getCreatorUserId()));
        Map<Long, TenantAccountResultResp> authUserMap = accountByUserIds.stream().collect(Collectors.toMap(TenantAccountResultResp::getAuthUserId, Function.identity(), (k1, k2) -> k1));

        String phone = authUserMap.getOrDefault(planOrder.getCreatorUserId(), new TenantAccountResultResp()).getPhone();
        if(StringUtils.isBlank(phone)){
            log.error("authUserId={}，手机号为空", planOrder.getCreatorUserId());
            return;
        }

        MerchantStoreResultResp merchantStoreResultResp = storeFacade.getMerchantStoreById(planOrder.getStoreId());
        if(merchantStoreResultResp == null){
            log.error("storeId={}，门店信息为空", planOrder.getStoreId());
            return;
        }

        // 发送短信
        Sms sendSms = new Sms();
        sendSms.setSceneId(SmsConstants.PLAN_ORDER_FAIL_NOTIFY);
        sendSms.setPhone(phone);
        String url = String.format("https://manage.cosfo.cn/index.html#/order/plan-order?planOrderStatus=300&agentOrderNo=%s", planOrder.getAgentOrderNo());
        sendSms.setArgs(Arrays.asList(merchantStoreResultResp.getStoreNo(), url));
        boolean success = smsSenderFactory.getSmsSender().sendSms(sendSms);
        if (success) {
            log.info("代下单号：{}发送下单失败短信成功", planOrder.getAgentOrderNo());
        } else {
            log.info("代下单号：{}发送下单失败短信失败", planOrder.getAgentOrderNo());
        }
    }


    /**
     * 检查是否重复通知，true-是 false-否
     *
     * @param agentOrderNo
     * @return
     */
    private boolean isRepeatRequest(String agentOrderNo) {
        try {
            String redisKey = RedisKeyEnum.CM00015.join(agentOrderNo);
            RLock lock = redissonClient.getLock(redisKey);
            // 获取到锁，不是重复请求
            if (lock.tryLock(0, 1, TimeUnit.DAYS)) {
                return false;
            }
        } catch (InterruptedException e) {
            log.error("代下单下单失败发送提醒获取redis锁异常", e);
        }

        // 未获取到锁，重复请求
        return true;
    }

    @Override
    public boolean updateCreateOrderSuccess(CreateOrderSuccessReq createOrderSuccessReq) {
        PlanOrder planOrder = planOrderDao.getByPlanOrderNo(createOrderSuccessReq.getPlanOrderNo());
        if (planOrder == null) {
            throw new BizException("计划单记录不存在" + createOrderSuccessReq.getPlanOrderNo());
        }

        PlanOrderCreateOrderSuccessDto createOrderSuccessDto = new PlanOrderCreateOrderSuccessDto();
        createOrderSuccessDto.setPlanOrderId(planOrder.getId());
        createOrderSuccessDto.setTenantId(planOrder.getTenantId());
        createOrderSuccessDto.setPlanTypeEnum(PlanTypeEnum.getByPlanType(planOrder.getPlanType()));
        createOrderSuccessDto.setFailReason(createOrderSuccessReq.getFailReason());
        createOrderSuccessDto.setOrderNoList(createOrderSuccessReq.getOrderNoList());

        Map<Long, String> itemSnapshotStores = getItemSnapshotStores(planOrder.getTenantId(), Collections.singletonList(planOrder.getStoreId()), planOrder.getAgentOrderNo());
        createOrderSuccessDto.setItemSnapshot(itemSnapshotStores.get(planOrder.getStoreId()));

        return planOrderDao.updateCreateOrderSuccess(createOrderSuccessDto);
    }

    /**
     * 为所有符合条件的计划单生成订单
     */
    @Override
    public void planOrderCreateOrder(List<String> planOrderNos) {
        List<PlanOrder> createOrderList = planOrderDao.listWaitCreateOrders(planOrderNos);
        if (CollectionUtils.isEmpty(createOrderList)) {
            log.info("没有需要创建订单的计划单……");
            return;
        } else {
            log.info("正在准备为{}条计划单创建订单", createOrderList.size());
        }
        Set<String> agentOrderNos = createOrderList.stream().map(PlanOrder::getAgentOrderNo).collect(Collectors.toSet());
        Map<String, List<AgentOrderItem>> orderItemMap = agentOrderItemDao.listByAgentOrderNos(agentOrderNos);
        List<Runnable> tasks = new ArrayList<>();
        for (PlanOrder planOrder : createOrderList) {
            tasks.add(() -> {
                try {
                    log.info("正在为计划单号：{}，创建订单", planOrder.getPlanOrderNo());
                    String redisKey = AgentOrderConstant.CREATE_ORDER_REDIS_KEY_PREFIX + planOrder.getPlanOrderNo();
                    Object createOrder = redisUtils.getNoPrefix(redisKey);
                    if (Objects.isNull(createOrder)) {
                        // 缓存中不存在：增加缓存，并创建订单
                        boolean redisResult = redisUtils.setIfAbsent(redisKey, planOrder.getPlanOrderNo(), AgentOrderConstant.CREATE_ORDER_REDIS_TTL, TimeUnit.MINUTES);
                        if (!redisResult) {
                            log.error("创建订单redisKey失败！计划单号：{}，redisKey：{}", planOrder.getPlanOrderNo(), redisKey);
                            return;
                        }
                        // 创建订单
                        PlanOrderCreateOrderReq createOrderReq = new PlanOrderCreateOrderReq();
                        createOrderReq.setTenantId(planOrder.getTenantId());
                        createOrderReq.setStoreId(planOrder.getStoreId());
                        createOrderReq.setPlanOrderNo(planOrder.getPlanOrderNo());
                        List<AgentOrderItem> agentOrderItems = orderItemMap.get(planOrder.getAgentOrderNo());
                        if (CollectionUtils.isEmpty(agentOrderItems)) {
                            log.error("计划单号：{}，没有商品列表", planOrder.getPlanOrderNo());
                            return;
                        }
                        createOrderReq.setOrderItemList(agentOrderItems.stream().map(i -> {
                            PlanOrderCreateOrderReq.PlanOrderItem item = new PlanOrderCreateOrderReq.PlanOrderItem();
                            item.setItemId(i.getItemId());
                            item.setQuantity(i.getItemQuantity());
                            return item;
                        }).collect(Collectors.toList()));
                        planOrderFacade.createOrder(createOrderReq);
                        log.info("计划单号：{}，创建订单完成", planOrder.getPlanOrderNo());
                    } else {
                        // 缓存中已存在：不做处理
                        log.info("计划单号：{}，已有其它线程正在执行创建订单……", planOrder.getPlanOrderNo());
                    }
                } catch (Exception e) {
                    log.error("计划单号：{}，生成订单失败！", planOrder.getPlanOrderNo(), e);
                }

            });
        }

        ThreadPoolHelper.build(ExecutorFactory.AGENT_ORDER_CREATE_EXECUTOR).batchTask(tasks);

    }
}
