package com.cosfo.manage.wangdiantong.sdk.api.sales.dto;

import com.google.gson.annotations.SerializedName;

import java.math.BigDecimal;
import java.util.List;

public class RawTradeSearchHistoryResponse {
    @SerializedName("total_count")
    private Integer total;
    @SerializedName("order")
    private List<OrderItem> orders;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<OrderItem> getOrders() {
        return orders;
    }

    public void setOrders(List<OrderItem> orders) {
        this.orders = orders;
    }

    public static class OrderItem {
        private Integer recId;
        private Integer platformId;
        private String tid;
        private Integer shopId;
        private String shopNo;
        private Integer processStatus;
        private Integer tradeStatus;
        private Integer payStatus;
        private Integer payMethod;
        private Integer refundStatus;
        private String badReason;
        private BigDecimal orderCount;
        private BigDecimal goodsCount;
        private String tradeTime;
        private String payTime;
        private String endTime;
        private String buyerMessage;
        private String remark;
        private String buyerNick;
        private String buyerEmail;
        private String buyerName;
        private String buyerArea;
        private String payId;
        private String payAccount;
        private String receiverName;
        private Integer receiverCountry;
        private Integer receiverProvince;
        private Integer receiverCity;
        private Integer receiverDistrict;
        private String receiverAddress;
        private String receiverMobile;
        private String receiverTelno;
        private String receiverArea;
        private String receiverRing;
        private String toDeliverTime;
        private BigDecimal goodsAmount;
        private BigDecimal postAmount;
        private BigDecimal otherAmount;
        private BigDecimal discount;
        private BigDecimal receivable;
        private BigDecimal paid;
        private BigDecimal platformCost;
        private String warehouseNo;
        private boolean isAutoWms;
        private String modified;
        private String created;
        private String oaid;
        private Integer deliveryTerm;
        private Integer guaranteeMode;
        private BigDecimal received;
        private String currency;
        private String fenxiaoNick;
        private byte fenxiaoType;
        private String receiverZip;
        private String preChargeTime;
        private BigDecimal refundAmount;
        private byte tradeFrom;
        private List<OrderDetailItem> tradeOrders;
        private List<OrderDiscountItem> discountList;

        public Integer getRecId() {
            return recId;
        }

        public void setRecId(Integer recId) {
            this.recId = recId;
        }

        public Integer getPlatformId() {
            return platformId;
        }

        public void setPlatformId(Integer platformId) {
            this.platformId = platformId;
        }

        public String getTid() {
            return tid;
        }

        public void setTid(String tid) {
            this.tid = tid;
        }

        public Integer getShopId() {
            return shopId;
        }

        public void setShopId(Integer shopId) {
            this.shopId = shopId;
        }

        public String getShopNo() {
            return shopNo;
        }

        public void setShopNo(String shopNo) {
            this.shopNo = shopNo;
        }

        public Integer getProcessStatus() {
            return processStatus;
        }

        public void setProcessStatus(Integer processStatus) {
            this.processStatus = processStatus;
        }

        public Integer getTradeStatus() {
            return tradeStatus;
        }

        public void setTradeStatus(Integer tradeStatus) {
            this.tradeStatus = tradeStatus;
        }

        public Integer getPayStatus() {
            return payStatus;
        }

        public void setPayStatus(Integer payStatus) {
            this.payStatus = payStatus;
        }

        public Integer getPayMethod() {
            return payMethod;
        }

        public void setPayMethod(Integer payMethod) {
            this.payMethod = payMethod;
        }

        public Integer getRefundStatus() {
            return refundStatus;
        }

        public void setRefundStatus(Integer refundStatus) {
            this.refundStatus = refundStatus;
        }

        public String getBadReason() {
            return badReason;
        }

        public void setBadReason(String badReason) {
            this.badReason = badReason;
        }

        public BigDecimal getOrderCount() {
            return orderCount;
        }

        public void setOrderCount(BigDecimal orderCount) {
            this.orderCount = orderCount;
        }

        public BigDecimal getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(BigDecimal goodsCount) {
            this.goodsCount = goodsCount;
        }

        public String getTradeTime() {
            return tradeTime;
        }

        public void setTradeTime(String tradeTime) {
            this.tradeTime = tradeTime;
        }

        public String getPayTime() {
            return payTime;
        }

        public void setPayTime(String payTime) {
            this.payTime = payTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getBuyerMessage() {
            return buyerMessage;
        }

        public void setBuyerMessage(String buyerMessage) {
            this.buyerMessage = buyerMessage;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getBuyerNick() {
            return buyerNick;
        }

        public void setBuyerNick(String buyerNick) {
            this.buyerNick = buyerNick;
        }

        public String getBuyerEmail() {
            return buyerEmail;
        }

        public void setBuyerEmail(String buyerEmail) {
            this.buyerEmail = buyerEmail;
        }

        public String getBuyerName() {
            return buyerName;
        }

        public void setBuyerName(String buyerName) {
            this.buyerName = buyerName;
        }

        public String getBuyerArea() {
            return buyerArea;
        }

        public void setBuyerArea(String buyerArea) {
            this.buyerArea = buyerArea;
        }

        public String getPayId() {
            return payId;
        }

        public void setPayId(String payId) {
            this.payId = payId;
        }

        public String getPayAccount() {
            return payAccount;
        }

        public void setPayAccount(String payAccount) {
            this.payAccount = payAccount;
        }

        public String getReceiverName() {
            return receiverName;
        }

        public void setReceiverName(String receiverName) {
            this.receiverName = receiverName;
        }

        public Integer getReceiverCountry() {
            return receiverCountry;
        }

        public void setReceiverCountry(Integer receiverCountry) {
            this.receiverCountry = receiverCountry;
        }

        public Integer getReceiverProvince() {
            return receiverProvince;
        }

        public void setReceiverProvince(Integer receiverProvince) {
            this.receiverProvince = receiverProvince;
        }

        public Integer getReceiverCity() {
            return receiverCity;
        }

        public void setReceiverCity(Integer receiverCity) {
            this.receiverCity = receiverCity;
        }

        public Integer getReceiverDistrict() {
            return receiverDistrict;
        }

        public void setReceiverDistrict(Integer receiverDistrict) {
            this.receiverDistrict = receiverDistrict;
        }

        public String getReceiverAddress() {
            return receiverAddress;
        }

        public void setReceiverAddress(String receiverAddress) {
            this.receiverAddress = receiverAddress;
        }

        public String getReceiverMobile() {
            return receiverMobile;
        }

        public void setReceiverMobile(String receiverMobile) {
            this.receiverMobile = receiverMobile;
        }

        public String getReceiverTelno() {
            return receiverTelno;
        }

        public void setReceiverTelno(String receiverTelno) {
            this.receiverTelno = receiverTelno;
        }

        public String getReceiverArea() {
            return receiverArea;
        }

        public void setReceiverArea(String receiverArea) {
            this.receiverArea = receiverArea;
        }

        public String getReceiverRing() {
            return receiverRing;
        }

        public void setReceiverRing(String receiverRing) {
            this.receiverRing = receiverRing;
        }

        public String getToDeliverTime() {
            return toDeliverTime;
        }

        public void setToDeliverTime(String toDeliverTime) {
            this.toDeliverTime = toDeliverTime;
        }

        public BigDecimal getGoodsAmount() {
            return goodsAmount;
        }

        public void setGoodsAmount(BigDecimal goodsAmount) {
            this.goodsAmount = goodsAmount;
        }

        public BigDecimal getPostAmount() {
            return postAmount;
        }

        public void setPostAmount(BigDecimal postAmount) {
            this.postAmount = postAmount;
        }

        public BigDecimal getOtherAmount() {
            return otherAmount;
        }

        public void setOtherAmount(BigDecimal otherAmount) {
            this.otherAmount = otherAmount;
        }

        public BigDecimal getDiscount() {
            return discount;
        }

        public void setDiscount(BigDecimal discount) {
            this.discount = discount;
        }

        public BigDecimal getReceivable() {
            return receivable;
        }

        public void setReceivable(BigDecimal receivable) {
            this.receivable = receivable;
        }

        public BigDecimal getPaid() {
            return paid;
        }

        public void setPaid(BigDecimal paid) {
            this.paid = paid;
        }

        public BigDecimal getPlatformCost() {
            return platformCost;
        }

        public void setPlatformCost(BigDecimal platformCost) {
            this.platformCost = platformCost;
        }

        public String getWarehouseNo() {
            return warehouseNo;
        }

        public void setWarehouseNo(String warehouseNo) {
            this.warehouseNo = warehouseNo;
        }

        public boolean isAutoWms() {
            return isAutoWms;
        }

        public void setAutoWms(boolean autoWms) {
            isAutoWms = autoWms;
        }

        public String getModified() {
            return modified;
        }

        public void setModified(String modified) {
            this.modified = modified;
        }

        public String getCreated() {
            return created;
        }

        public void setCreated(String created) {
            this.created = created;
        }

        public String getOaid() {
            return oaid;
        }

        public void setOaid(String oaid) {
            this.oaid = oaid;
        }

        public Integer getDeliveryTerm() {
            return deliveryTerm;
        }

        public void setDeliveryTerm(Integer deliveryTerm) {
            this.deliveryTerm = deliveryTerm;
        }

        public Integer getGuaranteeMode() {
            return guaranteeMode;
        }

        public void setGuaranteeMode(Integer guaranteeMode) {
            this.guaranteeMode = guaranteeMode;
        }

        public BigDecimal getReceived() {
            return received;
        }

        public void setReceived(BigDecimal received) {
            this.received = received;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getFenxiaoNick() {
            return fenxiaoNick;
        }

        public void setFenxiaoNick(String fenxiaoNick) {
            this.fenxiaoNick = fenxiaoNick;
        }

        public byte getFenxiaoType() {
            return fenxiaoType;
        }

        public void setFenxiaoType(byte fenxiaoType) {
            this.fenxiaoType = fenxiaoType;
        }

        public String getReceiverZip() {
            return receiverZip;
        }

        public void setReceiverZip(String receiverZip) {
            this.receiverZip = receiverZip;
        }

        public String getPreChargeTime() {
            return preChargeTime;
        }

        public void setPreChargeTime(String preChargeTime) {
            this.preChargeTime = preChargeTime;
        }

        public BigDecimal getRefundAmount() {
            return refundAmount;
        }

        public void setRefundAmount(BigDecimal refundAmount) {
            this.refundAmount = refundAmount;
        }

        public byte getTradeFrom() {
            return tradeFrom;
        }

        public void setTradeFrom(byte tradeFrom) {
            this.tradeFrom = tradeFrom;
        }

        public List<OrderDetailItem> getTradeOrders() {
            return tradeOrders;
        }

        public void setTradeOrders(List<OrderDetailItem> tradeOrders) {
            this.tradeOrders = tradeOrders;
        }

        public List<OrderDiscountItem> getDiscountList() {
            return discountList;
        }

        public void setDiscountList(List<OrderDiscountItem> discountList) {
            this.discountList = discountList;
        }
    }

    public static class OrderDetailItem {
        private int recId;
        private String tid;
        private String oid;
        private String refundId;
        private int platformId;
        private int status;
        private int processStatus;
        private int refundStatus;
        private String goodsName;
        private String goodsNo;
        private String goodsId;
        private String specNo;
        private String specName;
        private String specId;
        private String specCode;
        private BigDecimal num;
        private BigDecimal price;
        private BigDecimal discount;
        private BigDecimal adjustAmount;
        private BigDecimal shareDiscount;
        private BigDecimal totalAmount;
        private BigDecimal shareAmount;
        private BigDecimal refundAmount;
        private String remark;
        private String modified;
        private String created;
        private String endTime;
        private String warehouseNo;
        private byte orderType;
        private String logisticsNo;
        private BigDecimal commission;
        private String cid;
        private String bindOid;

        public int getRecId() {
            return recId;
        }

        public void setRecId(int recId) {
            this.recId = recId;
        }

        public String getTid() {
            return tid;
        }

        public void setTid(String tid) {
            this.tid = tid;
        }

        public String getOid() {
            return oid;
        }

        public void setOid(String oid) {
            this.oid = oid;
        }

        public String getRefundId() {
            return refundId;
        }

        public void setRefundId(String refundId) {
            this.refundId = refundId;
        }

        public int getPlatformId() {
            return platformId;
        }

        public void setPlatformId(int platformId) {
            this.platformId = platformId;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getProcessStatus() {
            return processStatus;
        }

        public void setProcessStatus(int processStatus) {
            this.processStatus = processStatus;
        }

        public int getRefundStatus() {
            return refundStatus;
        }

        public void setRefundStatus(int refundStatus) {
            this.refundStatus = refundStatus;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsNo() {
            return goodsNo;
        }

        public void setGoodsNo(String goodsNo) {
            this.goodsNo = goodsNo;
        }

        public String getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(String goodsId) {
            this.goodsId = goodsId;
        }

        public String getSpecNo() {
            return specNo;
        }

        public void setSpecNo(String specNo) {
            this.specNo = specNo;
        }

        public String getSpecName() {
            return specName;
        }

        public void setSpecName(String specName) {
            this.specName = specName;
        }

        public String getSpecId() {
            return specId;
        }

        public void setSpecId(String specId) {
            this.specId = specId;
        }

        public String getSpecCode() {
            return specCode;
        }

        public void setSpecCode(String specCode) {
            this.specCode = specCode;
        }

        public BigDecimal getNum() {
            return num;
        }

        public void setNum(BigDecimal num) {
            this.num = num;
        }

        public BigDecimal getPrice() {
            return price;
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        public BigDecimal getDiscount() {
            return discount;
        }

        public void setDiscount(BigDecimal discount) {
            this.discount = discount;
        }

        public BigDecimal getAdjustAmount() {
            return adjustAmount;
        }

        public void setAdjustAmount(BigDecimal adjustAmount) {
            this.adjustAmount = adjustAmount;
        }

        public BigDecimal getShareDiscount() {
            return shareDiscount;
        }

        public void setShareDiscount(BigDecimal shareDiscount) {
            this.shareDiscount = shareDiscount;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public void setTotalAmount(BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
        }

        public BigDecimal getShareAmount() {
            return shareAmount;
        }

        public void setShareAmount(BigDecimal shareAmount) {
            this.shareAmount = shareAmount;
        }

        public BigDecimal getRefundAmount() {
            return refundAmount;
        }

        public void setRefundAmount(BigDecimal refundAmount) {
            this.refundAmount = refundAmount;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getModified() {
            return modified;
        }

        public void setModified(String modified) {
            this.modified = modified;
        }

        public String getCreated() {
            return created;
        }

        public void setCreated(String created) {
            this.created = created;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getWarehouseNo() {
            return warehouseNo;
        }

        public void setWarehouseNo(String warehouseNo) {
            this.warehouseNo = warehouseNo;
        }

        public byte getOrderType() {
            return orderType;
        }

        public void setOrderType(byte orderType) {
            this.orderType = orderType;
        }

        public String getLogisticsNo() {
            return logisticsNo;
        }

        public void setLogisticsNo(String logisticsNo) {
            this.logisticsNo = logisticsNo;
        }

        public BigDecimal getCommission() {
            return commission;
        }

        public void setCommission(BigDecimal commission) {
            this.commission = commission;
        }

        public String getCid() {
            return cid;
        }

        public void setCid(String cid) {
            this.cid = cid;
        }

        public String getBindOid() {
            return bindOid;
        }

        public void setBindOid(String bindOid) {
            this.bindOid = bindOid;
        }
    }

    public static class OrderDiscountItem {
        private String oid;
        private String name;
        private String detail;
        private BigDecimal amount;
        private String type;
        private Integer is_bonus;

        public String getOid() {
            return oid;
        }

        public void setOid(String oid) {
            this.oid = oid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDetail() {
            return detail;
        }

        public void setDetail(String detail) {
            this.detail = detail;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Integer getIs_bonus() {
            return is_bonus;
        }

        public void setIs_bonus(Integer is_bonus) {
            this.is_bonus = is_bonus;
        }
    }
}

