package com.cosfo.manage.provider.handler.status.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.config.OpenApiConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.provider.handler.status.StoreStatusHandler;
import com.cosfo.manage.provider.model.StoreParam;
import com.cosfo.ordercenter.client.common.FulfillmentTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.provider.DeliveryInfoQueryProvider;
import net.summerfarm.ofc.client.req.DeliveryDateQueryReq;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.xianmu.common.result.CommonResult;
import net.xianmu.robot.feishu.SignedFeishuBotUtil;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-12-21
 * @Description:自有编码推送
 */
@Slf4j
@Component
public class NingjiNotifyDeliveryHandler implements StoreStatusHandler {

    // 日配0 非日配1
    private static final Integer isEveryDayFlag = 0;


    @Resource
    private OpenApiConfig openApiConfig;


    @DubboReference
    private DeliveryInfoQueryProvider deliveryInfoQueryProvider;

    @Override
    public boolean support(Long tenantId) {
        return openApiConfig.getOpenApiNingJiTenantIds().contains(tenantId);
    }

    @Override
    public void builderOrderDeliveringDTO(StoreParam storeParam) {
        MerchantStoreCommandReq merchantStoreCommandReq = storeParam.getMerchantStoreCommandReq();
        MerchantAddressCommandReq merchantAddressCommandReq = storeParam.getNewAddress();
        if (Objects.nonNull(merchantStoreCommandReq) && Objects.nonNull(merchantAddressCommandReq)) {
            if (checkIsEverydayDelivery(merchantStoreCommandReq.getTenantId(), merchantAddressCommandReq.getCity(), merchantAddressCommandReq.getArea())) {
                merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
            } else {
                merchantStoreCommandReq.setStatus(MerchantStoreEnum.Status.CLOSE.getCode());
            }

        }
    }

    @Override
    public void asyncExecuteNotify(StoreParam storeParam) {
        MerchantStoreCommandReq merchantStoreCommandReq = storeParam.getMerchantStoreCommandReq();
        MerchantAddressCommandReq address = storeParam.getNewAddress();
        if (merchantStoreCommandReq == null || address == null) {
            return;
        }
        String auditMsg = "";
        // 待审核发送飞书群告警消息
        if (MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode().equals(merchantStoreCommandReq.getStatus())) {
            auditMsg = "门店地址是日配区，自动审核通过";
        } else if (MerchantStoreEnum.Status.CLOSE.getCode().equals(merchantStoreCommandReq.getStatus())) {
            auditMsg = "门店地址不在配送区域或是非日配区，自动关闭门店";
        } else if (MerchantStoreEnum.Status.IN_AUDIT.getCode().equals(merchantStoreCommandReq.getStatus())) {
            auditMsg = "门店处于待审核状态，请更新该门店与城配仓绑定关系";
        }

        StringBuilder text = new StringBuilder();
        text.append("门店自动审核提醒").append(Constants.LINE).append("租户ID：").append(merchantStoreCommandReq.getTenantId()).append(Constants.LINE).append("门店ID：").append(merchantStoreCommandReq.getId())
                .append(Constants.COMMA).append("门店编号：").append(merchantStoreCommandReq.getStoreNo()).append(Constants.COMMA).append("门店名称：").append(merchantStoreCommandReq.getStoreName())
                .append(Constants.LINE).append("门店地址：").append(address.getProvince()).append(address.getCity()).append(address.getArea()).append(address.getAddress())
                .append(Constants.LINE).append(auditMsg);

        log.info("发送飞书群告警消息,text:{}", text.toString());
        CommonResult<Boolean> result = SignedFeishuBotUtil.sendTextMsgAndAtAll(openApiConfig.getOpenApiWarnUrl(), text.toString(), openApiConfig.getOpenApiWarnUrlSign());
        log.info("发送飞书群告警消息结果,text:{}", JSON.toJSONString(result));
    }


    /**
     * 门店地址是否日配区域
     *
     * @param tenantId
     * @param city
     * @param area
     * @return
     */
    private boolean checkIsEverydayDelivery(Long tenantId, String city, String area) {
        try {
            DeliveryDateQueryReq req = new DeliveryDateQueryReq();
            req.setSource(OfcOrderSourceEnum.SAAS_MALL);
            req.setTenantId(tenantId);
            req.setCity(city);
            req.setArea(area);
            req.setPayTime(LocalDateTime.now());
            DeliveryDateQueryResp deliveryDateQueryResp = RpcResultUtil.handle(deliveryInfoQueryProvider.queryDeliveryDate(req));
            if (deliveryDateQueryResp == null) {
                log.error("获取配送信息为空 req={}", JSON.toJSONString(req));
                return false;
            }

            return isEveryDayFlag.equals(deliveryDateQueryResp.getIsEveryDayFlag())
                    && FulfillmentTypeEnum.CITY_DELIVERY.getValue().equals(deliveryDateQueryResp.getFulfillmentType());

        } catch (Exception e) {
            log.warn("获取配送信息异常. ", e);
        }
        return false;
    }

}
