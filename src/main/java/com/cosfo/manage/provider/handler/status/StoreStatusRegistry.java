package com.cosfo.manage.provider.handler.status;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-12-21
 * @Description:
 */
@Component
public class StoreStatusRegistry {

    @Autowired
    private List<StoreStatusHandler> storeStatusHandlers;

    public StoreStatusHandler getHandlerByTenantId(Long tenantId) {
        for (StoreStatusHandler storeStatusHandler : storeStatusHandlers) {
            if (storeStatusHandler.support(tenantId)) {
                return storeStatusHandler;
            }
        }
        return null;
    }
}
