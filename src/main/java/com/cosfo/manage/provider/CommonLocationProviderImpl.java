//package com.cosfo.manage.provider;
//
//import com.cosfo.manage.client.common.location.CommonLocationProvider;
//import com.cosfo.manage.client.common.location.resp.CommonLocationCityResp;
//import com.cosfo.manage.common.mapper.CommonLocationCityMapper;
//import com.cosfo.manage.provider.convert.CommonLocationConvert;
//import com.cosfo.manage.system.model.dto.CommonLocationCityDTO;
//import lombok.extern.slf4j.Slf4j;
//import net.xianmu.common.result.DubboResponse;
//import org.apache.dubbo.config.annotation.DubboService;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * @Author: fansongsong
// * @Date: 2024-03-20
// * @Description:
// */
//@DubboService
//@Component
//@Slf4j
//public class CommonLocationProviderImpl implements CommonLocationProvider {
//
//    @Resource
//    private CommonLocationCityMapper commonLocationCityMapper;
//
//    @Override
//    public DubboResponse<List<CommonLocationCityResp>> queryAllCommonLocationCity() {
//        List<CommonLocationCityDTO> commonLocationCityDTOS = commonLocationCityMapper.queryAllCommonLocationCity();
//        return DubboResponse.getOK(CommonLocationConvert.INSTANCE.cityList2RespList(commonLocationCityDTOS));
//    }
//}
