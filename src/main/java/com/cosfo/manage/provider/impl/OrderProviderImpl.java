package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.order.OrderProvider;
import com.cosfo.manage.client.order.req.OrderReq;
import com.cosfo.manage.client.order.req.OrderSelfLiftingCommonReq;
import com.cosfo.manage.client.order.resp.OrderResp;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.order.model.dto.OrderDTO;
import com.cosfo.manage.order.model.dto.OrderQueryDTO;
import com.cosfo.manage.order.model.dto.OrderSelfLiftingDTO;
import com.cosfo.manage.order.model.vo.OrderVO;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.provider.convert.OrderConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@DubboService
@Component
@Slf4j
public class OrderProviderImpl implements OrderProvider {

    @Resource
    private OrderBusinessService orderBusinessService;
    @Override
    public DubboResponse<Void> selfLifting(OrderSelfLiftingCommonReq req) {
        ResultDTO resultDTO = orderBusinessService.querySelfLifting(req.getOrderNo(), req.getTenantId());
        List<OrderSelfLiftingDTO> orderSelfLiftingDTOS = (List<OrderSelfLiftingDTO>) resultDTO.getData();
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setOrderNo(req.getOrderNo());
        orderDTO.setTenantId(req.getTenantId());
        List<OrderSelfLiftingDTO> orderSelfLiftingDTOs = orderSelfLiftingDTOS.stream().map(e -> {
            OrderSelfLiftingDTO orderSelfLiftingDTO = new OrderSelfLiftingDTO();
            orderSelfLiftingDTO.setAddress(e.getAddress());
            // OFC发起的自提，预计自提时间取当前操作时间
            orderSelfLiftingDTO.setExpectTime(LocalDateTime.now());
            orderSelfLiftingDTO.setWarehouseNo(e.getWarehouseNo());
            return orderSelfLiftingDTO;
        }).collect(Collectors.toList());
        orderDTO.setOrderSelfLiftingDTOS(orderSelfLiftingDTOs);
        ResultDTO result = orderBusinessService.selfLifting(orderDTO, Boolean.FALSE);
        if(ResultDTOEnum.SUCCESS.getCode().equals(result.getCode())){
            return DubboResponse.getOK();
        }else{
            return DubboResponse.getError(String.valueOf(result.getCode()),result.getMessage());
        }

    }

    @Override
    public DubboResponse<OrderResp> queryOrderInfo(OrderReq orderReq) {
        OrderQueryDTO orderQueryDTO = OrderConvert.convertToOrderQueryDTO(orderReq);
        CommonResult<OrderVO> result = orderBusinessService.queryOrderInfoToOtherService(orderQueryDTO);
        if(ResultStatusEnum.OK.getStatus().equals(result.getStatus())){
            OrderVO orderVO = result.getData();
            OrderResp orderResp = OrderConvert.convertToOrderResp(orderVO);
            return DubboResponse.getOK(orderResp);
        }else{
            return DubboResponse.getError(String.valueOf(result.getStatus()), result.getMsg());
        }
    }
}