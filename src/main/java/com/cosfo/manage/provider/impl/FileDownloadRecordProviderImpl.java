package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.file.FileDownloadRecordProvider;
import com.cosfo.manage.client.file.req.FileDownloadRecordAddReq;
import com.cosfo.manage.client.file.req.FileDownloadRecordUpdateReq;
import com.cosfo.manage.file.model.po.FileDownloadRecord;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/26
 */
@DubboService
@Component
@Slf4j
public class FileDownloadRecordProviderImpl implements FileDownloadRecordProvider {

    @Resource
    private FileDownloadRecordService fileDownloadRecordService;

    @Override
    public DubboResponse<Long> save(FileDownloadRecordAddReq fileDownloadRecordAddReq) {
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setTenantId(fileDownloadRecordAddReq.getTenantId());
        fileDownloadRecord.setParams(fileDownloadRecordAddReq.getParams());
        fileDownloadRecord.setStatus(fileDownloadRecordAddReq.getStatus());
        fileDownloadRecord.setType(fileDownloadRecordAddReq.getType());
        Long id = fileDownloadRecordService.generateFileDownloadRecord(fileDownloadRecord);
        return DubboResponse.getOK(id);
    }

    @Override
    public DubboResponse<Boolean> update(FileDownloadRecordUpdateReq fileDownloadRecordUpdateReq) {
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setId(fileDownloadRecordUpdateReq.getId());
        fileDownloadRecord.setStatus(fileDownloadRecordUpdateReq.getStatus());
        fileDownloadRecord.setUrl(fileDownloadRecordUpdateReq.getUrl());
        fileDownloadRecordService.updateSelectiveByPrimaryKey(fileDownloadRecord);
        return DubboResponse.getOK(Boolean.TRUE);
    }
}
