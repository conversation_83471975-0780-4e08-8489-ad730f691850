package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.page.PageResp;
import com.cosfo.manage.client.product.ProductProvider;
import com.cosfo.manage.client.product.req.ProductCategoryIdQueryReq;
import com.cosfo.manage.client.product.req.ProductQueryReq;
import com.cosfo.manage.client.product.req.QuerySupplyReq;
import com.cosfo.manage.client.product.req.SaasSkuMappingQueryReq;
import com.cosfo.manage.client.product.req.SummerFarmSynchronizedSkuReq;
import com.cosfo.manage.client.product.req.UpdateAssociatedReq;
import com.cosfo.manage.client.product.resp.ProductDetailInfoResp;
import com.cosfo.manage.client.product.resp.ProductInfoResp;
import com.cosfo.manage.client.product.resp.ProductPricingSupplyCityMappingResp;
import com.cosfo.manage.client.product.resp.ProductSkuCodeResp;
import com.cosfo.manage.client.product.resp.ProductSkuResp;
import com.cosfo.manage.client.product.resp.SummerFarmSynchronizedSkuResp;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.good.convert.ProductInfoConvert;
import com.cosfo.manage.product.convert.ProductConverter;
import com.cosfo.manage.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.manage.product.model.dto.ProductAgentSkuQueryDTO;
import com.cosfo.manage.product.model.dto.ProductSkuDTO;
import com.cosfo.manage.product.model.dto.SummerFarmSynchronizedSkuDTO;
import com.cosfo.manage.product.model.po.ProductAgentSkuMapping;
import com.cosfo.manage.product.model.vo.CategoryVO;
import com.cosfo.manage.product.model.vo.ProductAgentSkuVO;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;
import com.cosfo.manage.product.model.vo.SummerFarmSynchronizedSkuVO;
import com.cosfo.manage.product.service.ProductAgentSkuMappingService;
import com.cosfo.manage.product.service.ProductCategoryService;
import com.cosfo.manage.product.service.ProductPricingSupplyService;
import com.cosfo.manage.product.service.ProductSkuService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 商品 provider
 * @date 2023/1/30 11:34
 */
@DubboService
@Component
@Slf4j
public class ProductProviderImpl implements ProductProvider {

    @Resource
    private ProductAgentSkuMappingService productAgentSkuMappingService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ProductPricingSupplyService productPricingSupplyService;
    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;

    @Override
    @Deprecated
    public DubboResponse<ProductInfoResp> batchQueryBySummerfarmSkuIds(ProductQueryReq req) {
        List<Long> summerfarmSkuIds = req.getSummerfarmSkuIds();
        if (CollectionUtils.isEmpty(summerfarmSkuIds)) {
            return DubboResponse.getOK();
        }
        List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingService.batchQueryByAgentSkuId(summerfarmSkuIds);
        if (CollectionUtils.isEmpty(productAgentSkuMappings)) {
            return DubboResponse.getOK();
        }
        productAgentSkuMappings = productAgentSkuMappings.stream().filter(e -> !XianmuSupplyTenant.TENANT_ID.equals(e.getTenantId())).collect(Collectors.toList());
        Map<Long, Long> agentSkuMapping = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductAgentSkuMapping::getAgentSkuId, ProductAgentSkuMapping::getSkuId));
        ProductInfoResp resp = new ProductInfoResp();
        resp.setSkuMappingMap(agentSkuMapping);
        resp.setDetailInfoRespMap(buildDetailInfo(productAgentSkuMappings.stream().map(ProductAgentSkuMapping::getSkuId).collect(Collectors.toList())));
        return DubboResponse.getOK(resp);
    }

    private Map<Long, ProductDetailInfoResp> buildDetailInfo(List<Long> skuIds){
        // 查询帆台对应的税率以及类目
        List<ProductSkuDTO> productSkuDtoS = productSkuService.querySupplySkuInfo(skuIds);
        if (CollectionUtils.isEmpty(productSkuDtoS)) {
            return Collections.emptyMap();
        }

        return productSkuDtoS.stream().collect(Collectors.toMap(ProductSkuDTO::getId, item -> {
            ProductDetailInfoResp productDetailInfoResp = new ProductDetailInfoResp();
            productDetailInfoResp.setCategoryId(item.getCategoryId());
            productDetailInfoResp.setTaxRateValue(item.getTaxRateValue());
            productDetailInfoResp.setCustomSkuCode(item.getCustomSkuCode());
            return productDetailInfoResp;
        }));
    }

    @Override
    @Deprecated
    public DubboResponse<ProductInfoResp> batchQueryBySaasSkuIds(SaasSkuMappingQueryReq saasSkuMappingQueryReq) {
        log.info("根据saas skuId获取鲜沐skuId, req = {}", saasSkuMappingQueryReq);
        if (CollectionUtils.isEmpty(saasSkuMappingQueryReq.getSkuIds())) {
            return DubboResponse.getOK();
        }
        List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingService.batchQueryBySaasSkuIds(saasSkuMappingQueryReq.getSkuIds());
        if (CollectionUtils.isEmpty(productAgentSkuMappings)) {
            return DubboResponse.getOK();
        }
        Map<Long, Long> skuIdsMap = productAgentSkuMappings.stream().collect(Collectors.toMap(ProductAgentSkuMapping::getSkuId, ProductAgentSkuMapping::getAgentSkuId));
        ProductInfoResp resp = new ProductInfoResp();
        resp.setSkuMappingMap(skuIdsMap);
        resp.setDetailInfoRespMap(buildDetailInfo(productAgentSkuMappings.stream().map(ProductAgentSkuMapping::getSkuId).collect(Collectors.toList())));
        return DubboResponse.getOK(resp);
    }

    @Override
    @Deprecated
    public DubboResponse<ProductSkuResp> querySkuInfo(ProductQueryReq productQueryReq) {
        ProductSkuDTO productSkuDTO = productSkuService.querySkuInfo(productQueryReq.getSkuId());
        return DubboResponse.getOK(ProductInfoConvert.INSTANCE.convert2SkuResp(productSkuDTO));
    }

    @Override
    @Deprecated
    public DubboResponse<List<ProductSkuResp>> querySkuInfoList(ProductQueryReq req) {
        List<ProductSkuDTO> productSkuList = productSkuService.querySupplySkuInfo(req.getSkuIds());
        return DubboResponse.getOK(ProductInfoConvert.INSTANCE.convert2SkuRespList(productSkuList));
    }

    @Override
    public DubboResponse<Boolean> updateAssociated(UpdateAssociatedReq req) {
        if (Objects.isNull(req.getSkuId()) || Objects.isNull(req.getTenantId()) || Objects.isNull(req.getAssociated())) {
            return DubboResponse.getDefaultError("参数不可为空！");
        }
        productSkuService.updateAssociated(req.getSkuId(), req.getTenantId(), req.getAssociated());
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<Boolean> updateSupplyAssociated(UpdateAssociatedReq req) {
        if (Objects.isNull(req.getSkuId()) || Objects.isNull(req.getTenantId()) || Objects.isNull(req.getAssociated())) {
            return DubboResponse.getDefaultError("参数不可为空！");
        }
        productPricingSupplyService.updateAssociated(req.getSkuId(), req.getTenantId(), req.getAssociated());
        return DubboResponse.getOK(Boolean.TRUE);
    }

    /**
     * 同步货品
     *
     * @param summerFarmSynchronizedSkuReq
     * @return
     */
    @Override
    public DubboResponse<SummerFarmSynchronizedSkuResp> doneSkuSynchronized(SummerFarmSynchronizedSkuReq summerFarmSynchronizedSkuReq) {
        SummerFarmSynchronizedSkuDTO summerFarmSynchronizedSkuDTO = ProductConverter.convertToSummerFarmSynchronized(summerFarmSynchronizedSkuReq);
        SummerFarmSynchronizedSkuVO summerFarmSynchronizedSkuVO = productSkuService.doneSkuSynchronized(summerFarmSynchronizedSkuDTO, XianmuSupplyTenant.TENANT_ID);
        SummerFarmSynchronizedSkuResp summerFarmSynchronizedSkuResp = ProductConverter.convertToSummerFarmSynchronizedSkuResp(summerFarmSynchronizedSkuVO);
        return DubboResponse.getOK(summerFarmSynchronizedSkuResp);
    }

    @Override
    public DubboResponse<List<ProductPricingSupplyCityMappingResp>> querySupplyCityBySkuId(QuerySupplyReq req) {
        if (Objects.isNull(req) || Objects.isNull(req.getTenantId())) {
            return DubboResponse.getDefaultError("参数不可为空！");
        }
        List<Long> skuIds = new ArrayList<>();
        if (Objects.nonNull(req.getSkuId())){
            skuIds.add(req.getSkuId());
        }
        if (!CollectionUtils.isEmpty(req.getSkuIds())){
            skuIds.addAll(req.getSkuIds());
        }
        if (CollectionUtils.isEmpty(skuIds)){
            return DubboResponse.getDefaultError("sku不可为空");
        }
        List<ProductPricingSupplyCityMappingDTO> mappingDTOS = productPricingSupplyService.querySupplyCityBySkuId(skuIds, req.getTenantId());

        return DubboResponse.getOK(ProductInfoConvert.INSTANCE.convert2MappingResp(mappingDTOS));
    }

    @Deprecated
    @Override
    public DubboResponse<PageResp<ProductSkuCodeResp>> queryProductBySaasCategoryId(ProductCategoryIdQueryReq req) {
        Long tenantId = req.getTenantId();
        Long categoryId = req.getCategoryId();
        Integer pageIndex = req.getPageIndex() == null ? 1 : req.getPageIndex();
        Integer pageSize = req.getPageSize() == null ? 100 : req.getPageSize();

        if (tenantId == null || categoryId == null) {
            return DubboResponse.getOK(PageResp.emptyPage(pageIndex, pageSize));
        }

        ProductAgentSkuQueryDTO dto = new ProductAgentSkuQueryDTO();
        dto.setTenantId(tenantId);
        // 查询类目集合
        if (categoryId != null) {
            List<CategoryVO> categoryVOS = productCategoryService.queryChildCategoryList(categoryId);
            List<Long> categoryIds = categoryVOS.stream().map(CategoryVO::getId).collect(Collectors.toList());
            dto.setCategoryIds(categoryIds);
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<ProductAgentSkuVO> productAgentSkuVOs = productAgentSkuMappingMapper.listAll(dto);
        if (CollectionUtils.isEmpty(productAgentSkuVOs)) {
            return DubboResponse.getOK(PageResp.emptyPage(pageIndex, pageSize));
        }
        PageInfo<ProductAgentSkuVO> pageInfo = new PageInfo<>(productAgentSkuVOs);
        List<ProductSkuCodeResp> respList = productAgentSkuVOs.stream().map(e -> {
            ProductSkuCodeResp productSkuResp = new ProductSkuCodeResp();
            productSkuResp.setSkuId(e.getSkuId());
            productSkuResp.setTenantId(tenantId);
            productSkuResp.setAgentTenantId(e.getSupplyTenantId());
            productSkuResp.setAgentSkuId(e.getSupplySkuId());
            productSkuResp.setAgentSkuCode(e.getSupplySkuCode());
            return productSkuResp;
        }).collect(Collectors.toList());

        return DubboResponse.getOK(PageResp.toPageList(respList, Math.toIntExact(pageInfo.getTotal()), pageIndex, pageSize));
    }
}
