package com.cosfo.manage.provider.impl;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.manage.client.enums.CommonExportFormatEnum;
import com.cosfo.manage.client.enums.ItemPriceRuleEnum;
import com.cosfo.manage.client.enums.PurchaseOrderExportFormatEnum;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantBaseResp;
import com.cosfo.manage.client.tenant.resp.TenantInfoResp;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.cosfo.manage.common.context.FlowRuleAuditBizTypeEnum;
import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.context.TenantStoreConfigEnum;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.provider.convert.TenantConvert;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.model.dto.TenantInfoDTO;
import com.cosfo.manage.tenant.model.dto.TenantQueryDTO;
import com.cosfo.manage.tenant.model.vo.TenantCommonConfigVO;
import com.cosfo.manage.tenant.model.vo.TenantStoreConfigVO;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.cosfo.manage.tenant.service.TenantFlowSchemeService;
import com.cosfo.manage.tenant.service.TenantService;
import com.cosfo.manage.tenant.service.TenantStoreConfigService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/26
 */
@DubboService
@Component
@Slf4j
public class TenantProviderImpl implements TenantProvider {

    @Resource
    private TenantService tenantService;
    @Resource
    private MerchantStoreService storeService;
    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private TenantFlowSchemeService tenantFlowSchemeService;
    @Resource
    private TenantStoreConfigService tenantStoreConfigService;

    @Override
    public DubboResponse<TenantInfoResp> getTenantInfo(String token) {
        TenantInfoDTO tenantInfo = tenantService.getTenantInfo(token);
        TenantInfoResp tenantInfoResp = TenantConvert.convertToTenantInfoResp(tenantInfo);
        return DubboResponse.getOK(tenantInfoResp);
    }

    @Override
    public DubboResponse<List<TenantResp>> list(TenantQueryReq tenantQueryReq) {
        TenantQueryDTO tenantQueryDTO = TenantConvert.convertToTenantQueryDTO(tenantQueryReq);
        List<TenantDTO> tenantDTOS = tenantService.list(tenantQueryDTO);
        if (CollectionUtils.isEmpty(tenantDTOS)) {
            return DubboResponse.getOK(new ArrayList<>());
        }

        List<TenantResp> tenantResps = tenantDTOS.stream().map(TenantConvert::convertToTenantResp).collect(Collectors.toList());
        return DubboResponse.getOK(tenantResps);
    }

    @Override
    public DubboResponse<List<String>> listAddress(Long tenantId) {
        return DubboResponse.getOK(storeService.listAddress(tenantId));
    }

    @Override
    public DubboResponse<Boolean> getAfterSaleApproveSwitch4haveWarehouse(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfigEnum.TenantConfig.SELF_WAREHOUSE_AFTER_SALE_APPROVAL_RULE.getConfigKey());
        if (ObjectUtil.isNull(tenantCommonConfigVO)) {
            return DubboResponse.getOK(Boolean.FALSE);
        }
        return DubboResponse.getOK(TenantConfigEnum.AfterSaleApprovalRule.NEED_AUDIT.getCode().equals(tenantCommonConfigVO.getConfigValue()));
    }

    @Override
    public DubboResponse<Boolean> getAfterSaleApproveSwitch4nonWarehouse(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfigEnum.TenantConfig.NO_WAREHOUSE_AFTER_SALE_APPROVAL_RULE.getConfigKey());
        if (ObjectUtil.isNull(tenantCommonConfigVO)) {
            return DubboResponse.getOK(Boolean.FALSE);
        }
        return DubboResponse.getOK(TenantConfigEnum.AfterSaleApprovalRule.NEED_AUDIT.getCode().equals(tenantCommonConfigVO.getConfigValue()));
    }

    @Override
    public DubboResponse<Boolean> getAutoOutboundTaskSwitch4Order(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfigEnum.TenantConfig.AUTO_OUTBOUND_TASK4ORDER_SWITCH.getConfigKey());
        if (ObjectUtil.isNull(tenantCommonConfigVO)) {
            return DubboResponse.getOK(Boolean.FALSE);
        }
        return DubboResponse.getOK("1".equals(tenantCommonConfigVO.getConfigValue()));
    }
    @Override
    @Deprecated
    public DubboResponse<ItemPriceRuleEnum> getItemPriceRule(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig (tenantId, TenantConfigEnum.TenantConfig.ITEM_PRICE_RULE.getConfigKey () );
        if(ObjectUtil.isNull (tenantCommonConfigVO)){
            return DubboResponse.getOK(ItemPriceRuleEnum.USE_COST_PRICE);
        }
        return DubboResponse.getOK(ItemPriceRuleEnum.getRule (tenantCommonConfigVO.getConfigValue ()));
    }

    @Override
    public DubboResponse<Boolean> getOrderAuditSwitch(Long storeId) {
        boolean flag = tenantFlowSchemeService.getAuditSwitchByStoreIdAndBizType(storeId, FlowRuleAuditBizTypeEnum.ORDER_AUDIT);
        return DubboResponse.getOK(flag);
    }


    @Override
    public DubboResponse<Boolean> getAfterSaleAuditSwitch(Long storeId, Integer flowRuleAuditBizType) {
        FlowRuleAuditBizTypeEnum bizTypeEnum = FlowRuleAuditBizTypeEnum.getByType(flowRuleAuditBizType);
        if(bizTypeEnum == null){
            throw new BizException("不支持的审核业务类型");
        }
        boolean flag = tenantFlowSchemeService.getAuditSwitchByStoreIdAndBizType(storeId, bizTypeEnum);
        return DubboResponse.getOK(flag);
    }

    @Override
    public DubboResponse<Boolean> getSaveWorrySwitch(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig (tenantId, TenantConfigEnum.TenantConfig.SAVE_WORRY.getConfigKey ());
        return DubboResponse.getOK(Integer.valueOf (tenantCommonConfigVO.getConfigValue ())==1);
    }

    @Override
    public DubboResponse<Boolean> initDefaultFlowScheme(Long tenantId) {
        tenantFlowSchemeService.initDefaultFlowScheme(tenantId);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<TenantBaseResp> getTenantBaseInfo(Long tenantId) {
        TenantDTO tenantDTO = tenantService.getTenantBaseInfo(tenantId);
        TenantBaseResp tenantBaseResp = TenantConvert.convertToTenantBaseResp(tenantDTO);
        return DubboResponse.getOK(tenantBaseResp);
    }

    @Override
    public DubboResponse<Boolean> getStoreInventorySwitch(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig (tenantId, TenantConfigEnum.TenantConfig.STORE_INVENTORY_SWITCH.getConfigKey ());
        return DubboResponse.getOK(Integer.valueOf (tenantCommonConfigVO.getConfigValue ())==1);
    }

    @Override
    public DubboResponse<PurchaseOrderExportFormatEnum> getPurchaseOrderExportFormat(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfigEnum.TenantConfig.PURCHASE_ORDER_EXPORT_FORMAT.getConfigKey());
        if(ObjectUtil.isNull (tenantCommonConfigVO)){
            return DubboResponse.getOK(PurchaseOrderExportFormatEnum.STANDARD_FORMAT);
        }
        return DubboResponse.getOK(PurchaseOrderExportFormatEnum.getFormat(tenantCommonConfigVO.getConfigValue()));
    }

    @Override
    public DubboResponse<CommonExportFormatEnum> getBizCommonExportFormat(Long tenantId, String configKey) {
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, configKey);
        if(ObjectUtil.isNull (tenantCommonConfigVO)){
            return DubboResponse.getOK(CommonExportFormatEnum.STANDARD_FORMAT);
        }
        return DubboResponse.getOK(CommonExportFormatEnum.getFormat(tenantCommonConfigVO.getConfigValue()));
    }

    @Override
    public DubboResponse<Map<Long, Boolean>> queryDeliveryNotePrintPriceSwitch(List<Long> storeIdList) {
        if(CollectionUtils.isEmpty(storeIdList)){
            return DubboResponse.getOK(Collections.emptyMap());
        }

        List<TenantStoreConfigVO> tenantStoreConfigVOS = tenantStoreConfigService.queryTenantStoreConfigList(storeIdList, TenantStoreConfigEnum.StoreConfig.DELIVERY_NOTE_PRINT_PRICE.getConfigKey());

        Map<Long, Boolean> storeConfigMap = tenantStoreConfigVOS.stream().collect(Collectors.toMap(TenantStoreConfigVO::getStoreId, e -> TenantStoreConfigEnum.SwitchEnum.OPEN.getCode().equals(e.getConfigValue()), (v1, v2) -> v1));
        return DubboResponse.getOK(storeConfigMap);
    }

    @Override
    public DubboResponse<BigDecimal> getOrderTaxRate(Long tenantId) {
        TenantCommonConfigVO tenantCommonConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfigEnum.TenantConfig.ORDER_TAX_RATE.getConfigKey());
        if(ObjectUtil.isNull (tenantCommonConfigVO)){
            return DubboResponse.getOK(BigDecimal.ZERO);
        }
        return DubboResponse.getOK(new BigDecimal(tenantCommonConfigVO.getConfigValue()));
    }

}
