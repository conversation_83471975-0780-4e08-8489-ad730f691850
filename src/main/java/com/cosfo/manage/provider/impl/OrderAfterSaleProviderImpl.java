package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.order.aftersale.OrderAfterSaleProvider;
import com.cosfo.manage.client.order.req.OrderAfterSaleSelfReviewAgentReq;
import com.cosfo.manage.common.config.OrderAfterSaleAgentSelfReviewConfig;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-12-26
 **/
@Slf4j
@Component
@DubboService
public class OrderAfterSaleProviderImpl implements OrderAfterSaleProvider {

    @Resource
    private OrderAfterSaleAgentSelfReviewConfig orderAfterSaleAgentSelfReviewConfig;

    @Override
    public DubboResponse<Boolean> needSelfReviewFlag(OrderAfterSaleSelfReviewAgentReq req) {
        return DubboResponse.getOK(orderAfterSaleAgentSelfReviewConfig.getAgentAfterSaleSelfReviewFlag(req.getTenantId()));
    }
}
